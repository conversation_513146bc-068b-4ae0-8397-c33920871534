任务背景与目标
您正在为上海联劝公益基金会担任数字化转型项目负责人，当前需要为新上线的Boss3业务管理系统（http://csboss.lianquan.org.cn）制作一份专业的用户使用手册。该手册将作为团队全员的标准操作指南，旨在提升工作效率、确保操作规范性，并降低系统学习成本。
角色定义
您是一位具备以下能力的数字化项目专家：

熟练掌握Python自动化测试工具（Playwright等）
具备技术文档撰写和用户体验设计能力
了解公益机构业务流程和管理需求
能够将复杂技术功能转化为易懂的操作指南

具体执行要求
技术实施约束：

必须在虚拟环境中进行所有Python包安装和操作
严格执行只读模式：禁止提交任何新表单或修改现有数据
使用Playwright或同等自动化工具进行系统功能遍历
确保访问过程中的数据安全和系统稳定性

内容覆盖要求：

遍历所有可访问的功能菜单和子模块
记录每个功能的操作流程、界面布局和关键要素
整合现有的"功能列表及反馈列表.xlsx"中的信息
识别常见问题和最佳实践

输入输出规范
系统访问信息：

系统地址：http://csboss.lianquan.org.cn
测试账号：wangjinxiao
访问密码：Ok756756@

参考资料：

本目录下的"功能列表及反馈列表.xlsx"文件

输出格式要求：

Markdown版本：

层次清晰的目录结构
包含截图和操作步骤说明
支持跨平台查看和编辑


网页版本：

响应式设计，适配不同设备
可导航的侧边栏目录
内嵌图片和交互式元素



执行步骤流程
第一阶段：环境准备

创建并激活Python虚拟环境
安装Playwright及相关依赖包
配置浏览器驱动和测试环境
验证系统访问权限和连接稳定性

第二阶段：系统探索

登录Boss3系统并验证账号权限
系统性遍历所有主菜单和子菜单
记录每个功能模块的页面结构和操作要素
截取关键界面和操作流程图片
识别权限控制和访问限制

第三阶段：内容整理

分析"功能列表及反馈列表.xlsx"中的业务逻辑
将自动化收集的信息与业务需求对应
构建用户友好的功能分类体系
编写操作步骤和注意事项

第四阶段：文档生成

编写Markdown格式的使用手册
转换为HTML网页格式
优化排版和用户体验
添加搜索和导航功能

质量标准
内容完整性：

覆盖率达到95%以上的可访问功能
每个功能模块至少包含3个关键操作说明
包含不少于50个实际操作截图

用户体验标准：

新用户能在10分钟内找到目标功能
操作步骤清晰，无歧义表达
提供故障排除和常见问题解答

技术质量要求：

Markdown文档符合标准语法规范
网页版本在主流浏览器中兼容性良好
图片清晰度不低于1080p，文件大小合理

示例参考
功能模块文档结构示例：
markdown## 2.1 项目管理模块
### 功能概述
[模块用途和主要功能说明]
### 访问路径
导航菜单 → 项目管理 → 项目列表
### 操作步骤
1. 点击"项目管理"主菜单
2. 选择"项目列表"子菜单
3. [详细操作步骤...]
### 界面说明
[截图 + 关键元素标注]
### 注意事项
- 权限要求：需要项目管理权限
- 数据更新：实时同步
特殊要求
安全注意事项：

严禁在文档中明文显示真实密码
操作过程中避免触发敏感业务流程
确保个人信息和业务数据的隐私保护

边界条件处理：

遇到权限限制时记录并标注
对于需要特殊权限的功能进行分类说明
处理系统响应异常或加载失败的情况

版本管理要求：

文档需包含创建日期和版本号
预留后续功能更新的扩展空间
建立文档维护和更新机制

交付标准：

提供完整的项目文件夹，包含源码、文档和资源文件
生成的手册能够独立部署和分发
包含详细的环境配置和使用说明