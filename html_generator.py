#!/usr/bin/env python3
"""
HTML网页版使用手册生成器
将Markdown手册转换为响应式HTML网页
"""

import json
import markdown
from pathlib import Path
from datetime import datetime

class HTMLManualGenerator:
    def __init__(self):
        self.manual_dir = Path("manual")
        self.data_dir = Path("data")
        self.screenshots_dir = Path("screenshots")
        self.html_dir = Path("html_manual")
        self.html_dir.mkdir(exist_ok=True)
        
        # 加载探索数据
        self.load_exploration_data()
    
    def load_exploration_data(self):
        """加载探索数据"""
        try:
            # 优先加载增强版探索数据
            enhanced_results_file = self.data_dir / "enhanced_exploration_results.json"
            enhanced_report_file = self.data_dir / "enhanced_exploration_report.json"

            if enhanced_results_file.exists() and enhanced_report_file.exists():
                with open(enhanced_results_file, 'r', encoding='utf-8') as f:
                    self.exploration_results = json.load(f)

                with open(enhanced_report_file, 'r', encoding='utf-8') as f:
                    self.exploration_report = json.load(f)

                print("增强版探索数据加载成功")
            else:
                # 回退到原始数据
                with open(self.data_dir / "comprehensive_exploration_results.json", 'r', encoding='utf-8') as f:
                    self.exploration_results = json.load(f)

                with open(self.data_dir / "exploration_report.json", 'r', encoding='utf-8') as f:
                    self.exploration_report = json.load(f)

                print("原始探索数据加载成功")

        except Exception as e:
            print(f"加载探索数据时出错: {e}")
            self.exploration_results = []
            self.exploration_report = {}
    
    def create_html_template(self):
        """创建HTML模板"""
        return """<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Boss3业务管理系统使用手册</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', sans-serif;
            line-height: 1.6;
            color: #333;
            background-color: #f5f5f5;
        }
        
        .container {
            display: flex;
            min-height: 100vh;
        }
        
        .sidebar {
            width: 280px;
            background: #2c3e50;
            color: white;
            padding: 20px;
            position: fixed;
            height: 100vh;
            overflow-y: auto;
            z-index: 1000;
        }
        
        .sidebar h1 {
            font-size: 18px;
            margin-bottom: 20px;
            padding-bottom: 10px;
            border-bottom: 1px solid #34495e;
        }
        
        .nav-menu {
            list-style: none;
        }
        
        .nav-menu li {
            margin-bottom: 5px;
        }
        
        .nav-menu a {
            color: #ecf0f1;
            text-decoration: none;
            display: block;
            padding: 8px 12px;
            border-radius: 4px;
            transition: background-color 0.3s;
        }
        
        .nav-menu a:hover {
            background-color: #34495e;
        }
        
        .nav-menu .active {
            background-color: #3498db;
        }
        
        .nav-submenu {
            list-style: none;
            margin-left: 15px;
            margin-top: 5px;
        }
        
        .nav-submenu a {
            font-size: 14px;
            color: #bdc3c7;
        }
        
        .main-content {
            margin-left: 280px;
            flex: 1;
            padding: 20px;
            background: white;
            min-height: 100vh;
        }
        
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            margin: -20px -20px 30px -20px;
            border-radius: 0 0 10px 10px;
        }
        
        .header h1 {
            font-size: 28px;
            margin-bottom: 10px;
        }
        
        .header p {
            font-size: 16px;
            opacity: 0.9;
        }
        
        .section {
            margin-bottom: 40px;
            padding: 20px;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .section h2 {
            color: #2c3e50;
            font-size: 24px;
            margin-bottom: 20px;
            padding-bottom: 10px;
            border-bottom: 2px solid #3498db;
        }
        
        .section h3 {
            color: #34495e;
            font-size: 20px;
            margin: 25px 0 15px 0;
        }
        
        .section h4 {
            color: #7f8c8d;
            font-size: 16px;
            margin: 20px 0 10px 0;
        }
        
        .function-card {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 6px;
            padding: 15px;
            margin: 15px 0;
        }
        
        .function-card h4 {
            color: #495057;
            margin-bottom: 10px;
        }
        
        .function-meta {
            display: flex;
            gap: 15px;
            margin-bottom: 10px;
            font-size: 14px;
            color: #6c757d;
        }
        
        .function-features {
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
            margin-top: 10px;
        }
        
        .feature-tag {
            background: #e3f2fd;
            color: #1976d2;
            padding: 2px 8px;
            border-radius: 12px;
            font-size: 12px;
        }
        
        .screenshot {
            max-width: 100%;
            height: auto;
            border: 1px solid #ddd;
            border-radius: 4px;
            margin: 15px 0;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }
        
        .stat-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            border-radius: 8px;
            text-align: center;
        }
        
        .stat-number {
            font-size: 32px;
            font-weight: bold;
            margin-bottom: 5px;
        }
        
        .stat-label {
            font-size: 14px;
            opacity: 0.9;
        }
        
        .coverage-bar {
            background: #e9ecef;
            height: 20px;
            border-radius: 10px;
            overflow: hidden;
            margin: 5px 0;
        }
        
        .coverage-fill {
            height: 100%;
            background: linear-gradient(90deg, #28a745, #20c997);
            transition: width 0.3s ease;
        }
        
        .table {
            width: 100%;
            border-collapse: collapse;
            margin: 15px 0;
        }
        
        .table th,
        .table td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #dee2e6;
        }
        
        .table th {
            background-color: #f8f9fa;
            font-weight: 600;
            color: #495057;
        }
        
        .table tr:hover {
            background-color: #f8f9fa;
        }
        
        @media (max-width: 768px) {
            .sidebar {
                transform: translateX(-100%);
                transition: transform 0.3s;
            }
            
            .sidebar.open {
                transform: translateX(0);
            }
            
            .main-content {
                margin-left: 0;
            }
            
            .stats-grid {
                grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            }
        }
        
        .search-box {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            margin-bottom: 20px;
            font-size: 14px;
        }
        
        .mobile-menu-btn {
            display: none;
            position: fixed;
            top: 20px;
            left: 20px;
            z-index: 1001;
            background: #2c3e50;
            color: white;
            border: none;
            padding: 10px;
            border-radius: 4px;
            cursor: pointer;
        }
        
        @media (max-width: 768px) {
            .mobile-menu-btn {
                display: block;
            }
        }
    </style>
</head>
<body>
    <button class="mobile-menu-btn" onclick="toggleSidebar()">☰</button>
    
    <div class="container">
        <nav class="sidebar" id="sidebar">
            <h1>Boss3使用手册</h1>
            <input type="text" class="search-box" placeholder="搜索功能..." onkeyup="searchFunction(this.value)">
            <ul class="nav-menu" id="nav-menu">
                {nav_content}
            </ul>
        </nav>
        
        <main class="main-content">
            {main_content}
        </main>
    </div>
    
    <script>
        function toggleSidebar() {
            document.getElementById('sidebar').classList.toggle('open');
        }
        
        function searchFunction(query) {
            const navItems = document.querySelectorAll('.nav-menu a');
            navItems.forEach(item => {
                const text = item.textContent.toLowerCase();
                const parent = item.parentElement;
                if (text.includes(query.toLowerCase()) || query === '') {
                    parent.style.display = 'block';
                } else {
                    parent.style.display = 'none';
                }
            });
        }
        
        function scrollToSection(sectionId) {
            document.getElementById(sectionId).scrollIntoView({
                behavior: 'smooth'
            });
            
            // 更新导航状态
            document.querySelectorAll('.nav-menu a').forEach(a => a.classList.remove('active'));
            event.target.classList.add('active');
        }
    </script>
</body>
</html>"""
    
    def generate_navigation(self):
        """生成导航菜单"""
        nav_items = [
            ("overview", "系统概览"),
            ("login", "登录指南"),
            ("functions", "功能模块"),
            ("statistics", "探索统计"),
            ("faq", "常见问题")
        ]
        
        nav_html = ""
        for section_id, title in nav_items:
            nav_html += f'<li><a href="#{section_id}" onclick="scrollToSection(\'{section_id}\')">{title}</a></li>\n'
        
        # 添加功能模块子菜单
        if self.exploration_results:
            nav_html += '<li><a href="#functions">功能详情</a>\n<ul class="nav-submenu">\n'
            
            # 按主菜单分组
            menu_groups = {}
            for result in self.exploration_results:
                parent = result['parent_menu']
                if parent not in menu_groups:
                    menu_groups[parent] = []
                menu_groups[parent].append(result['sub_menu'])
            
            for parent_menu, sub_menus in menu_groups.items():
                nav_html += f'<li><a href="#menu-{parent_menu}">{parent_menu}</a></li>\n'
            
            nav_html += '</ul></li>\n'
        
        return nav_html

    def generate_main_content(self):
        """生成主要内容"""
        content = self.generate_header()
        content += self.generate_overview_section()
        content += self.generate_login_section()
        content += self.generate_statistics_section()
        content += self.generate_functions_section()
        content += self.generate_faq_section()

        return content

    def generate_header(self):
        """生成页面头部"""
        return f"""
        <div class="header">
            <h1>Boss3业务管理系统使用手册</h1>
            <p>上海联劝公益基金会 | 生成时间: {datetime.now().strftime('%Y年%m月%d日')}</p>
        </div>
        """

    def generate_overview_section(self):
        """生成系统概览部分"""
        total_functions = len(self.exploration_results)
        total_screenshots = len(list(self.screenshots_dir.glob("*.png")))
        successful_menus = self.exploration_report.get('successful_main_menus', 0)
        total_menus = self.exploration_report.get('total_main_menus', 18)

        return f"""
        <section id="overview" class="section">
            <h2>系统概览</h2>

            <div class="stats-grid">
                <div class="stat-card">
                    <div class="stat-number">{successful_menus}</div>
                    <div class="stat-label">已探索主模块</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">{total_functions}</div>
                    <div class="stat-label">子功能数量</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">{total_screenshots}</div>
                    <div class="stat-label">功能截图</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">{(successful_menus/total_menus*100):.1f}%</div>
                    <div class="stat-label">模块覆盖率</div>
                </div>
            </div>

            <h3>系统特点</h3>
            <ul>
                <li><strong>功能全面</strong>: 涵盖收入管理、支出管理、项目管理等核心业务</li>
                <li><strong>操作便捷</strong>: 基于Web的用户界面，支持多种浏览器</li>
                <li><strong>权限管理</strong>: 完善的用户权限控制，确保数据安全</li>
                <li><strong>实时更新</strong>: 数据实时同步，支持多用户协同工作</li>
            </ul>

            <h3>主要功能模块</h3>
            <table class="table">
                <thead>
                    <tr>
                        <th>模块名称</th>
                        <th>探索状态</th>
                        <th>子功能数量</th>
                        <th>覆盖率</th>
                    </tr>
                </thead>
                <tbody>
                    {self.generate_module_table()}
                </tbody>
            </table>
        </section>
        """

    def generate_module_table(self):
        """生成模块表格"""
        table_rows = ""
        coverage_data = self.exploration_report.get('coverage_by_menu', {})

        for module_name, data in coverage_data.items():
            explored_count = data.get('explored_count', 0)
            expected_count = data.get('expected_count', 0)
            coverage_rate = data.get('coverage_rate', 0)

            status = "✅ 已探索" if explored_count > 0 else "⏳ 待探索"
            coverage_percent = f"{coverage_rate * 100:.1f}%"

            table_rows += f"""
            <tr>
                <td>{module_name}</td>
                <td>{status}</td>
                <td>{explored_count}/{expected_count if expected_count > 0 else '未知'}</td>
                <td>
                    <div class="coverage-bar">
                        <div class="coverage-fill" style="width: {coverage_rate * 100}%"></div>
                    </div>
                    {coverage_percent}
                </td>
            </tr>
            """

        return table_rows

    def generate_login_section(self):
        """生成登录指南部分"""
        return """
        <section id="login" class="section">
            <h2>登录指南</h2>

            <h3>访问系统</h3>
            <ol>
                <li>打开浏览器，访问系统地址：<code>http://csboss.lianquan.org.cn</code></li>
                <li>系统支持Chrome、Firefox、Safari等主流浏览器</li>
            </ol>

            <img src="screenshots/01_login_page.png" alt="登录页面" class="screenshot">

            <h3>登录步骤</h3>
            <ol>
                <li><strong>输入用户名</strong>: 在用户名输入框中输入您的账号</li>
                <li><strong>输入密码</strong>: 在密码输入框中输入您的密码</li>
                <li><strong>点击登录</strong>: 点击"登录"按钮进入系统</li>
            </ol>

            <img src="screenshots/02_after_login.png" alt="登录后界面" class="screenshot">

            <h3>注意事项</h3>
            <ul>
                <li>请妥善保管您的账号密码，不要与他人共享</li>
                <li>如果忘记密码，请联系系统管理员重置</li>
                <li>建议定期更改密码，确保账户安全</li>
                <li>使用完毕后请及时退出登录</li>
            </ul>
        </section>
        """

    def generate_statistics_section(self):
        """生成探索统计部分"""
        coverage_data = self.exploration_report.get('coverage_by_menu', {})

        return f"""
        <section id="statistics" class="section">
            <h2>探索统计</h2>

            <h3>总体统计</h3>
            <ul>
                <li>探索时间: {self.exploration_report.get('exploration_time', '未知')}</li>
                <li>成功探索的主菜单: {self.exploration_report.get('successful_main_menus', 0)}/{self.exploration_report.get('total_main_menus', 18)}</li>
                <li>总子功能数量: {self.exploration_report.get('total_sub_functions_explored', 0)}</li>
                <li>生成截图数量: {self.exploration_report.get('total_screenshots', 0)}</li>
                <li>失败尝试次数: {self.exploration_report.get('failed_attempts_count', 0)}</li>
            </ul>

            <h3>各模块覆盖情况</h3>
            <div class="stats-grid">
                {self.generate_coverage_cards()}
            </div>
        </section>
        """

    def generate_coverage_cards(self):
        """生成覆盖率卡片"""
        coverage_data = self.exploration_report.get('coverage_by_menu', {})
        cards_html = ""

        for module_name, data in coverage_data.items():
            if data.get('explored_count', 0) > 0:  # 只显示已探索的模块
                coverage_rate = data.get('coverage_rate', 0)
                explored_count = data.get('explored_count', 0)
                expected_count = data.get('expected_count', 0)

                cards_html += f"""
                <div class="stat-card">
                    <div class="stat-number">{coverage_rate * 100:.1f}%</div>
                    <div class="stat-label">{module_name}</div>
                    <div style="font-size: 12px; margin-top: 5px;">
                        {explored_count}/{expected_count if expected_count > 0 else '?'} 功能
                    </div>
                </div>
                """

        return cards_html

    def generate_functions_section(self):
        """生成功能模块部分"""
        content = """
        <section id="functions" class="section">
            <h2>功能模块详情</h2>
            <p>以下是已成功探索的功能模块详细信息：</p>
        """

        # 按主菜单分组
        menu_groups = {}
        for result in self.exploration_results:
            parent = result['parent_menu']
            if parent not in menu_groups:
                menu_groups[parent] = []
            menu_groups[parent].append(result)

        # 为每个主菜单生成内容
        for parent_menu, functions in menu_groups.items():
            content += f"""
            <h3 id="menu-{parent_menu}">{parent_menu}</h3>
            <p>该模块包含 {len(functions)} 个已探索的子功能：</p>
            """

            for func in functions:
                content += self.generate_function_card(func)

        content += "</section>"
        return content

    def generate_function_card(self, function_data):
        """生成单个功能卡片"""
        content_analysis = function_data.get('content_analysis', {})

        # 生成功能特性标签
        features = []
        if content_analysis.get('has_search'):
            features.append("搜索")
        if content_analysis.get('has_pagination'):
            features.append("分页")
        if content_analysis.get('has_add_button'):
            features.append("新增")
        if content_analysis.get('has_export_button'):
            features.append("导出")
        if content_analysis.get('has_edit_button'):
            features.append("编辑")
        if content_analysis.get('has_delete_button'):
            features.append("删除")

        features_html = "".join([f'<span class="feature-tag">{feature}</span>' for feature in features])

        # 确保截图路径正确
        screenshot_path = function_data['screenshot']
        if not screenshot_path.startswith('screenshots/'):
            screenshot_path = f"screenshots/{screenshot_path}"

        # 检查文件是否存在，如果不存在则使用默认图片
        full_screenshot_path = self.html_dir / screenshot_path
        if not full_screenshot_path.exists():
            # 尝试查找相似的截图文件
            screenshot_name = Path(screenshot_path).name
            alternative_files = list(self.html_dir.glob(f"screenshots/*{function_data['sub_menu']}*.png"))
            if alternative_files:
                screenshot_path = f"screenshots/{alternative_files[0].name}"
            else:
                # 使用占位符图片路径
                screenshot_path = "screenshots/placeholder.png"

        return f"""
        <div class="function-card">
            <h4>{function_data['sub_menu']}</h4>
            <div class="function-meta">
                <span>📅 {function_data['timestamp'][:10]}</span>
                <span>🔗 <a href="{function_data['url']}" target="_blank">访问链接</a></span>
                <span>📊 {content_analysis.get('tables_count', 0)} 个表格</span>
                <span>🔘 {content_analysis.get('buttons_count', 0)} 个按钮</span>
            </div>
            <div class="function-features">
                {features_html}
            </div>
            <img src="{screenshot_path}" alt="{function_data['sub_menu']}截图" class="screenshot">
        </div>
        """

    def generate_faq_section(self):
        """生成常见问题部分"""
        return """
        <section id="faq" class="section">
            <h2>常见问题</h2>

            <h3>Q: 忘记密码怎么办？</h3>
            <p>A: 请联系系统管理员重置密码。</p>

            <h3>Q: 页面加载缓慢怎么办？</h3>
            <p>A: 请检查网络连接，或尝试刷新页面。如问题持续存在，请联系技术支持。</p>

            <h3>Q: 没有某个功能的操作权限怎么办？</h3>
            <p>A: 请联系您的直属领导或系统管理员申请相应权限。</p>

            <h3>Q: 数据保存失败怎么办？</h3>
            <p>A: 请检查必填字段是否完整，数据格式是否正确。如问题持续存在，请联系技术支持。</p>

            <h3>联系方式</h3>
            <ul>
                <li><strong>技术支持</strong>: 请联系IT部门</li>
                <li><strong>业务咨询</strong>: 请联系相关业务负责人</li>
                <li><strong>系统管理员</strong>: 王金晓</li>
            </ul>
        </section>
        """

    def generate_html_manual(self):
        """生成完整的HTML手册"""
        template = self.create_html_template()
        nav_content = self.generate_navigation()
        main_content = self.generate_main_content()

        # 使用replace而不是format来避免格式化问题
        html_content = template.replace("{nav_content}", nav_content)
        html_content = html_content.replace("{main_content}", main_content)

        # 保存HTML文件
        html_file = self.html_dir / "index.html"
        with open(html_file, 'w', encoding='utf-8') as f:
            f.write(html_content)

        # 复制截图到HTML目录
        self.copy_screenshots()

        print(f"HTML手册已生成: {html_file}")
        return html_file

    def copy_screenshots(self):
        """复制截图到HTML目录"""
        import shutil

        html_screenshots_dir = self.html_dir / "screenshots"
        html_screenshots_dir.mkdir(exist_ok=True)

        # 复制所有截图，包括增强版探索的截图
        copied_count = 0
        for screenshot in self.screenshots_dir.glob("*.png"):
            try:
                shutil.copy2(screenshot, html_screenshots_dir / screenshot.name)
                copied_count += 1
            except Exception as e:
                print(f"复制截图 {screenshot.name} 时出错: {e}")

        print(f"已复制 {copied_count} 张截图到HTML目录")

    def run_generation(self):
        """运行HTML生成"""
        try:
            html_file = self.generate_html_manual()
            print(f"HTML使用手册已生成到: {html_file}")
            return True
        except Exception as e:
            print(f"生成HTML手册时出错: {e}")
            return False

def main():
    """主函数"""
    generator = HTMLManualGenerator()
    success = generator.run_generation()

    if success:
        print("HTML使用手册生成成功！")
    else:
        print("HTML使用手册生成失败！")

if __name__ == "__main__":
    main()
