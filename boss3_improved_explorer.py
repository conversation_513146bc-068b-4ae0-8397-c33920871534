#!/usr/bin/env python3
"""
Boss3系统改进版功能探索脚本
解决菜单点击问题，提升功能覆盖率
"""

import asyncio
import json
import re
from datetime import datetime
from pathlib import Path
from playwright.async_api import async_playwright
import time

class Boss3ImprovedExplorer:
    def __init__(self):
        self.base_url = "http://csboss.lianquan.org.cn"
        self.username = "wang<PERSON><PERSON><PERSON>"
        self.password = "Ok756756@"
        self.screenshots_dir = Path("screenshots")
        self.data_dir = Path("data")
        self.browser = None
        self.page = None
        self.explored_functions = []
        self.failed_attempts = []
        
        # 创建必要的目录
        self.screenshots_dir.mkdir(exist_ok=True)
        self.data_dir.mkdir(exist_ok=True)
        
        # 定义主菜单项和预期子功能
        self.main_menus = {
            "数据看板": [],
            "收入管理": ["认领公告", "认领审核", "渠道收入", "账单明细", "未入账清单", "筹款产品", "慈善备案", "订单查询", "支出订单", "统计报表", "配置管理"],
            "资金池管理": ["资金池", "收入调整", "隐藏资金池", "资金池清理"],
            "预算决算": ["收入", "支出", "导入记录"],
            "物资管理": ["库存管理", "仓管单据", "物品管理", "仓库管理"],
            "支出管理": ["备用金", "外部请款", "项目报销", "行政报销", "支出调整", "支出退款", "捐赠退款", "商家退款", "银企直连", "票据管理", "票据催办", "支付汇总", "票据备份", "项目分摊", "报销票据管理"],
            "业务管理": [],
            "资助管理": [],
            "项目管理": [],
            "开票管理": ["票据看板", "票据开具", "票据查询", "订单核销", "核销失败", "备份下载", "票据接口"],
            "合作方管理": [],
            "合同管理": [],
            "捐方管理": [],
            "财务统计": ["月末关账", "月收入报表", "月收入结转", "业务收支汇总", "业务收支明细"],
            "用友管理": ["会计科目", "辅助核算", "凭证确认", "凭证管理"],
            "可变配置": ["动态表单", "动态标签", "合同模板"],
            "安全审计": ["操作日志", "错误日志"],
            "组织管理": ["组织架构", "角色配置", "资金权限", "更新记录"]
        }
    
    async def start_browser(self):
        """启动浏览器"""
        playwright = await async_playwright().start()
        self.browser = await playwright.chromium.launch(
            headless=False,
            args=['--no-sandbox', '--disable-dev-shm-usage'],
            slow_mo=1000  # 添加延迟，提高稳定性
        )
        context = await self.browser.new_context(
            viewport={'width': 1920, 'height': 1080}
        )
        self.page = await context.new_page()
        
        # 设置更长的超时时间
        self.page.set_default_timeout(60000)
        
    async def login(self):
        """登录系统"""
        print(f"正在访问登录页面: {self.base_url}")
        await self.page.goto(self.base_url)
        await self.page.wait_for_load_state('networkidle')
        
        # 填写用户名和密码
        await self.page.fill('input[name="username"]', self.username)
        await self.page.fill('input[name="password"]', self.password)
        
        # 点击登录按钮
        login_button = await self.page.query_selector('button:has-text("登 录")')
        if login_button:
            await login_button.click()
        
        await self.page.wait_for_load_state('networkidle')
        await asyncio.sleep(3)
        
        print(f"登录后URL: {self.page.url}")
        return True
    
    async def safe_click_element(self, selector, timeout=30000, retry_count=3):
        """安全点击元素，带重试机制"""
        for attempt in range(retry_count):
            try:
                # 等待元素出现
                await self.page.wait_for_selector(selector, timeout=timeout)
                
                # 确保元素可见和可点击
                element = await self.page.query_selector(selector)
                if element:
                    # 滚动到元素位置
                    await element.scroll_into_view_if_needed()
                    await asyncio.sleep(1)
                    
                    # 检查元素是否可见
                    is_visible = await element.is_visible()
                    if is_visible:
                        await element.click()
                        await asyncio.sleep(2)
                        return True
                    else:
                        print(f"元素不可见: {selector}")
                
            except Exception as e:
                print(f"点击尝试 {attempt + 1} 失败: {selector}, 错误: {e}")
                if attempt < retry_count - 1:
                    await asyncio.sleep(2)
                    continue
                else:
                    return False
        
        return False
    
    async def find_and_click_menu_item(self, menu_text, is_main_menu=True):
        """查找并点击菜单项"""
        print(f"查找菜单项: {menu_text}")
        
        # 多种选择器策略
        selectors = []
        
        if is_main_menu:
            selectors = [
                f'.el-submenu__title:has-text("{menu_text}")',
                f'.el-menu-item:has-text("{menu_text}")',
                f'[title="{menu_text}"]',
                f'text="{menu_text}"'
            ]
        else:
            selectors = [
                f'.el-menu-item:has-text("{menu_text}")',
                f'.el-menu--inline .el-menu-item:has-text("{menu_text}")',
                f'a:has-text("{menu_text}")',
                f'text="{menu_text}"'
            ]
        
        for selector in selectors:
            try:
                success = await self.safe_click_element(selector, timeout=10000)
                if success:
                    print(f"成功点击菜单项: {menu_text} (使用选择器: {selector})")
                    return True
            except Exception as e:
                print(f"选择器 {selector} 失败: {e}")
                continue
        
        print(f"无法点击菜单项: {menu_text}")
        return False
    
    async def explore_main_menu_comprehensive(self, menu_name):
        """全面探索主菜单"""
        print(f"\n=== 开始探索主菜单: {menu_name} ===")
        
        try:
            # 返回主页面确保状态一致
            await self.page.goto(f"{self.base_url}/statistic/bill/board")
            await self.page.wait_for_load_state('networkidle')
            await asyncio.sleep(3)
            
            # 点击主菜单
            main_menu_clicked = await self.find_and_click_menu_item(menu_name, is_main_menu=True)
            
            if not main_menu_clicked:
                self.failed_attempts.append({
                    'menu': menu_name,
                    'type': 'main_menu',
                    'reason': 'Cannot click main menu'
                })
                return False
            
            # 等待子菜单展开
            await asyncio.sleep(3)
            
            # 截取展开后的菜单
            screenshot_name = f"main_menu_{self.sanitize_filename(menu_name)}_expanded.png"
            await self.page.screenshot(path=self.screenshots_dir / screenshot_name)
            
            # 探索子菜单
            expected_sub_menus = self.main_menus.get(menu_name, [])
            explored_count = 0
            
            if expected_sub_menus:
                for sub_menu in expected_sub_menus[:5]:  # 探索前5个子菜单
                    success = await self.explore_sub_menu(menu_name, sub_menu)
                    if success:
                        explored_count += 1
                    
                    # 每次子菜单探索后返回主页面
                    await self.page.goto(f"{self.base_url}/statistic/bill/board")
                    await self.page.wait_for_load_state('networkidle')
                    await asyncio.sleep(2)
            else:
                # 如果没有预定义子菜单，尝试自动发现
                explored_count = await self.auto_discover_sub_menus(menu_name)
            
            print(f"主菜单 {menu_name} 探索完成，成功探索 {explored_count} 个子功能")
            return explored_count > 0
            
        except Exception as e:
            print(f"探索主菜单 {menu_name} 时出错: {e}")
            self.failed_attempts.append({
                'menu': menu_name,
                'type': 'main_menu',
                'reason': str(e)
            })
            return False
    
    async def explore_sub_menu(self, parent_menu, sub_menu_name):
        """探索子菜单"""
        print(f"  探索子菜单: {parent_menu} -> {sub_menu_name}")
        
        try:
            # 重新点击主菜单
            main_menu_clicked = await self.find_and_click_menu_item(parent_menu, is_main_menu=True)
            if not main_menu_clicked:
                return False
            
            await asyncio.sleep(2)
            
            # 点击子菜单
            sub_menu_clicked = await self.find_and_click_menu_item(sub_menu_name, is_main_menu=False)
            if not sub_menu_clicked:
                self.failed_attempts.append({
                    'menu': f"{parent_menu} -> {sub_menu_name}",
                    'type': 'sub_menu',
                    'reason': 'Cannot click sub menu'
                })
                return False
            
            # 等待页面加载
            await self.page.wait_for_load_state('networkidle')
            await asyncio.sleep(3)
            
            # 截图
            screenshot_name = f"sub_menu_{self.sanitize_filename(parent_menu)}_{self.sanitize_filename(sub_menu_name)}.png"
            await self.page.screenshot(path=self.screenshots_dir / screenshot_name)
            
            # 记录功能信息
            function_info = {
                'parent_menu': parent_menu,
                'sub_menu': sub_menu_name,
                'url': self.page.url,
                'title': await self.page.title(),
                'screenshot': screenshot_name,
                'timestamp': datetime.now().isoformat()
            }
            
            # 分析页面内容
            await self.analyze_page_content_detailed(function_info)
            
            self.explored_functions.append(function_info)
            print(f"    成功探索: {parent_menu} -> {sub_menu_name}")
            return True
            
        except Exception as e:
            print(f"    探索子菜单失败: {parent_menu} -> {sub_menu_name}, 错误: {e}")
            self.failed_attempts.append({
                'menu': f"{parent_menu} -> {sub_menu_name}",
                'type': 'sub_menu',
                'reason': str(e)
            })
            return False
    
    async def auto_discover_sub_menus(self, parent_menu):
        """自动发现子菜单"""
        print(f"  自动发现 {parent_menu} 的子菜单...")
        
        try:
            # 重新点击主菜单
            await self.find_and_click_menu_item(parent_menu, is_main_menu=True)
            await asyncio.sleep(2)
            
            # 查找可能的子菜单项
            sub_menu_selectors = [
                '.el-menu--inline .el-menu-item',
                '.el-submenu .el-menu-item',
                '.submenu-item'
            ]
            
            discovered_count = 0
            
            for selector in sub_menu_selectors:
                try:
                    elements = await self.page.query_selector_all(selector)
                    
                    for i, element in enumerate(elements[:3]):  # 最多探索3个
                        try:
                            text = await element.inner_text()
                            if text and text.strip() and len(text.strip()) > 1:
                                success = await self.explore_discovered_sub_menu(parent_menu, text.strip(), i)
                                if success:
                                    discovered_count += 1
                        except:
                            continue
                    
                    if discovered_count > 0:
                        break
                        
                except Exception as e:
                    print(f"    自动发现时出错: {e}")
                    continue
            
            return discovered_count
            
        except Exception as e:
            print(f"  自动发现 {parent_menu} 子菜单时出错: {e}")
            return 0
    
    async def explore_discovered_sub_menu(self, parent_menu, sub_menu_text, index):
        """探索发现的子菜单"""
        try:
            # 返回主页面
            await self.page.goto(f"{self.base_url}/statistic/bill/board")
            await self.page.wait_for_load_state('networkidle')
            await asyncio.sleep(2)
            
            # 重新导航到子菜单
            success = await self.explore_sub_menu(parent_menu, sub_menu_text)
            return success
            
        except Exception as e:
            print(f"    探索发现的子菜单失败: {parent_menu} -> {sub_menu_text}, 错误: {e}")
            return False

    async def analyze_page_content_detailed(self, function_info):
        """详细分析页面内容"""
        try:
            # 查找页面中的关键元素
            buttons = await self.page.query_selector_all('button, .btn, input[type="button"], input[type="submit"]')
            tables = await self.page.query_selector_all('table, .table, .el-table')
            forms = await self.page.query_selector_all('form, .form, .el-form')
            inputs = await self.page.query_selector_all('input, .input, .el-input')

            # 获取按钮文本
            button_texts = []
            for button in buttons[:10]:  # 只获取前10个按钮
                try:
                    text = await button.inner_text()
                    if text and text.strip():
                        button_texts.append(text.strip())
                except:
                    continue

            # 获取表格信息
            table_info = []
            for table in tables[:3]:  # 只分析前3个表格
                try:
                    headers = await table.query_selector_all('th')
                    header_texts = []
                    for header in headers[:5]:  # 只获取前5个表头
                        try:
                            text = await header.inner_text()
                            if text and text.strip():
                                header_texts.append(text.strip())
                        except:
                            continue

                    if header_texts:
                        table_info.append({
                            'headers': header_texts,
                            'header_count': len(header_texts)
                        })
                except:
                    continue

            # 获取页面标题和描述
            page_title = await self.page.title()

            # 尝试获取页面主要内容
            main_content = ""
            try:
                main_element = await self.page.query_selector('.main-content, .content, .page-content, main')
                if main_element:
                    main_content = await main_element.inner_text()
                    main_content = main_content[:200] if main_content else ""  # 限制长度
            except:
                pass

            content_analysis = {
                'buttons_count': len(buttons),
                'tables_count': len(tables),
                'forms_count': len(forms),
                'inputs_count': len(inputs),
                'button_texts': button_texts,
                'table_info': table_info,
                'page_title': page_title,
                'main_content': main_content,
                'has_search': bool(await self.page.query_selector('input[type="search"], .search, .el-input__inner[placeholder*="搜索"], .el-input__inner[placeholder*="查询"]')),
                'has_pagination': bool(await self.page.query_selector('.pagination, .page, .el-pagination')),
                'has_add_button': any('新增' in text or '添加' in text or '创建' in text for text in button_texts),
                'has_export_button': any('导出' in text or '下载' in text for text in button_texts),
                'has_edit_button': any('编辑' in text or '修改' in text for text in button_texts),
                'has_delete_button': any('删除' in text for text in button_texts)
            }

            function_info['content_analysis'] = content_analysis

        except Exception as e:
            print(f"    分析页面内容时出错: {e}")
            function_info['content_analysis'] = {'error': str(e)}

    def sanitize_filename(self, filename):
        """清理文件名"""
        filename = re.sub(r'[<>:"/\\|?*]', '_', filename)
        filename = filename.replace(' ', '_')
        return filename[:30]  # 限制长度

    async def run_comprehensive_exploration(self):
        """运行全面探索"""
        print("开始全面探索Boss3系统...")

        try:
            await self.start_browser()

            # 登录
            login_success = await self.login()
            if not login_success:
                print("登录失败，无法继续探索")
                return False

            # 初始截图
            await self.page.screenshot(path=self.screenshots_dir / "00_initial_dashboard.png")

            # 探索所有主菜单
            successful_menus = 0
            total_sub_functions = 0

            for menu_name in self.main_menus.keys():
                try:
                    success = await self.explore_main_menu_comprehensive(menu_name)
                    if success:
                        successful_menus += 1

                    # 统计已探索的子功能数量
                    menu_sub_functions = len([f for f in self.explored_functions if f['parent_menu'] == menu_name])
                    total_sub_functions += menu_sub_functions

                    print(f"菜单 {menu_name}: {'成功' if success else '失败'}, 子功能: {menu_sub_functions}")

                except Exception as e:
                    print(f"探索菜单 {menu_name} 时出现异常: {e}")
                    continue

            # 保存探索结果
            await self.save_exploration_results()

            # 打印统计信息
            self.print_exploration_statistics(successful_menus, total_sub_functions)

            return True

        except Exception as e:
            print(f"探索过程中出现错误: {e}")
            return False
        finally:
            await self.close()

    async def save_exploration_results(self):
        """保存探索结果"""
        # 保存成功探索的功能
        with open(self.data_dir / "comprehensive_exploration_results.json", 'w', encoding='utf-8') as f:
            json.dump(self.explored_functions, f, ensure_ascii=False, indent=2)

        # 保存失败尝试
        with open(self.data_dir / "failed_attempts.json", 'w', encoding='utf-8') as f:
            json.dump(self.failed_attempts, f, ensure_ascii=False, indent=2)

        # 生成探索报告
        report = {
            'exploration_time': datetime.now().isoformat(),
            'total_main_menus': len(self.main_menus),
            'successful_main_menus': len(set(f['parent_menu'] for f in self.explored_functions)),
            'total_sub_functions_explored': len(self.explored_functions),
            'total_screenshots': len(list(self.screenshots_dir.glob("*.png"))),
            'failed_attempts_count': len(self.failed_attempts),
            'coverage_by_menu': {}
        }

        # 计算每个菜单的覆盖率
        for menu_name, expected_subs in self.main_menus.items():
            explored_subs = [f['sub_menu'] for f in self.explored_functions if f['parent_menu'] == menu_name]

            if expected_subs:
                coverage_rate = len(set(explored_subs) & set(expected_subs)) / len(expected_subs)
            else:
                coverage_rate = 1.0 if explored_subs else 0.0

            report['coverage_by_menu'][menu_name] = {
                'expected_count': len(expected_subs),
                'explored_count': len(explored_subs),
                'coverage_rate': coverage_rate,
                'explored_functions': explored_subs
            }

        with open(self.data_dir / "exploration_report.json", 'w', encoding='utf-8') as f:
            json.dump(report, f, ensure_ascii=False, indent=2)

        print(f"探索结果已保存到 {self.data_dir}")

    def print_exploration_statistics(self, successful_menus, total_sub_functions):
        """打印探索统计信息"""
        print(f"\n=== 探索统计 ===")
        print(f"成功探索的主菜单: {successful_menus}/{len(self.main_menus)} ({successful_menus/len(self.main_menus)*100:.1f}%)")
        print(f"成功探索的子功能: {total_sub_functions}")
        print(f"生成的截图数量: {len(list(self.screenshots_dir.glob('*.png')))}")
        print(f"失败尝试次数: {len(self.failed_attempts)}")

        # 按菜单显示覆盖率
        print(f"\n=== 各菜单覆盖情况 ===")
        for menu_name in self.main_menus.keys():
            menu_functions = [f for f in self.explored_functions if f['parent_menu'] == menu_name]
            expected_count = len(self.main_menus[menu_name])
            actual_count = len(menu_functions)

            if expected_count > 0:
                coverage = actual_count / expected_count * 100
                print(f"{menu_name}: {actual_count}/{expected_count} ({coverage:.1f}%)")
            else:
                status = "已探索" if actual_count > 0 else "未探索"
                print(f"{menu_name}: {actual_count} 个功能 ({status})")

    async def close(self):
        """关闭浏览器"""
        if self.browser:
            await self.browser.close()

async def main():
    """主函数"""
    explorer = Boss3ImprovedExplorer()
    success = await explorer.run_comprehensive_exploration()

    if success:
        print("\n系统功能全面探索完成！")
    else:
        print("\n系统功能探索失败！")

if __name__ == "__main__":
    asyncio.run(main())
