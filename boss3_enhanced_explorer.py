#!/usr/bin/env python3
"""
Boss3系统增强版功能探索脚本
解决浏览器会话中断和菜单点击失败问题，大幅提升覆盖率
"""

import asyncio
import json
import re
import logging
from datetime import datetime
from pathlib import Path
from playwright.async_api import async_playwright, TimeoutError
import time

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class Boss3EnhancedExplorer:
    def __init__(self):
        self.base_url = "http://csboss.lianquan.org.cn"
        self.username = "wangjin<PERSON>o"
        self.password = "Ok756756@"
        self.screenshots_dir = Path("screenshots")
        self.data_dir = Path("data")
        self.browser = None
        self.context = None
        self.page = None
        self.explored_functions = []
        self.failed_attempts = []
        self.session_active = False
        
        # 创建必要的目录
        self.screenshots_dir.mkdir(exist_ok=True)
        self.data_dir.mkdir(exist_ok=True)
        
        # 定义主菜单项（完整列表）
        self.main_menus = [
            "数据看板", "收入管理", "资金池管理", "预算决算", "物资管理", 
            "支出管理", "业务管理", "资助管理", "项目管理", "开票管理",
            "合作方管理", "合同管理", "捐方管理", "财务统计", "用友管理",
            "可变配置", "安全审计", "组织管理"
        ]
        
        # 菜单状态跟踪
        self.menu_states = {}
        self.current_expanded_menu = None
    
    async def start_browser_session(self):
        """启动浏览器会话，增强稳定性"""
        try:
            playwright = await async_playwright().start()
            self.browser = await playwright.chromium.launch(
                headless=False,
                args=[
                    '--no-sandbox', 
                    '--disable-dev-shm-usage',
                    '--disable-web-security',
                    '--disable-features=VizDisplayCompositor',
                    '--memory-pressure-off'
                ],
                slow_mo=500  # 减少操作速度，提高稳定性
            )
            
            # 创建持久化上下文
            self.context = await self.browser.new_context(
                viewport={'width': 1920, 'height': 1080},
                user_agent='Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
            )
            
            self.page = await self.context.new_page()
            
            # 设置更长的超时时间
            self.page.set_default_timeout(30000)
            self.session_active = True
            
            logger.info("浏览器会话启动成功")
            return True
            
        except Exception as e:
            logger.error(f"启动浏览器会话失败: {e}")
            return False
    
    async def check_session_health(self):
        """检查会话健康状态"""
        try:
            if not self.page or not self.session_active:
                return False
            
            # 尝试获取页面标题来验证会话
            await self.page.title()
            return True
            
        except Exception as e:
            logger.warning(f"会话健康检查失败: {e}")
            self.session_active = False
            return False
    
    async def recover_session(self):
        """恢复会话"""
        logger.info("尝试恢复浏览器会话...")
        
        try:
            # 关闭现有会话
            if self.browser:
                await self.browser.close()
            
            # 重新启动
            success = await self.start_browser_session()
            if success:
                # 重新登录
                login_success = await self.login()
                if login_success:
                    logger.info("会话恢复成功")
                    return True
            
            logger.error("会话恢复失败")
            return False
            
        except Exception as e:
            logger.error(f"会话恢复过程中出错: {e}")
            return False
    
    async def safe_operation(self, operation, *args, **kwargs):
        """安全执行操作，带会话恢复"""
        max_retries = 3
        
        for attempt in range(max_retries):
            try:
                # 检查会话健康状态
                if not await self.check_session_health():
                    if not await self.recover_session():
                        continue
                
                # 执行操作
                result = await operation(*args, **kwargs)
                return result
                
            except Exception as e:
                logger.warning(f"操作失败 (尝试 {attempt + 1}/{max_retries}): {e}")
                
                if attempt < max_retries - 1:
                    await asyncio.sleep(2)
                    continue
                else:
                    logger.error(f"操作最终失败: {operation.__name__}")
                    return None
        
        return None
    
    async def login(self):
        """登录系统"""
        try:
            logger.info(f"正在访问登录页面: {self.base_url}")
            await self.page.goto(self.base_url, wait_until='networkidle')
            
            # 等待登录表单加载
            await self.page.wait_for_selector('input[name="username"]', timeout=10000)
            
            # 填写用户名和密码
            await self.page.fill('input[name="username"]', self.username)
            await self.page.fill('input[name="password"]', self.password)
            
            # 点击登录按钮
            login_button = await self.page.query_selector('button:has-text("登 录")')
            if login_button:
                await login_button.click()
            
            # 等待登录完成
            await self.page.wait_for_load_state('networkidle')
            await asyncio.sleep(3)
            
            # 验证登录成功
            current_url = self.page.url
            if 'login' not in current_url.lower():
                logger.info(f"登录成功，当前URL: {current_url}")
                return True
            else:
                logger.error("登录失败")
                return False
                
        except Exception as e:
            logger.error(f"登录过程中出错: {e}")
            return False
    
    async def enhanced_element_locator(self, text, element_type="menu"):
        """增强的元素定位器"""
        # 清理文本
        clean_text = text.strip()
        
        # 多种选择器策略
        selectors = []
        
        if element_type == "main_menu":
            selectors = [
                # Element UI 菜单选择器
                f'.el-submenu__title:has-text("{clean_text}")',
                f'.el-menu-item:has-text("{clean_text}")',
                
                # 通用选择器
                f'[title="{clean_text}"]',
                f'[aria-label="{clean_text}"]',
                f'text="{clean_text}"',
                
                # 模糊匹配
                f'text=/.*{re.escape(clean_text)}.*/i',
                
                # XPath 备选
                f'//span[contains(text(), "{clean_text}")]',
                f'//div[contains(text(), "{clean_text}")]',
                f'//*[contains(@class, "menu") and contains(text(), "{clean_text}")]'
            ]
        else:  # sub_menu
            selectors = [
                # 子菜单特定选择器
                f'.el-menu--inline .el-menu-item:has-text("{clean_text}")',
                f'.el-submenu .el-menu-item:has-text("{clean_text}")',
                f'.submenu-item:has-text("{clean_text}")',
                
                # 通用选择器
                f'a:has-text("{clean_text}")',
                f'li:has-text("{clean_text}")',
                f'text="{clean_text}"',
                
                # 模糊匹配
                f'text=/.*{re.escape(clean_text)}.*/i',
                
                # XPath 备选
                f'//a[contains(text(), "{clean_text}")]',
                f'//li[contains(text(), "{clean_text}")]'
            ]
        
        return selectors
    
    async def smart_click_element(self, text, element_type="menu", timeout=15000):
        """智能点击元素"""
        selectors = await self.enhanced_element_locator(text, element_type)
        
        for i, selector in enumerate(selectors):
            try:
                logger.debug(f"尝试选择器 {i+1}/{len(selectors)}: {selector}")
                
                # 等待元素出现
                await self.page.wait_for_selector(selector, timeout=timeout//len(selectors))
                
                # 获取所有匹配的元素
                elements = await self.page.query_selector_all(selector)
                
                for element in elements:
                    try:
                        # 检查元素文本是否匹配
                        element_text = await element.inner_text()
                        if not element_text or text.strip() not in element_text.strip():
                            continue
                        
                        # 滚动到元素位置
                        await element.scroll_into_view_if_needed()
                        await asyncio.sleep(0.5)
                        
                        # 检查元素状态
                        is_visible = await element.is_visible()
                        is_enabled = await element.is_enabled()
                        
                        if not is_visible or not is_enabled:
                            logger.debug(f"元素不可用: visible={is_visible}, enabled={is_enabled}")
                            continue
                        
                        # 尝试点击
                        await element.click(timeout=5000)
                        await asyncio.sleep(1)
                        
                        logger.info(f"成功点击元素: {text} (选择器: {selector})")
                        return True
                        
                    except Exception as e:
                        logger.debug(f"点击元素失败: {e}")
                        continue
                
            except TimeoutError:
                logger.debug(f"选择器超时: {selector}")
                continue
            except Exception as e:
                logger.debug(f"选择器错误: {selector}, 错误: {e}")
                continue
        
        logger.warning(f"无法点击元素: {text}")
        return False
    
    async def wait_for_menu_animation(self):
        """等待菜单动画完成"""
        try:
            # 等待可能的动画完成
            await asyncio.sleep(1)
            
            # 检查是否有动画正在进行
            animations = await self.page.evaluate("""
                () => {
                    const elements = document.querySelectorAll('*');
                    let animating = false;
                    elements.forEach(el => {
                        const style = window.getComputedStyle(el);
                        if (style.animationName !== 'none' || style.transitionProperty !== 'none') {
                            animating = true;
                        }
                    });
                    return animating;
                }
            """)
            
            if animations:
                logger.debug("检测到动画，等待完成...")
                await asyncio.sleep(2)
            
        except Exception as e:
            logger.debug(f"动画检测失败: {e}")
            await asyncio.sleep(1)

    async def explore_main_menu_enhanced(self, menu_name):
        """增强版主菜单探索"""
        logger.info(f"开始探索主菜单: {menu_name}")

        try:
            # 确保在主页面
            await self.safe_operation(self.navigate_to_dashboard)

            # 检查菜单是否已展开
            if self.current_expanded_menu == menu_name:
                logger.info(f"菜单 {menu_name} 已展开")
            else:
                # 点击主菜单
                success = await self.safe_operation(self.smart_click_element, menu_name, "main_menu")
                if not success:
                    self.failed_attempts.append({
                        'menu': menu_name,
                        'type': 'main_menu',
                        'reason': 'Cannot click main menu',
                        'timestamp': datetime.now().isoformat()
                    })
                    return False

                # 等待菜单展开
                await self.wait_for_menu_animation()
                self.current_expanded_menu = menu_name

            # 截取展开后的菜单
            screenshot_name = f"enhanced_main_menu_{self.sanitize_filename(menu_name)}.png"
            await self.page.screenshot(path=self.screenshots_dir / screenshot_name)

            # 自动发现子菜单
            sub_menus = await self.discover_sub_menus(menu_name)

            # 探索子菜单
            explored_count = 0
            for sub_menu in sub_menus[:8]:  # 增加到8个子菜单
                success = await self.explore_sub_menu_enhanced(menu_name, sub_menu)
                if success:
                    explored_count += 1

                # 每次探索后短暂休息
                await asyncio.sleep(1)

            logger.info(f"主菜单 {menu_name} 探索完成，成功探索 {explored_count} 个子功能")
            return explored_count > 0

        except Exception as e:
            logger.error(f"探索主菜单 {menu_name} 时出错: {e}")
            self.failed_attempts.append({
                'menu': menu_name,
                'type': 'main_menu',
                'reason': str(e),
                'timestamp': datetime.now().isoformat()
            })
            return False

    async def navigate_to_dashboard(self):
        """导航到仪表板"""
        try:
            dashboard_url = f"{self.base_url}/statistic/bill/board"
            await self.page.goto(dashboard_url, wait_until='networkidle')
            await asyncio.sleep(2)
            self.current_expanded_menu = None  # 重置菜单状态
            return True
        except Exception as e:
            logger.error(f"导航到仪表板失败: {e}")
            return False

    async def discover_sub_menus(self, parent_menu):
        """智能发现子菜单"""
        try:
            logger.info(f"发现 {parent_menu} 的子菜单...")

            # 多种子菜单选择器
            sub_menu_selectors = [
                '.el-menu--inline .el-menu-item',
                '.el-submenu .el-menu-item',
                '.submenu-item',
                '.menu-item',
                'li[role="menuitem"]',
                '.nav-item'
            ]

            discovered_menus = set()

            for selector in sub_menu_selectors:
                try:
                    elements = await self.page.query_selector_all(selector)

                    for element in elements:
                        try:
                            # 检查元素是否可见
                            if not await element.is_visible():
                                continue

                            text = await element.inner_text()
                            if text and text.strip() and len(text.strip()) > 1:
                                clean_text = text.strip()

                                # 过滤掉明显不是菜单项的文本
                                if (len(clean_text) < 20 and
                                    not any(char in clean_text for char in ['(', ')', '[', ']', '{', '}']) and
                                    not clean_text.isdigit()):
                                    discovered_menus.add(clean_text)

                        except Exception as e:
                            logger.debug(f"处理子菜单元素时出错: {e}")
                            continue

                except Exception as e:
                    logger.debug(f"使用选择器 {selector} 时出错: {e}")
                    continue

            # 转换为列表并排序
            sub_menus = sorted(list(discovered_menus))
            logger.info(f"在 {parent_menu} 中发现 {len(sub_menus)} 个子菜单: {sub_menus}")

            return sub_menus

        except Exception as e:
            logger.error(f"发现子菜单时出错: {e}")
            return []

    async def explore_sub_menu_enhanced(self, parent_menu, sub_menu_name):
        """增强版子菜单探索"""
        logger.info(f"探索子菜单: {parent_menu} -> {sub_menu_name}")

        try:
            # 确保主菜单已展开
            if self.current_expanded_menu != parent_menu:
                await self.safe_operation(self.smart_click_element, parent_menu, "main_menu")
                await self.wait_for_menu_animation()
                self.current_expanded_menu = parent_menu

            # 点击子菜单
            success = await self.safe_operation(self.smart_click_element, sub_menu_name, "sub_menu")
            if not success:
                self.failed_attempts.append({
                    'menu': f"{parent_menu} -> {sub_menu_name}",
                    'type': 'sub_menu',
                    'reason': 'Cannot click sub menu',
                    'timestamp': datetime.now().isoformat()
                })
                return False

            # 等待页面加载
            await self.page.wait_for_load_state('networkidle')
            await asyncio.sleep(2)

            # 截图
            screenshot_name = f"enhanced_sub_menu_{self.sanitize_filename(parent_menu)}_{self.sanitize_filename(sub_menu_name)}.png"
            await self.page.screenshot(path=self.screenshots_dir / screenshot_name)

            # 记录功能信息
            function_info = {
                'parent_menu': parent_menu,
                'sub_menu': sub_menu_name,
                'url': self.page.url,
                'title': await self.page.title(),
                'screenshot': screenshot_name,
                'timestamp': datetime.now().isoformat(),
                'exploration_method': 'enhanced'
            }

            # 详细分析页面内容
            await self.analyze_page_content_comprehensive(function_info)

            self.explored_functions.append(function_info)
            logger.info(f"成功探索: {parent_menu} -> {sub_menu_name}")
            return True

        except Exception as e:
            logger.error(f"探索子菜单失败: {parent_menu} -> {sub_menu_name}, 错误: {e}")
            self.failed_attempts.append({
                'menu': f"{parent_menu} -> {sub_menu_name}",
                'type': 'sub_menu',
                'reason': str(e),
                'timestamp': datetime.now().isoformat()
            })
            return False

    async def analyze_page_content_comprehensive(self, function_info):
        """全面分析页面内容"""
        try:
            # 基础元素统计
            buttons = await self.page.query_selector_all('button, .btn, input[type="button"], input[type="submit"]')
            tables = await self.page.query_selector_all('table, .table, .el-table')
            forms = await self.page.query_selector_all('form, .form, .el-form')
            inputs = await self.page.query_selector_all('input, .input, .el-input')

            # 获取按钮文本和功能
            button_analysis = []
            for button in buttons[:15]:  # 增加到15个按钮
                try:
                    text = await button.inner_text()
                    if text and text.strip():
                        button_type = await button.get_attribute('type')
                        class_name = await button.get_attribute('class')
                        button_analysis.append({
                            'text': text.strip(),
                            'type': button_type,
                            'class': class_name
                        })
                except:
                    continue

            # 获取表格信息
            table_analysis = []
            for table in tables[:5]:  # 分析前5个表格
                try:
                    headers = await table.query_selector_all('th')
                    rows = await table.query_selector_all('tr')

                    header_texts = []
                    for header in headers[:10]:  # 前10个表头
                        try:
                            text = await header.inner_text()
                            if text and text.strip():
                                header_texts.append(text.strip())
                        except:
                            continue

                    table_analysis.append({
                        'headers': header_texts,
                        'header_count': len(header_texts),
                        'row_count': len(rows)
                    })
                except:
                    continue

            # 页面功能特征检测
            features = {
                'has_search': bool(await self.page.query_selector('input[type="search"], .search, input[placeholder*="搜索"], input[placeholder*="查询"]')),
                'has_pagination': bool(await self.page.query_selector('.pagination, .page, .el-pagination')),
                'has_add_button': any('新增' in btn['text'] or '添加' in btn['text'] or '创建' in btn['text'] for btn in button_analysis),
                'has_export_button': any('导出' in btn['text'] or '下载' in btn['text'] for btn in button_analysis),
                'has_edit_button': any('编辑' in btn['text'] or '修改' in btn['text'] for btn in button_analysis),
                'has_delete_button': any('删除' in btn['text'] for btn in button_analysis),
                'has_filter': bool(await self.page.query_selector('.filter, .el-filter, select')),
                'has_tabs': bool(await self.page.query_selector('.tab, .el-tabs')),
                'has_modal': bool(await self.page.query_selector('.modal, .el-dialog')),
                'has_upload': bool(await self.page.query_selector('input[type="file"], .upload'))
            }

            # 页面内容摘要
            try:
                main_content = await self.page.query_selector('.main-content, .content, .page-content, main, .container')
                content_text = ""
                if main_content:
                    content_text = await main_content.inner_text()
                    content_text = content_text[:300] if content_text else ""  # 限制长度
            except:
                content_text = ""

            # 综合分析结果
            content_analysis = {
                'elements': {
                    'buttons_count': len(buttons),
                    'tables_count': len(tables),
                    'forms_count': len(forms),
                    'inputs_count': len(inputs)
                },
                'buttons': button_analysis,
                'tables': table_analysis,
                'features': features,
                'page_title': await self.page.title(),
                'content_preview': content_text,
                'url_path': self.page.url.replace(self.base_url, ''),
                'analysis_timestamp': datetime.now().isoformat()
            }

            function_info['content_analysis'] = content_analysis

        except Exception as e:
            logger.error(f"分析页面内容时出错: {e}")
            function_info['content_analysis'] = {'error': str(e)}

    def sanitize_filename(self, filename):
        """清理文件名"""
        filename = re.sub(r'[<>:"/\\|?*]', '_', filename)
        filename = filename.replace(' ', '_')
        return filename[:30]

    async def run_comprehensive_exploration(self):
        """运行全面增强探索"""
        logger.info("开始Boss3系统全面增强探索...")

        try:
            # 启动浏览器会话
            if not await self.start_browser_session():
                logger.error("无法启动浏览器会话")
                return False

            # 登录系统
            if not await self.safe_operation(self.login):
                logger.error("登录失败")
                return False

            # 初始截图
            await self.page.screenshot(path=self.screenshots_dir / "enhanced_00_initial_dashboard.png")

            # 探索所有主菜单
            successful_menus = 0
            total_sub_functions = 0

            for menu_name in self.main_menus:
                try:
                    logger.info(f"开始探索菜单: {menu_name}")
                    success = await self.explore_main_menu_enhanced(menu_name)

                    if success:
                        successful_menus += 1

                    # 统计已探索的子功能数量
                    menu_sub_functions = len([f for f in self.explored_functions if f['parent_menu'] == menu_name])
                    total_sub_functions += menu_sub_functions

                    logger.info(f"菜单 {menu_name}: {'成功' if success else '失败'}, 子功能: {menu_sub_functions}")

                    # 每个主菜单之间休息
                    await asyncio.sleep(2)

                except Exception as e:
                    logger.error(f"探索菜单 {menu_name} 时出现异常: {e}")
                    continue

            # 保存探索结果
            await self.save_enhanced_results()

            # 打印统计信息
            self.print_enhanced_statistics(successful_menus, total_sub_functions)

            return True

        except Exception as e:
            logger.error(f"探索过程中出现错误: {e}")
            return False
        finally:
            await self.close_session()

    async def save_enhanced_results(self):
        """保存增强探索结果"""
        timestamp = datetime.now().isoformat()

        # 保存成功探索的功能
        with open(self.data_dir / "enhanced_exploration_results.json", 'w', encoding='utf-8') as f:
            json.dump(self.explored_functions, f, ensure_ascii=False, indent=2)

        # 保存失败尝试
        with open(self.data_dir / "enhanced_failed_attempts.json", 'w', encoding='utf-8') as f:
            json.dump(self.failed_attempts, f, ensure_ascii=False, indent=2)

        # 生成增强探索报告
        report = {
            'exploration_time': timestamp,
            'exploration_method': 'enhanced',
            'total_main_menus': len(self.main_menus),
            'successful_main_menus': len(set(f['parent_menu'] for f in self.explored_functions)),
            'total_sub_functions_explored': len(self.explored_functions),
            'total_screenshots': len(list(self.screenshots_dir.glob("enhanced_*.png"))),
            'failed_attempts_count': len(self.failed_attempts),
            'coverage_rate': len(set(f['parent_menu'] for f in self.explored_functions)) / len(self.main_menus),
            'success_metrics': {
                'main_menu_success_rate': len(set(f['parent_menu'] for f in self.explored_functions)) / len(self.main_menus),
                'average_sub_functions_per_menu': len(self.explored_functions) / max(1, len(set(f['parent_menu'] for f in self.explored_functions))),
                'total_pages_analyzed': len(self.explored_functions)
            },
            'coverage_by_menu': {}
        }

        # 计算每个菜单的详细覆盖情况
        for menu_name in self.main_menus:
            explored_subs = [f for f in self.explored_functions if f['parent_menu'] == menu_name]

            report['coverage_by_menu'][menu_name] = {
                'explored': len(explored_subs) > 0,
                'sub_function_count': len(explored_subs),
                'sub_functions': [f['sub_menu'] for f in explored_subs],
                'screenshots': [f['screenshot'] for f in explored_subs]
            }

        with open(self.data_dir / "enhanced_exploration_report.json", 'w', encoding='utf-8') as f:
            json.dump(report, f, ensure_ascii=False, indent=2)

        logger.info(f"增强探索结果已保存到 {self.data_dir}")

    def print_enhanced_statistics(self, successful_menus, total_sub_functions):
        """打印增强探索统计信息"""
        coverage_rate = successful_menus / len(self.main_menus) * 100

        print(f"\n{'='*60}")
        print(f"🎉 Boss3系统增强探索完成！")
        print(f"{'='*60}")
        print(f"📊 探索统计:")
        print(f"   成功探索的主菜单: {successful_menus}/{len(self.main_menus)} ({coverage_rate:.1f}%)")
        print(f"   成功探索的子功能: {total_sub_functions}")
        print(f"   生成的截图数量: {len(list(self.screenshots_dir.glob('enhanced_*.png')))}")
        print(f"   失败尝试次数: {len(self.failed_attempts)}")

        print(f"\n📈 覆盖率详情:")
        for menu_name in self.main_menus:
            menu_functions = [f for f in self.explored_functions if f['parent_menu'] == menu_name]
            status = "✅" if menu_functions else "❌"
            print(f"   {status} {menu_name}: {len(menu_functions)} 个子功能")

        # 质量评级
        if coverage_rate >= 90:
            grade = "🏆 优秀"
        elif coverage_rate >= 70:
            grade = "🥈 良好"
        elif coverage_rate >= 50:
            grade = "🥉 中等"
        else:
            grade = "⚠️ 需改进"

        print(f"\n🎯 探索质量评级: {grade}")

        if coverage_rate >= 90:
            print("🎊 恭喜！已达到90%以上的覆盖率目标！")
        elif coverage_rate >= 50:
            print("💪 探索效果显著改善，继续努力达到90%目标！")
        else:
            print("🔧 建议进一步优化探索策略")

    async def close_session(self):
        """关闭浏览器会话"""
        try:
            if self.browser:
                await self.browser.close()
                self.session_active = False
                logger.info("浏览器会话已关闭")
        except Exception as e:
            logger.error(f"关闭会话时出错: {e}")

async def main():
    """主函数"""
    explorer = Boss3EnhancedExplorer()
    success = await explorer.run_comprehensive_exploration()

    if success:
        print("\n🎉 Boss3系统增强探索成功完成！")
    else:
        print("\n❌ Boss3系统增强探索失败！")

if __name__ == "__main__":
    asyncio.run(main())
