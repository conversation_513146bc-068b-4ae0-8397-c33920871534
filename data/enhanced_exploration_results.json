[{"parent_menu": "数据看板", "sub_menu": "业务管理", "url": "http://csboss.lianquan.org.cn/statistic/bill/board", "title": "基金会业务管理系统", "screenshot": "enhanced_sub_menu_数据看板_业务管理.png", "timestamp": "2025-06-30T17:31:56.248630", "exploration_method": "enhanced", "content_analysis": {"elements": {"buttons_count": 2, "tables_count": 0, "forms_count": 0, "inputs_count": 5}, "buttons": [], "tables": [], "features": {"has_search": false, "has_pagination": false, "has_add_button": false, "has_export_button": false, "has_edit_button": false, "has_delete_button": false, "has_filter": false, "has_tabs": false, "has_modal": true, "has_upload": false}, "page_title": "基金会业务管理系统", "content_preview": "", "url_path": "/statistic/bill/board", "analysis_timestamp": "2025-06-30T17:31:56.273828"}}, {"parent_menu": "数据看板", "sub_menu": "可变配置", "url": "http://csboss.lianquan.org.cn/statistic/bill/board", "title": "基金会业务管理系统", "screenshot": "enhanced_sub_menu_数据看板_可变配置.png", "timestamp": "2025-06-30T17:32:08.636945", "exploration_method": "enhanced", "content_analysis": {"elements": {"buttons_count": 2, "tables_count": 0, "forms_count": 0, "inputs_count": 5}, "buttons": [], "tables": [], "features": {"has_search": false, "has_pagination": false, "has_add_button": false, "has_export_button": false, "has_edit_button": false, "has_delete_button": false, "has_filter": false, "has_tabs": false, "has_modal": true, "has_upload": false}, "page_title": "基金会业务管理系统", "content_preview": "", "url_path": "/statistic/bill/board", "analysis_timestamp": "2025-06-30T17:32:08.659501"}}, {"parent_menu": "数据看板", "sub_menu": "合作方管理", "url": "http://csboss.lianquan.org.cn/ins", "title": "基金会业务管理系统", "screenshot": "enhanced_sub_menu_数据看板_合作方管理.png", "timestamp": "2025-06-30T17:32:21.087540", "exploration_method": "enhanced", "content_analysis": {"elements": {"buttons_count": 85, "tables_count": 7, "forms_count": 1, "inputs_count": 23}, "buttons": [{"text": "相关说明", "type": "button", "class": "el-button middle-space el-button--primary el-button--small"}, {"text": "审核者设置", "type": "button", "class": "el-button el-button--default el-button--small"}, {"text": "新增", "type": "button", "class": "el-button middle-space add-button el-button--primary el-button--small"}, {"text": "查询", "type": "button", "class": "el-button middle-space el-button--primary el-button--small"}, {"text": "重置", "type": "button", "class": "el-button middle-space el-button--default el-button--small"}, {"text": "导出", "type": "button", "class": "el-button middle-space export-btn el-button--default el-button--small"}], "tables": [{"headers": ["合作方编号", "合作方类型", "机构名称", "机构对接人姓名", "合作期间", "合作状态", "累计合作项目数量", "进行中的合作项目", "标签"], "header_count": 9, "row_count": 63}, {"headers": ["合作方编号", "合作方类型", "机构名称", "机构对接人姓名", "合作期间", "合作状态", "累计合作项目数量", "进行中的合作项目", "标签"], "header_count": 9, "row_count": 1}, {"headers": [], "header_count": 0, "row_count": 20}, {"headers": ["序号"], "header_count": 1, "row_count": 1}, {"headers": [], "header_count": 0, "row_count": 20}], "features": {"has_search": true, "has_pagination": true, "has_add_button": true, "has_export_button": true, "has_edit_button": false, "has_delete_button": false, "has_filter": false, "has_tabs": true, "has_modal": true, "has_upload": false}, "page_title": "基金会业务管理系统", "content_preview": "社会组织个人类型企业类型其他组织\n合作方类型：\n机构名称：\n对接人姓名：\n合作状态：\n标签：\n联劝对接人：\n相关说明 审核者设置 新增 查询 重置 导出\n\t合作方编号\t合作方类型\t机构名称\t机构对接人姓名\t合作期间\t合作状态\t累计合作项目数量\t进行中的合作项目\t标签\t联劝对接人\t\t\n\t\nINSGG202500592\n\t\n--\n\t\n福建省担当者行动教育基金会2\n\t\n安庆旭\n\t\n~\n\t\n未合作\n\t\n--\n\t\n--\n\t\n--\n\t\n--\n\t\n\t\nINSGG202500910\n\t\n执行方\n\t\n佳县慈爱社会公益事业服务中心\n\t\n屈红艳\n\t\n~\n\t\n未合作\n\t\n--\n\t\n--\n\t\n--\n\t\n--\n\t\n\t\nI", "url_path": "/ins", "analysis_timestamp": "2025-06-30T17:32:21.293233"}}, {"parent_menu": "数据看板", "sub_menu": "合同管理", "url": "http://csboss.lianquan.org.cn/contract", "title": "基金会业务管理系统", "screenshot": "enhanced_sub_menu_数据看板_合同管理.png", "timestamp": "2025-06-30T17:32:33.727057", "exploration_method": "enhanced", "content_analysis": {"elements": {"buttons_count": 82, "tables_count": 7, "forms_count": 1, "inputs_count": 24}, "buttons": [{"text": "提醒设置", "type": "button", "class": "el-button left el-button--small"}, {"text": "新增", "type": "button", "class": "el-button left middle-space add-button el-button--primary el-button--small"}, {"text": "导出n天内即将过期的合同", "type": "button", "class": "el-button el-button--default el-button--small"}, {"text": "查询", "type": "button", "class": "el-button middle-space el-button--primary el-button--small"}, {"text": "重置", "type": "button", "class": "el-button middle-space el-button--default el-button--small"}, {"text": "导出", "type": "button", "class": "el-button middle-space export-btn el-button--default el-button--small"}, {"text": "更多", "type": "button", "class": "el-button middle-space el-button--default el-button--small"}], "tables": [{"headers": ["合同编号", "合同名称", "合同类型", "合同类别", "签约单位", "合同金额", "合同状态【流程节点】审核人", "申请人", "申请部门"], "header_count": 9, "row_count": 63}, {"headers": ["合同编号", "合同名称", "合同类型", "合同类别", "签约单位", "合同金额", "合同状态【流程节点】审核人", "申请人", "申请部门"], "header_count": 9, "row_count": 1}, {"headers": [], "header_count": 0, "row_count": 20}, {"headers": ["序号"], "header_count": 1, "row_count": 1}, {"headers": [], "header_count": 0, "row_count": 20}], "features": {"has_search": false, "has_pagination": true, "has_add_button": true, "has_export_button": true, "has_edit_button": false, "has_delete_button": false, "has_filter": false, "has_tabs": true, "has_modal": true, "has_upload": false}, "page_title": "基金会业务管理系统", "content_preview": "合同管理作废审批记录\n合同编号：\n合同名称：\n合同类型：\n合同类别：\n合同状态：\n签约单位：\n提醒设置\n新增\n导出n天内即将过期的合同 查询 重置 导出 更多\n\t合同编号\t合同名称\t合同类型\t合同类别\t签约单位\t合同金额\n\t合同状态【流程节点】审核人\t申请人\t申请部门\t申请日期\t签约有效期\t标签\t\t\n\t\nHT2025999\n\t\n资助协议\n\t\n付款\n\t\n资助协议\n\t\n佛吉亚（中国）投资有限公司，无锡市德盛社会工作服务中心\n\t\n118,000.00\n\t\n草稿\n\t\n何松\n\t\n基金会\n\t\n--\n\t\n2021-11-30~2022-08-31\n\t\n--\n\t\n\t\nHT2025998\n\t\n资助协议\n\t\n付款", "url_path": "/contract", "analysis_timestamp": "2025-06-30T17:32:33.926739"}}, {"parent_menu": "数据看板", "sub_menu": "安全审计", "url": "http://csboss.lianquan.org.cn/contract", "title": "基金会业务管理系统", "screenshot": "enhanced_sub_menu_数据看板_安全审计.png", "timestamp": "2025-06-30T17:32:46.253264", "exploration_method": "enhanced", "content_analysis": {"elements": {"buttons_count": 82, "tables_count": 7, "forms_count": 1, "inputs_count": 24}, "buttons": [{"text": "提醒设置", "type": "button", "class": "el-button left el-button--small"}, {"text": "新增", "type": "button", "class": "el-button left middle-space add-button el-button--primary el-button--small"}, {"text": "导出n天内即将过期的合同", "type": "button", "class": "el-button el-button--default el-button--small"}, {"text": "查询", "type": "button", "class": "el-button middle-space el-button--primary el-button--small"}, {"text": "重置", "type": "button", "class": "el-button middle-space el-button--default el-button--small"}, {"text": "导出", "type": "button", "class": "el-button middle-space export-btn el-button--default el-button--small"}, {"text": "更多", "type": "button", "class": "el-button middle-space el-button--default el-button--small"}], "tables": [{"headers": ["合同编号", "合同名称", "合同类型", "合同类别", "签约单位", "合同金额", "合同状态【流程节点】审核人", "申请人", "申请部门"], "header_count": 9, "row_count": 63}, {"headers": ["合同编号", "合同名称", "合同类型", "合同类别", "签约单位", "合同金额", "合同状态【流程节点】审核人", "申请人", "申请部门"], "header_count": 9, "row_count": 1}, {"headers": [], "header_count": 0, "row_count": 20}, {"headers": ["序号"], "header_count": 1, "row_count": 1}, {"headers": [], "header_count": 0, "row_count": 20}], "features": {"has_search": false, "has_pagination": true, "has_add_button": true, "has_export_button": true, "has_edit_button": false, "has_delete_button": false, "has_filter": false, "has_tabs": true, "has_modal": true, "has_upload": false}, "page_title": "基金会业务管理系统", "content_preview": "合同管理作废审批记录\n合同编号：\n合同名称：\n合同类型：\n合同类别：\n合同状态：\n签约单位：\n提醒设置\n新增\n导出n天内即将过期的合同 查询 重置 导出 更多\n\t合同编号\t合同名称\t合同类型\t合同类别\t签约单位\t合同金额\n\t合同状态【流程节点】审核人\t申请人\t申请部门\t申请日期\t签约有效期\t标签\t\t\n\t\nHT2025999\n\t\n资助协议\n\t\n付款\n\t\n资助协议\n\t\n佛吉亚（中国）投资有限公司，无锡市德盛社会工作服务中心\n\t\n118,000.00\n\t\n草稿\n\t\n何松\n\t\n基金会\n\t\n--\n\t\n2021-11-30~2022-08-31\n\t\n--\n\t\n\t\nHT2025998\n\t\n资助协议\n\t\n付款", "url_path": "/contract", "analysis_timestamp": "2025-06-30T17:32:46.447794"}}, {"parent_menu": "数据看板", "sub_menu": "开票管理", "url": "http://csboss.lianquan.org.cn/contract", "title": "基金会业务管理系统", "screenshot": "enhanced_sub_menu_数据看板_开票管理.png", "timestamp": "2025-06-30T17:32:58.770872", "exploration_method": "enhanced", "content_analysis": {"elements": {"buttons_count": 82, "tables_count": 7, "forms_count": 1, "inputs_count": 24}, "buttons": [{"text": "提醒设置", "type": "button", "class": "el-button left el-button--small"}, {"text": "新增", "type": "button", "class": "el-button left middle-space add-button el-button--primary el-button--small"}, {"text": "导出n天内即将过期的合同", "type": "button", "class": "el-button el-button--default el-button--small"}, {"text": "查询", "type": "button", "class": "el-button middle-space el-button--primary el-button--small"}, {"text": "重置", "type": "button", "class": "el-button middle-space el-button--default el-button--small"}, {"text": "导出", "type": "button", "class": "el-button middle-space export-btn el-button--default el-button--small"}, {"text": "更多", "type": "button", "class": "el-button middle-space el-button--default el-button--small"}], "tables": [{"headers": ["合同编号", "合同名称", "合同类型", "合同类别", "签约单位", "合同金额", "合同状态【流程节点】审核人", "申请人", "申请部门"], "header_count": 9, "row_count": 63}, {"headers": ["合同编号", "合同名称", "合同类型", "合同类别", "签约单位", "合同金额", "合同状态【流程节点】审核人", "申请人", "申请部门"], "header_count": 9, "row_count": 1}, {"headers": [], "header_count": 0, "row_count": 20}, {"headers": ["序号"], "header_count": 1, "row_count": 1}, {"headers": [], "header_count": 0, "row_count": 20}], "features": {"has_search": false, "has_pagination": true, "has_add_button": true, "has_export_button": true, "has_edit_button": false, "has_delete_button": false, "has_filter": false, "has_tabs": true, "has_modal": true, "has_upload": false}, "page_title": "基金会业务管理系统", "content_preview": "合同管理作废审批记录\n合同编号：\n合同名称：\n合同类型：\n合同类别：\n合同状态：\n签约单位：\n提醒设置\n新增\n导出n天内即将过期的合同 查询 重置 导出 更多\n\t合同编号\t合同名称\t合同类型\t合同类别\t签约单位\t合同金额\n\t合同状态【流程节点】审核人\t申请人\t申请部门\t申请日期\t签约有效期\t标签\t\t\n\t\nHT2025999\n\t\n资助协议\n\t\n付款\n\t\n资助协议\n\t\n佛吉亚（中国）投资有限公司，无锡市德盛社会工作服务中心\n\t\n118,000.00\n\t\n草稿\n\t\n何松\n\t\n基金会\n\t\n--\n\t\n2021-11-30~2022-08-31\n\t\n--\n\t\n\t\nHT2025998\n\t\n资助协议\n\t\n付款", "url_path": "/contract", "analysis_timestamp": "2025-06-30T17:32:58.960746"}}, {"parent_menu": "数据看板", "sub_menu": "捐方管理", "url": "http://csboss.lianquan.org.cn/donor/manage", "title": "基金会业务管理系统", "screenshot": "enhanced_sub_menu_数据看板_捐方管理.png", "timestamp": "2025-06-30T17:33:11.343475", "exploration_method": "enhanced", "content_analysis": {"elements": {"buttons_count": 15, "tables_count": 7, "forms_count": 1, "inputs_count": 24}, "buttons": [{"text": "新增", "type": "button", "class": "el-button left middle-space add-button el-button--primary el-button--small"}, {"text": "查询", "type": "button", "class": "el-button middle-space el-button--primary el-button--small"}, {"text": "重置", "type": "button", "class": "el-button middle-space el-button--default el-button--small"}, {"text": "导出", "type": "button", "class": "el-button middle-space export-btn el-button--default el-button--small"}, {"text": "更多", "type": "button", "class": "el-button middle-space el-button--default el-button--small"}, {"text": "关闭", "type": "button", "class": "el-button el-button--default el-button--small"}, {"text": "确定", "type": "button", "class": "el-button el-button--primary el-button--small"}], "tables": [{"headers": ["捐方编号", "捐方名称", "捐方简称", "所属小组", "行业类别", "捐方规模", "年营业收入", "企业性质", "上市状态"], "header_count": 9, "row_count": 3}, {"headers": ["捐方编号", "捐方名称", "捐方简称", "所属小组", "行业类别", "捐方规模", "年营业收入", "企业性质", "上市状态"], "header_count": 9, "row_count": 1}, {"headers": [], "header_count": 0, "row_count": 0}, {"headers": ["序号"], "header_count": 1, "row_count": 1}, {"headers": [], "header_count": 0, "row_count": 0}], "features": {"has_search": false, "has_pagination": true, "has_add_button": true, "has_export_button": true, "has_edit_button": false, "has_delete_button": false, "has_filter": false, "has_tabs": true, "has_modal": true, "has_upload": false}, "page_title": "基金会业务管理系统", "content_preview": "企业类型社会组织其他组织个人\n捐方编号：\n捐方名称：\n捐方简称：\n所属小组：\n行业类别：\n捐方规模：\n年营业收入：\n新增\n查询 重置 导出 更多\n\t捐方编号\t捐方名称\t捐方简称\t所属小组\t行业类别\t捐方规模\t年营业收入\t企业性质\t上市状态\t企业成立日期\t业务覆盖范围\t联系地址\t标签\t联系人姓名\t累计管理费金额\t单笔捐赠最大金额\n\t累计捐赠金额\t累计管理费比例\t最新捐赠日期\t累计捐赠项目数量\n\t资助最多次项目名称\t\n\n暂无记录，请更换条件重新搜索\n\n序号\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t操作\n共 0 条\n1\n前往页", "url_path": "/donor/manage", "analysis_timestamp": "2025-06-30T17:33:11.490518"}}, {"parent_menu": "数据看板", "sub_menu": "支出管理", "url": "http://csboss.lianquan.org.cn/donor/manage", "title": "基金会业务管理系统", "screenshot": "enhanced_sub_menu_数据看板_支出管理.png", "timestamp": "2025-06-30T17:33:23.794872", "exploration_method": "enhanced", "content_analysis": {"elements": {"buttons_count": 15, "tables_count": 7, "forms_count": 1, "inputs_count": 24}, "buttons": [{"text": "新增", "type": "button", "class": "el-button left middle-space add-button el-button--primary el-button--small"}, {"text": "查询", "type": "button", "class": "el-button middle-space el-button--primary el-button--small"}, {"text": "重置", "type": "button", "class": "el-button middle-space el-button--default el-button--small"}, {"text": "导出", "type": "button", "class": "el-button middle-space export-btn el-button--default el-button--small"}, {"text": "更多", "type": "button", "class": "el-button middle-space el-button--default el-button--small"}, {"text": "关闭", "type": "button", "class": "el-button el-button--default el-button--small"}, {"text": "确定", "type": "button", "class": "el-button el-button--primary el-button--small"}], "tables": [{"headers": ["捐方编号", "捐方名称", "捐方简称", "所属小组", "行业类别", "捐方规模", "年营业收入", "企业性质", "上市状态"], "header_count": 9, "row_count": 3}, {"headers": ["捐方编号", "捐方名称", "捐方简称", "所属小组", "行业类别", "捐方规模", "年营业收入", "企业性质", "上市状态"], "header_count": 9, "row_count": 1}, {"headers": [], "header_count": 0, "row_count": 0}, {"headers": ["序号"], "header_count": 1, "row_count": 1}, {"headers": [], "header_count": 0, "row_count": 0}], "features": {"has_search": false, "has_pagination": true, "has_add_button": true, "has_export_button": true, "has_edit_button": false, "has_delete_button": false, "has_filter": false, "has_tabs": true, "has_modal": true, "has_upload": false}, "page_title": "基金会业务管理系统", "content_preview": "企业类型社会组织其他组织个人\n捐方编号：\n捐方名称：\n捐方简称：\n所属小组：\n行业类别：\n捐方规模：\n年营业收入：\n新增\n查询 重置 导出 更多\n\t捐方编号\t捐方名称\t捐方简称\t所属小组\t行业类别\t捐方规模\t年营业收入\t企业性质\t上市状态\t企业成立日期\t业务覆盖范围\t联系地址\t标签\t联系人姓名\t累计管理费金额\t单笔捐赠最大金额\n\t累计捐赠金额\t累计管理费比例\t最新捐赠日期\t累计捐赠项目数量\n\t资助最多次项目名称\t\n\n暂无记录，请更换条件重新搜索\n\n序号\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t操作\n共 0 条\n1\n前往页", "url_path": "/donor/manage", "analysis_timestamp": "2025-06-30T17:33:23.942005"}}, {"parent_menu": "收入管理", "sub_menu": "业务管理", "url": "http://csboss.lianquan.org.cn/statistic/bill/board", "title": "基金会业务管理系统", "screenshot": "enhanced_sub_menu_收入管理_业务管理.png", "timestamp": "2025-06-30T17:33:46.313101", "exploration_method": "enhanced", "content_analysis": {"elements": {"buttons_count": 2, "tables_count": 0, "forms_count": 0, "inputs_count": 5}, "buttons": [], "tables": [], "features": {"has_search": false, "has_pagination": false, "has_add_button": false, "has_export_button": false, "has_edit_button": false, "has_delete_button": false, "has_filter": false, "has_tabs": false, "has_modal": true, "has_upload": false}, "page_title": "基金会业务管理系统", "content_preview": "", "url_path": "/statistic/bill/board", "analysis_timestamp": "2025-06-30T17:33:46.334132"}}, {"parent_menu": "收入管理", "sub_menu": "可变配置", "url": "http://csboss.lianquan.org.cn/statistic/bill/board", "title": "基金会业务管理系统", "screenshot": "enhanced_sub_menu_收入管理_可变配置.png", "timestamp": "2025-06-30T17:33:58.693758", "exploration_method": "enhanced", "content_analysis": {"elements": {"buttons_count": 2, "tables_count": 0, "forms_count": 0, "inputs_count": 5}, "buttons": [], "tables": [], "features": {"has_search": false, "has_pagination": false, "has_add_button": false, "has_export_button": false, "has_edit_button": false, "has_delete_button": false, "has_filter": false, "has_tabs": false, "has_modal": true, "has_upload": false}, "page_title": "基金会业务管理系统", "content_preview": "", "url_path": "/statistic/bill/board", "analysis_timestamp": "2025-06-30T17:33:58.713949"}}, {"parent_menu": "收入管理", "sub_menu": "合作方管理", "url": "http://csboss.lianquan.org.cn/ins", "title": "基金会业务管理系统", "screenshot": "enhanced_sub_menu_收入管理_合作方管理.png", "timestamp": "2025-06-30T17:34:11.105852", "exploration_method": "enhanced", "content_analysis": {"elements": {"buttons_count": 85, "tables_count": 7, "forms_count": 1, "inputs_count": 23}, "buttons": [{"text": "相关说明", "type": "button", "class": "el-button middle-space el-button--primary el-button--small"}, {"text": "审核者设置", "type": "button", "class": "el-button el-button--default el-button--small"}, {"text": "新增", "type": "button", "class": "el-button middle-space add-button el-button--primary el-button--small"}, {"text": "查询", "type": "button", "class": "el-button middle-space el-button--primary el-button--small"}, {"text": "重置", "type": "button", "class": "el-button middle-space el-button--default el-button--small"}, {"text": "导出", "type": "button", "class": "el-button middle-space export-btn el-button--default el-button--small"}], "tables": [{"headers": ["合作方编号", "合作方类型", "机构名称", "机构对接人姓名", "合作期间", "合作状态", "累计合作项目数量", "进行中的合作项目", "标签"], "header_count": 9, "row_count": 63}, {"headers": ["合作方编号", "合作方类型", "机构名称", "机构对接人姓名", "合作期间", "合作状态", "累计合作项目数量", "进行中的合作项目", "标签"], "header_count": 9, "row_count": 1}, {"headers": [], "header_count": 0, "row_count": 20}, {"headers": ["序号"], "header_count": 1, "row_count": 1}, {"headers": [], "header_count": 0, "row_count": 20}], "features": {"has_search": true, "has_pagination": true, "has_add_button": true, "has_export_button": true, "has_edit_button": false, "has_delete_button": false, "has_filter": false, "has_tabs": true, "has_modal": true, "has_upload": false}, "page_title": "基金会业务管理系统", "content_preview": "社会组织个人类型企业类型其他组织\n合作方类型：\n机构名称：\n对接人姓名：\n合作状态：\n标签：\n联劝对接人：\n相关说明 审核者设置 新增 查询 重置 导出\n\t合作方编号\t合作方类型\t机构名称\t机构对接人姓名\t合作期间\t合作状态\t累计合作项目数量\t进行中的合作项目\t标签\t联劝对接人\t\t\n\t\nINSGG202500592\n\t\n--\n\t\n福建省担当者行动教育基金会2\n\t\n安庆旭\n\t\n~\n\t\n未合作\n\t\n--\n\t\n--\n\t\n--\n\t\n--\n\t\n\t\nINSGG202500910\n\t\n执行方\n\t\n佳县慈爱社会公益事业服务中心\n\t\n屈红艳\n\t\n~\n\t\n未合作\n\t\n--\n\t\n--\n\t\n--\n\t\n--\n\t\n\t\nI", "url_path": "/ins", "analysis_timestamp": "2025-06-30T17:34:11.311537"}}, {"parent_menu": "收入管理", "sub_menu": "合同管理", "url": "http://csboss.lianquan.org.cn/contract", "title": "基金会业务管理系统", "screenshot": "enhanced_sub_menu_收入管理_合同管理.png", "timestamp": "2025-06-30T17:34:23.694195", "exploration_method": "enhanced", "content_analysis": {"elements": {"buttons_count": 82, "tables_count": 7, "forms_count": 1, "inputs_count": 24}, "buttons": [{"text": "提醒设置", "type": "button", "class": "el-button left el-button--small"}, {"text": "新增", "type": "button", "class": "el-button left middle-space add-button el-button--primary el-button--small"}, {"text": "导出n天内即将过期的合同", "type": "button", "class": "el-button el-button--default el-button--small"}, {"text": "查询", "type": "button", "class": "el-button middle-space el-button--primary el-button--small"}, {"text": "重置", "type": "button", "class": "el-button middle-space el-button--default el-button--small"}, {"text": "导出", "type": "button", "class": "el-button middle-space export-btn el-button--default el-button--small"}, {"text": "更多", "type": "button", "class": "el-button middle-space el-button--default el-button--small"}], "tables": [{"headers": ["合同编号", "合同名称", "合同类型", "合同类别", "签约单位", "合同金额", "合同状态【流程节点】审核人", "申请人", "申请部门"], "header_count": 9, "row_count": 63}, {"headers": ["合同编号", "合同名称", "合同类型", "合同类别", "签约单位", "合同金额", "合同状态【流程节点】审核人", "申请人", "申请部门"], "header_count": 9, "row_count": 1}, {"headers": [], "header_count": 0, "row_count": 20}, {"headers": ["序号"], "header_count": 1, "row_count": 1}, {"headers": [], "header_count": 0, "row_count": 20}], "features": {"has_search": false, "has_pagination": true, "has_add_button": true, "has_export_button": true, "has_edit_button": false, "has_delete_button": false, "has_filter": false, "has_tabs": true, "has_modal": true, "has_upload": false}, "page_title": "基金会业务管理系统", "content_preview": "合同管理作废审批记录\n合同编号：\n合同名称：\n合同类型：\n合同类别：\n合同状态：\n签约单位：\n提醒设置\n新增\n导出n天内即将过期的合同 查询 重置 导出 更多\n\t合同编号\t合同名称\t合同类型\t合同类别\t签约单位\t合同金额\n\t合同状态【流程节点】审核人\t申请人\t申请部门\t申请日期\t签约有效期\t标签\t\t\n\t\nHT2025999\n\t\n资助协议\n\t\n付款\n\t\n资助协议\n\t\n佛吉亚（中国）投资有限公司，无锡市德盛社会工作服务中心\n\t\n118,000.00\n\t\n草稿\n\t\n何松\n\t\n基金会\n\t\n--\n\t\n2021-11-30~2022-08-31\n\t\n--\n\t\n\t\nHT2025998\n\t\n资助协议\n\t\n付款", "url_path": "/contract", "analysis_timestamp": "2025-06-30T17:34:23.902645"}}, {"parent_menu": "收入管理", "sub_menu": "安全审计", "url": "http://csboss.lianquan.org.cn/contract", "title": "基金会业务管理系统", "screenshot": "enhanced_sub_menu_收入管理_安全审计.png", "timestamp": "2025-06-30T17:34:36.235573", "exploration_method": "enhanced", "content_analysis": {"elements": {"buttons_count": 82, "tables_count": 7, "forms_count": 1, "inputs_count": 24}, "buttons": [{"text": "提醒设置", "type": "button", "class": "el-button left el-button--small"}, {"text": "新增", "type": "button", "class": "el-button left middle-space add-button el-button--primary el-button--small"}, {"text": "导出n天内即将过期的合同", "type": "button", "class": "el-button el-button--default el-button--small"}, {"text": "查询", "type": "button", "class": "el-button middle-space el-button--primary el-button--small"}, {"text": "重置", "type": "button", "class": "el-button middle-space el-button--default el-button--small"}, {"text": "导出", "type": "button", "class": "el-button middle-space export-btn el-button--default el-button--small"}, {"text": "更多", "type": "button", "class": "el-button middle-space el-button--default el-button--small"}], "tables": [{"headers": ["合同编号", "合同名称", "合同类型", "合同类别", "签约单位", "合同金额", "合同状态【流程节点】审核人", "申请人", "申请部门"], "header_count": 9, "row_count": 63}, {"headers": ["合同编号", "合同名称", "合同类型", "合同类别", "签约单位", "合同金额", "合同状态【流程节点】审核人", "申请人", "申请部门"], "header_count": 9, "row_count": 1}, {"headers": [], "header_count": 0, "row_count": 20}, {"headers": ["序号"], "header_count": 1, "row_count": 1}, {"headers": [], "header_count": 0, "row_count": 20}], "features": {"has_search": false, "has_pagination": true, "has_add_button": true, "has_export_button": true, "has_edit_button": false, "has_delete_button": false, "has_filter": false, "has_tabs": true, "has_modal": true, "has_upload": false}, "page_title": "基金会业务管理系统", "content_preview": "合同管理作废审批记录\n合同编号：\n合同名称：\n合同类型：\n合同类别：\n合同状态：\n签约单位：\n提醒设置\n新增\n导出n天内即将过期的合同 查询 重置 导出 更多\n\t合同编号\t合同名称\t合同类型\t合同类别\t签约单位\t合同金额\n\t合同状态【流程节点】审核人\t申请人\t申请部门\t申请日期\t签约有效期\t标签\t\t\n\t\nHT2025999\n\t\n资助协议\n\t\n付款\n\t\n资助协议\n\t\n佛吉亚（中国）投资有限公司，无锡市德盛社会工作服务中心\n\t\n118,000.00\n\t\n草稿\n\t\n何松\n\t\n基金会\n\t\n--\n\t\n2021-11-30~2022-08-31\n\t\n--\n\t\n\t\nHT2025998\n\t\n资助协议\n\t\n付款", "url_path": "/contract", "analysis_timestamp": "2025-06-30T17:34:36.421211"}}, {"parent_menu": "收入管理", "sub_menu": "开票管理", "url": "http://csboss.lianquan.org.cn/contract", "title": "基金会业务管理系统", "screenshot": "enhanced_sub_menu_收入管理_开票管理.png", "timestamp": "2025-06-30T17:34:48.740606", "exploration_method": "enhanced", "content_analysis": {"elements": {"buttons_count": 82, "tables_count": 7, "forms_count": 1, "inputs_count": 24}, "buttons": [{"text": "提醒设置", "type": "button", "class": "el-button left el-button--small"}, {"text": "新增", "type": "button", "class": "el-button left middle-space add-button el-button--primary el-button--small"}, {"text": "导出n天内即将过期的合同", "type": "button", "class": "el-button el-button--default el-button--small"}, {"text": "查询", "type": "button", "class": "el-button middle-space el-button--primary el-button--small"}, {"text": "重置", "type": "button", "class": "el-button middle-space el-button--default el-button--small"}, {"text": "导出", "type": "button", "class": "el-button middle-space export-btn el-button--default el-button--small"}, {"text": "更多", "type": "button", "class": "el-button middle-space el-button--default el-button--small"}], "tables": [{"headers": ["合同编号", "合同名称", "合同类型", "合同类别", "签约单位", "合同金额", "合同状态【流程节点】审核人", "申请人", "申请部门"], "header_count": 9, "row_count": 63}, {"headers": ["合同编号", "合同名称", "合同类型", "合同类别", "签约单位", "合同金额", "合同状态【流程节点】审核人", "申请人", "申请部门"], "header_count": 9, "row_count": 1}, {"headers": [], "header_count": 0, "row_count": 20}, {"headers": ["序号"], "header_count": 1, "row_count": 1}, {"headers": [], "header_count": 0, "row_count": 20}], "features": {"has_search": false, "has_pagination": true, "has_add_button": true, "has_export_button": true, "has_edit_button": false, "has_delete_button": false, "has_filter": false, "has_tabs": true, "has_modal": true, "has_upload": false}, "page_title": "基金会业务管理系统", "content_preview": "合同管理作废审批记录\n合同编号：\n合同名称：\n合同类型：\n合同类别：\n合同状态：\n签约单位：\n提醒设置\n新增\n导出n天内即将过期的合同 查询 重置 导出 更多\n\t合同编号\t合同名称\t合同类型\t合同类别\t签约单位\t合同金额\n\t合同状态【流程节点】审核人\t申请人\t申请部门\t申请日期\t签约有效期\t标签\t\t\n\t\nHT2025999\n\t\n资助协议\n\t\n付款\n\t\n资助协议\n\t\n佛吉亚（中国）投资有限公司，无锡市德盛社会工作服务中心\n\t\n118,000.00\n\t\n草稿\n\t\n何松\n\t\n基金会\n\t\n--\n\t\n2021-11-30~2022-08-31\n\t\n--\n\t\n\t\nHT2025998\n\t\n资助协议\n\t\n付款", "url_path": "/contract", "analysis_timestamp": "2025-06-30T17:34:48.926752"}}, {"parent_menu": "收入管理", "sub_menu": "捐方管理", "url": "http://csboss.lianquan.org.cn/donor/manage", "title": "基金会业务管理系统", "screenshot": "enhanced_sub_menu_收入管理_捐方管理.png", "timestamp": "2025-06-30T17:35:46.130295", "exploration_method": "enhanced", "content_analysis": {"elements": {"buttons_count": 15, "tables_count": 7, "forms_count": 1, "inputs_count": 24}, "buttons": [{"text": "新增", "type": "button", "class": "el-button left middle-space add-button el-button--primary el-button--small"}, {"text": "查询", "type": "button", "class": "el-button middle-space el-button--primary el-button--small"}, {"text": "重置", "type": "button", "class": "el-button middle-space el-button--default el-button--small"}, {"text": "导出", "type": "button", "class": "el-button middle-space export-btn el-button--default el-button--small"}, {"text": "更多", "type": "button", "class": "el-button middle-space el-button--default el-button--small"}, {"text": "关闭", "type": "button", "class": "el-button el-button--default el-button--small"}, {"text": "确定", "type": "button", "class": "el-button el-button--primary el-button--small"}], "tables": [{"headers": ["捐方编号", "捐方名称", "捐方简称", "所属小组", "行业类别", "捐方规模", "年营业收入", "企业性质", "上市状态"], "header_count": 9, "row_count": 3}, {"headers": ["捐方编号", "捐方名称", "捐方简称", "所属小组", "行业类别", "捐方规模", "年营业收入", "企业性质", "上市状态"], "header_count": 9, "row_count": 1}, {"headers": [], "header_count": 0, "row_count": 0}, {"headers": ["序号"], "header_count": 1, "row_count": 1}, {"headers": [], "header_count": 0, "row_count": 0}], "features": {"has_search": false, "has_pagination": true, "has_add_button": true, "has_export_button": true, "has_edit_button": false, "has_delete_button": false, "has_filter": false, "has_tabs": true, "has_modal": true, "has_upload": false}, "page_title": "基金会业务管理系统", "content_preview": "企业类型社会组织其他组织个人\n捐方编号：\n捐方名称：\n捐方简称：\n所属小组：\n行业类别：\n捐方规模：\n年营业收入：\n新增\n查询 重置 导出 更多\n\t捐方编号\t捐方名称\t捐方简称\t所属小组\t行业类别\t捐方规模\t年营业收入\t企业性质\t上市状态\t企业成立日期\t业务覆盖范围\t联系地址\t标签\t联系人姓名\t累计管理费金额\t单笔捐赠最大金额\n\t累计捐赠金额\t累计管理费比例\t最新捐赠日期\t累计捐赠项目数量\n\t资助最多次项目名称\t\n\n暂无记录，请更换条件重新搜索\n\n序号\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t操作\n共 0 条\n1\n前往页", "url_path": "/donor/manage", "analysis_timestamp": "2025-06-30T17:35:46.293159"}}, {"parent_menu": "资金池管理", "sub_menu": "业务管理", "url": "http://csboss.lianquan.org.cn/statistic/bill/board", "title": "基金会业务管理系统", "screenshot": "enhanced_sub_menu_资金池管理_业务管理.png", "timestamp": "2025-06-30T17:36:08.645278", "exploration_method": "enhanced", "content_analysis": {"elements": {"buttons_count": 2, "tables_count": 0, "forms_count": 0, "inputs_count": 5}, "buttons": [], "tables": [], "features": {"has_search": false, "has_pagination": false, "has_add_button": false, "has_export_button": false, "has_edit_button": false, "has_delete_button": false, "has_filter": false, "has_tabs": false, "has_modal": true, "has_upload": false}, "page_title": "基金会业务管理系统", "content_preview": "", "url_path": "/statistic/bill/board", "analysis_timestamp": "2025-06-30T17:36:08.663203"}}, {"parent_menu": "资金池管理", "sub_menu": "可变配置", "url": "http://csboss.lianquan.org.cn/statistic/bill/board", "title": "基金会业务管理系统", "screenshot": "enhanced_sub_menu_资金池管理_可变配置.png", "timestamp": "2025-06-30T17:36:21.013100", "exploration_method": "enhanced", "content_analysis": {"elements": {"buttons_count": 2, "tables_count": 0, "forms_count": 0, "inputs_count": 5}, "buttons": [], "tables": [], "features": {"has_search": false, "has_pagination": false, "has_add_button": false, "has_export_button": false, "has_edit_button": false, "has_delete_button": false, "has_filter": false, "has_tabs": false, "has_modal": true, "has_upload": false}, "page_title": "基金会业务管理系统", "content_preview": "", "url_path": "/statistic/bill/board", "analysis_timestamp": "2025-06-30T17:36:21.035088"}}, {"parent_menu": "资金池管理", "sub_menu": "合作方管理", "url": "http://csboss.lianquan.org.cn/ins", "title": "基金会业务管理系统", "screenshot": "enhanced_sub_menu_资金池管理_合作方管理.png", "timestamp": "2025-06-30T17:36:33.422803", "exploration_method": "enhanced", "content_analysis": {"elements": {"buttons_count": 85, "tables_count": 7, "forms_count": 1, "inputs_count": 23}, "buttons": [{"text": "相关说明", "type": "button", "class": "el-button middle-space el-button--primary el-button--small"}, {"text": "审核者设置", "type": "button", "class": "el-button el-button--default el-button--small"}, {"text": "新增", "type": "button", "class": "el-button middle-space add-button el-button--primary el-button--small"}, {"text": "查询", "type": "button", "class": "el-button middle-space el-button--primary el-button--small"}, {"text": "重置", "type": "button", "class": "el-button middle-space el-button--default el-button--small"}, {"text": "导出", "type": "button", "class": "el-button middle-space export-btn el-button--default el-button--small"}], "tables": [{"headers": ["合作方编号", "合作方类型", "机构名称", "机构对接人姓名", "合作期间", "合作状态", "累计合作项目数量", "进行中的合作项目", "标签"], "header_count": 9, "row_count": 63}, {"headers": ["合作方编号", "合作方类型", "机构名称", "机构对接人姓名", "合作期间", "合作状态", "累计合作项目数量", "进行中的合作项目", "标签"], "header_count": 9, "row_count": 1}, {"headers": [], "header_count": 0, "row_count": 20}, {"headers": ["序号"], "header_count": 1, "row_count": 1}, {"headers": [], "header_count": 0, "row_count": 20}], "features": {"has_search": true, "has_pagination": true, "has_add_button": true, "has_export_button": true, "has_edit_button": false, "has_delete_button": false, "has_filter": false, "has_tabs": true, "has_modal": true, "has_upload": false}, "page_title": "基金会业务管理系统", "content_preview": "社会组织个人类型企业类型其他组织\n合作方类型：\n机构名称：\n对接人姓名：\n合作状态：\n标签：\n联劝对接人：\n相关说明 审核者设置 新增 查询 重置 导出\n\t合作方编号\t合作方类型\t机构名称\t机构对接人姓名\t合作期间\t合作状态\t累计合作项目数量\t进行中的合作项目\t标签\t联劝对接人\t\t\n\t\nINSGG202500592\n\t\n--\n\t\n福建省担当者行动教育基金会2\n\t\n安庆旭\n\t\n~\n\t\n未合作\n\t\n--\n\t\n--\n\t\n--\n\t\n--\n\t\n\t\nINSGG202500910\n\t\n执行方\n\t\n佳县慈爱社会公益事业服务中心\n\t\n屈红艳\n\t\n~\n\t\n未合作\n\t\n--\n\t\n--\n\t\n--\n\t\n--\n\t\n\t\nI", "url_path": "/ins", "analysis_timestamp": "2025-06-30T17:36:33.612110"}}, {"parent_menu": "资金池管理", "sub_menu": "合同管理", "url": "http://csboss.lianquan.org.cn/contract", "title": "基金会业务管理系统", "screenshot": "enhanced_sub_menu_资金池管理_合同管理.png", "timestamp": "2025-06-30T17:36:45.995727", "exploration_method": "enhanced", "content_analysis": {"elements": {"buttons_count": 82, "tables_count": 7, "forms_count": 1, "inputs_count": 24}, "buttons": [{"text": "提醒设置", "type": "button", "class": "el-button left el-button--small"}, {"text": "新增", "type": "button", "class": "el-button left middle-space add-button el-button--primary el-button--small"}, {"text": "导出n天内即将过期的合同", "type": "button", "class": "el-button el-button--default el-button--small"}, {"text": "查询", "type": "button", "class": "el-button middle-space el-button--primary el-button--small"}, {"text": "重置", "type": "button", "class": "el-button middle-space el-button--default el-button--small"}, {"text": "导出", "type": "button", "class": "el-button middle-space export-btn el-button--default el-button--small"}, {"text": "更多", "type": "button", "class": "el-button middle-space el-button--default el-button--small"}], "tables": [{"headers": ["合同编号", "合同名称", "合同类型", "合同类别", "签约单位", "合同金额", "合同状态【流程节点】审核人", "申请人", "申请部门"], "header_count": 9, "row_count": 63}, {"headers": ["合同编号", "合同名称", "合同类型", "合同类别", "签约单位", "合同金额", "合同状态【流程节点】审核人", "申请人", "申请部门"], "header_count": 9, "row_count": 1}, {"headers": [], "header_count": 0, "row_count": 20}, {"headers": ["序号"], "header_count": 1, "row_count": 1}, {"headers": [], "header_count": 0, "row_count": 20}], "features": {"has_search": false, "has_pagination": true, "has_add_button": true, "has_export_button": true, "has_edit_button": false, "has_delete_button": false, "has_filter": false, "has_tabs": true, "has_modal": true, "has_upload": false}, "page_title": "基金会业务管理系统", "content_preview": "合同管理作废审批记录\n合同编号：\n合同名称：\n合同类型：\n合同类别：\n合同状态：\n签约单位：\n提醒设置\n新增\n导出n天内即将过期的合同 查询 重置 导出 更多\n\t合同编号\t合同名称\t合同类型\t合同类别\t签约单位\t合同金额\n\t合同状态【流程节点】审核人\t申请人\t申请部门\t申请日期\t签约有效期\t标签\t\t\n\t\nHT2025999\n\t\n资助协议\n\t\n付款\n\t\n资助协议\n\t\n佛吉亚（中国）投资有限公司，无锡市德盛社会工作服务中心\n\t\n118,000.00\n\t\n草稿\n\t\n何松\n\t\n基金会\n\t\n--\n\t\n2021-11-30~2022-08-31\n\t\n--\n\t\n\t\nHT2025998\n\t\n资助协议\n\t\n付款", "url_path": "/contract", "analysis_timestamp": "2025-06-30T17:36:46.195765"}}, {"parent_menu": "资金池管理", "sub_menu": "安全审计", "url": "http://csboss.lianquan.org.cn/contract", "title": "基金会业务管理系统", "screenshot": "enhanced_sub_menu_资金池管理_安全审计.png", "timestamp": "2025-06-30T17:36:58.517113", "exploration_method": "enhanced", "content_analysis": {"elements": {"buttons_count": 82, "tables_count": 7, "forms_count": 1, "inputs_count": 24}, "buttons": [{"text": "提醒设置", "type": "button", "class": "el-button left el-button--small"}, {"text": "新增", "type": "button", "class": "el-button left middle-space add-button el-button--primary el-button--small"}, {"text": "导出n天内即将过期的合同", "type": "button", "class": "el-button el-button--default el-button--small"}, {"text": "查询", "type": "button", "class": "el-button middle-space el-button--primary el-button--small"}, {"text": "重置", "type": "button", "class": "el-button middle-space el-button--default el-button--small"}, {"text": "导出", "type": "button", "class": "el-button middle-space export-btn el-button--default el-button--small"}, {"text": "更多", "type": "button", "class": "el-button middle-space el-button--default el-button--small"}], "tables": [{"headers": ["合同编号", "合同名称", "合同类型", "合同类别", "签约单位", "合同金额", "合同状态【流程节点】审核人", "申请人", "申请部门"], "header_count": 9, "row_count": 63}, {"headers": ["合同编号", "合同名称", "合同类型", "合同类别", "签约单位", "合同金额", "合同状态【流程节点】审核人", "申请人", "申请部门"], "header_count": 9, "row_count": 1}, {"headers": [], "header_count": 0, "row_count": 20}, {"headers": ["序号"], "header_count": 1, "row_count": 1}, {"headers": [], "header_count": 0, "row_count": 20}], "features": {"has_search": false, "has_pagination": true, "has_add_button": true, "has_export_button": true, "has_edit_button": false, "has_delete_button": false, "has_filter": false, "has_tabs": true, "has_modal": true, "has_upload": false}, "page_title": "基金会业务管理系统", "content_preview": "合同管理作废审批记录\n合同编号：\n合同名称：\n合同类型：\n合同类别：\n合同状态：\n签约单位：\n提醒设置\n新增\n导出n天内即将过期的合同 查询 重置 导出 更多\n\t合同编号\t合同名称\t合同类型\t合同类别\t签约单位\t合同金额\n\t合同状态【流程节点】审核人\t申请人\t申请部门\t申请日期\t签约有效期\t标签\t\t\n\t\nHT2025999\n\t\n资助协议\n\t\n付款\n\t\n资助协议\n\t\n佛吉亚（中国）投资有限公司，无锡市德盛社会工作服务中心\n\t\n118,000.00\n\t\n草稿\n\t\n何松\n\t\n基金会\n\t\n--\n\t\n2021-11-30~2022-08-31\n\t\n--\n\t\n\t\nHT2025998\n\t\n资助协议\n\t\n付款", "url_path": "/contract", "analysis_timestamp": "2025-06-30T17:36:58.720504"}}, {"parent_menu": "资金池管理", "sub_menu": "开票管理", "url": "http://csboss.lianquan.org.cn/contract", "title": "基金会业务管理系统", "screenshot": "enhanced_sub_menu_资金池管理_开票管理.png", "timestamp": "2025-06-30T17:37:11.040353", "exploration_method": "enhanced", "content_analysis": {"elements": {"buttons_count": 82, "tables_count": 7, "forms_count": 1, "inputs_count": 24}, "buttons": [{"text": "提醒设置", "type": "button", "class": "el-button left el-button--small"}, {"text": "新增", "type": "button", "class": "el-button left middle-space add-button el-button--primary el-button--small"}, {"text": "导出n天内即将过期的合同", "type": "button", "class": "el-button el-button--default el-button--small"}, {"text": "查询", "type": "button", "class": "el-button middle-space el-button--primary el-button--small"}, {"text": "重置", "type": "button", "class": "el-button middle-space el-button--default el-button--small"}, {"text": "导出", "type": "button", "class": "el-button middle-space export-btn el-button--default el-button--small"}, {"text": "更多", "type": "button", "class": "el-button middle-space el-button--default el-button--small"}], "tables": [{"headers": ["合同编号", "合同名称", "合同类型", "合同类别", "签约单位", "合同金额", "合同状态【流程节点】审核人", "申请人", "申请部门"], "header_count": 9, "row_count": 63}, {"headers": ["合同编号", "合同名称", "合同类型", "合同类别", "签约单位", "合同金额", "合同状态【流程节点】审核人", "申请人", "申请部门"], "header_count": 9, "row_count": 1}, {"headers": [], "header_count": 0, "row_count": 20}, {"headers": ["序号"], "header_count": 1, "row_count": 1}, {"headers": [], "header_count": 0, "row_count": 20}], "features": {"has_search": false, "has_pagination": true, "has_add_button": true, "has_export_button": true, "has_edit_button": false, "has_delete_button": false, "has_filter": false, "has_tabs": true, "has_modal": true, "has_upload": false}, "page_title": "基金会业务管理系统", "content_preview": "合同管理作废审批记录\n合同编号：\n合同名称：\n合同类型：\n合同类别：\n合同状态：\n签约单位：\n提醒设置\n新增\n导出n天内即将过期的合同 查询 重置 导出 更多\n\t合同编号\t合同名称\t合同类型\t合同类别\t签约单位\t合同金额\n\t合同状态【流程节点】审核人\t申请人\t申请部门\t申请日期\t签约有效期\t标签\t\t\n\t\nHT2025999\n\t\n资助协议\n\t\n付款\n\t\n资助协议\n\t\n佛吉亚（中国）投资有限公司，无锡市德盛社会工作服务中心\n\t\n118,000.00\n\t\n草稿\n\t\n何松\n\t\n基金会\n\t\n--\n\t\n2021-11-30~2022-08-31\n\t\n--\n\t\n\t\nHT2025998\n\t\n资助协议\n\t\n付款", "url_path": "/contract", "analysis_timestamp": "2025-06-30T17:37:11.241632"}}, {"parent_menu": "资金池管理", "sub_menu": "捐方管理", "url": "http://csboss.lianquan.org.cn/donor/manage", "title": "基金会业务管理系统", "screenshot": "enhanced_sub_menu_资金池管理_捐方管理.png", "timestamp": "2025-06-30T17:37:23.592087", "exploration_method": "enhanced", "content_analysis": {"elements": {"buttons_count": 15, "tables_count": 7, "forms_count": 1, "inputs_count": 24}, "buttons": [{"text": "新增", "type": "button", "class": "el-button left middle-space add-button el-button--primary el-button--small"}, {"text": "查询", "type": "button", "class": "el-button middle-space el-button--primary el-button--small"}, {"text": "重置", "type": "button", "class": "el-button middle-space el-button--default el-button--small"}, {"text": "导出", "type": "button", "class": "el-button middle-space export-btn el-button--default el-button--small"}, {"text": "更多", "type": "button", "class": "el-button middle-space el-button--default el-button--small"}, {"text": "关闭", "type": "button", "class": "el-button el-button--default el-button--small"}, {"text": "确定", "type": "button", "class": "el-button el-button--primary el-button--small"}], "tables": [{"headers": ["捐方编号", "捐方名称", "捐方简称", "所属小组", "行业类别", "捐方规模", "年营业收入", "企业性质", "上市状态"], "header_count": 9, "row_count": 3}, {"headers": ["捐方编号", "捐方名称", "捐方简称", "所属小组", "行业类别", "捐方规模", "年营业收入", "企业性质", "上市状态"], "header_count": 9, "row_count": 1}, {"headers": [], "header_count": 0, "row_count": 0}, {"headers": ["序号"], "header_count": 1, "row_count": 1}, {"headers": [], "header_count": 0, "row_count": 0}], "features": {"has_search": false, "has_pagination": true, "has_add_button": true, "has_export_button": true, "has_edit_button": false, "has_delete_button": false, "has_filter": false, "has_tabs": true, "has_modal": true, "has_upload": false}, "page_title": "基金会业务管理系统", "content_preview": "企业类型社会组织其他组织个人\n捐方编号：\n捐方名称：\n捐方简称：\n所属小组：\n行业类别：\n捐方规模：\n年营业收入：\n新增\n查询 重置 导出 更多\n\t捐方编号\t捐方名称\t捐方简称\t所属小组\t行业类别\t捐方规模\t年营业收入\t企业性质\t上市状态\t企业成立日期\t业务覆盖范围\t联系地址\t标签\t联系人姓名\t累计管理费金额\t单笔捐赠最大金额\n\t累计捐赠金额\t累计管理费比例\t最新捐赠日期\t累计捐赠项目数量\n\t资助最多次项目名称\t\n\n暂无记录，请更换条件重新搜索\n\n序号\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t操作\n共 0 条\n1\n前往页", "url_path": "/donor/manage", "analysis_timestamp": "2025-06-30T17:37:23.749891"}}, {"parent_menu": "资金池管理", "sub_menu": "支出管理", "url": "http://csboss.lianquan.org.cn/donor/manage", "title": "基金会业务管理系统", "screenshot": "enhanced_sub_menu_资金池管理_支出管理.png", "timestamp": "2025-06-30T17:37:36.058144", "exploration_method": "enhanced", "content_analysis": {"elements": {"buttons_count": 15, "tables_count": 7, "forms_count": 1, "inputs_count": 24}, "buttons": [{"text": "新增", "type": "button", "class": "el-button left middle-space add-button el-button--primary el-button--small"}, {"text": "查询", "type": "button", "class": "el-button middle-space el-button--primary el-button--small"}, {"text": "重置", "type": "button", "class": "el-button middle-space el-button--default el-button--small"}, {"text": "导出", "type": "button", "class": "el-button middle-space export-btn el-button--default el-button--small"}, {"text": "更多", "type": "button", "class": "el-button middle-space el-button--default el-button--small"}, {"text": "关闭", "type": "button", "class": "el-button el-button--default el-button--small"}, {"text": "确定", "type": "button", "class": "el-button el-button--primary el-button--small"}], "tables": [{"headers": ["捐方编号", "捐方名称", "捐方简称", "所属小组", "行业类别", "捐方规模", "年营业收入", "企业性质", "上市状态"], "header_count": 9, "row_count": 3}, {"headers": ["捐方编号", "捐方名称", "捐方简称", "所属小组", "行业类别", "捐方规模", "年营业收入", "企业性质", "上市状态"], "header_count": 9, "row_count": 1}, {"headers": [], "header_count": 0, "row_count": 0}, {"headers": ["序号"], "header_count": 1, "row_count": 1}, {"headers": [], "header_count": 0, "row_count": 0}], "features": {"has_search": false, "has_pagination": true, "has_add_button": true, "has_export_button": true, "has_edit_button": false, "has_delete_button": false, "has_filter": false, "has_tabs": true, "has_modal": true, "has_upload": false}, "page_title": "基金会业务管理系统", "content_preview": "企业类型社会组织其他组织个人\n捐方编号：\n捐方名称：\n捐方简称：\n所属小组：\n行业类别：\n捐方规模：\n年营业收入：\n新增\n查询 重置 导出 更多\n\t捐方编号\t捐方名称\t捐方简称\t所属小组\t行业类别\t捐方规模\t年营业收入\t企业性质\t上市状态\t企业成立日期\t业务覆盖范围\t联系地址\t标签\t联系人姓名\t累计管理费金额\t单笔捐赠最大金额\n\t累计捐赠金额\t累计管理费比例\t最新捐赠日期\t累计捐赠项目数量\n\t资助最多次项目名称\t\n\n暂无记录，请更换条件重新搜索\n\n序号\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t操作\n共 0 条\n1\n前往页", "url_path": "/donor/manage", "analysis_timestamp": "2025-06-30T17:37:36.197837"}}, {"parent_menu": "预算决算", "sub_menu": "业务管理", "url": "http://csboss.lianquan.org.cn/statistic/bill/board", "title": "基金会业务管理系统", "screenshot": "enhanced_sub_menu_预算决算_业务管理.png", "timestamp": "2025-06-30T17:37:58.570675", "exploration_method": "enhanced", "content_analysis": {"elements": {"buttons_count": 2, "tables_count": 0, "forms_count": 0, "inputs_count": 5}, "buttons": [], "tables": [], "features": {"has_search": false, "has_pagination": false, "has_add_button": false, "has_export_button": false, "has_edit_button": false, "has_delete_button": false, "has_filter": false, "has_tabs": false, "has_modal": true, "has_upload": false}, "page_title": "基金会业务管理系统", "content_preview": "", "url_path": "/statistic/bill/board", "analysis_timestamp": "2025-06-30T17:37:58.591983"}}, {"parent_menu": "预算决算", "sub_menu": "可变配置", "url": "http://csboss.lianquan.org.cn/statistic/bill/board", "title": "基金会业务管理系统", "screenshot": "enhanced_sub_menu_预算决算_可变配置.png", "timestamp": "2025-06-30T17:38:10.953176", "exploration_method": "enhanced", "content_analysis": {"elements": {"buttons_count": 2, "tables_count": 0, "forms_count": 0, "inputs_count": 5}, "buttons": [], "tables": [], "features": {"has_search": false, "has_pagination": false, "has_add_button": false, "has_export_button": false, "has_edit_button": false, "has_delete_button": false, "has_filter": false, "has_tabs": false, "has_modal": true, "has_upload": false}, "page_title": "基金会业务管理系统", "content_preview": "", "url_path": "/statistic/bill/board", "analysis_timestamp": "2025-06-30T17:38:10.973603"}}, {"parent_menu": "预算决算", "sub_menu": "合作方管理", "url": "http://csboss.lianquan.org.cn/ins", "title": "基金会业务管理系统", "screenshot": "enhanced_sub_menu_预算决算_合作方管理.png", "timestamp": "2025-06-30T17:38:23.346670", "exploration_method": "enhanced", "content_analysis": {"elements": {"buttons_count": 85, "tables_count": 7, "forms_count": 1, "inputs_count": 23}, "buttons": [{"text": "相关说明", "type": "button", "class": "el-button middle-space el-button--primary el-button--small"}, {"text": "审核者设置", "type": "button", "class": "el-button el-button--default el-button--small"}, {"text": "新增", "type": "button", "class": "el-button middle-space add-button el-button--primary el-button--small"}, {"text": "查询", "type": "button", "class": "el-button middle-space el-button--primary el-button--small"}, {"text": "重置", "type": "button", "class": "el-button middle-space el-button--default el-button--small"}, {"text": "导出", "type": "button", "class": "el-button middle-space export-btn el-button--default el-button--small"}], "tables": [{"headers": ["合作方编号", "合作方类型", "机构名称", "机构对接人姓名", "合作期间", "合作状态", "累计合作项目数量", "进行中的合作项目", "标签"], "header_count": 9, "row_count": 63}, {"headers": ["合作方编号", "合作方类型", "机构名称", "机构对接人姓名", "合作期间", "合作状态", "累计合作项目数量", "进行中的合作项目", "标签"], "header_count": 9, "row_count": 1}, {"headers": [], "header_count": 0, "row_count": 20}, {"headers": ["序号"], "header_count": 1, "row_count": 1}, {"headers": [], "header_count": 0, "row_count": 20}], "features": {"has_search": true, "has_pagination": true, "has_add_button": true, "has_export_button": true, "has_edit_button": false, "has_delete_button": false, "has_filter": false, "has_tabs": true, "has_modal": true, "has_upload": false}, "page_title": "基金会业务管理系统", "content_preview": "社会组织个人类型企业类型其他组织\n合作方类型：\n机构名称：\n对接人姓名：\n合作状态：\n标签：\n联劝对接人：\n相关说明 审核者设置 新增 查询 重置 导出\n\t合作方编号\t合作方类型\t机构名称\t机构对接人姓名\t合作期间\t合作状态\t累计合作项目数量\t进行中的合作项目\t标签\t联劝对接人\t\t\n\t\nINSGG202500592\n\t\n--\n\t\n福建省担当者行动教育基金会2\n\t\n安庆旭\n\t\n~\n\t\n未合作\n\t\n--\n\t\n--\n\t\n--\n\t\n--\n\t\n\t\nINSGG202500910\n\t\n执行方\n\t\n佳县慈爱社会公益事业服务中心\n\t\n屈红艳\n\t\n~\n\t\n未合作\n\t\n--\n\t\n--\n\t\n--\n\t\n--\n\t\n\t\nI", "url_path": "/ins", "analysis_timestamp": "2025-06-30T17:38:23.529253"}}, {"parent_menu": "预算决算", "sub_menu": "合同管理", "url": "http://csboss.lianquan.org.cn/contract", "title": "基金会业务管理系统", "screenshot": "enhanced_sub_menu_预算决算_合同管理.png", "timestamp": "2025-06-30T17:38:35.912887", "exploration_method": "enhanced", "content_analysis": {"elements": {"buttons_count": 82, "tables_count": 7, "forms_count": 1, "inputs_count": 24}, "buttons": [{"text": "提醒设置", "type": "button", "class": "el-button left el-button--small"}, {"text": "新增", "type": "button", "class": "el-button left middle-space add-button el-button--primary el-button--small"}, {"text": "导出n天内即将过期的合同", "type": "button", "class": "el-button el-button--default el-button--small"}, {"text": "查询", "type": "button", "class": "el-button middle-space el-button--primary el-button--small"}, {"text": "重置", "type": "button", "class": "el-button middle-space el-button--default el-button--small"}, {"text": "导出", "type": "button", "class": "el-button middle-space export-btn el-button--default el-button--small"}, {"text": "更多", "type": "button", "class": "el-button middle-space el-button--default el-button--small"}], "tables": [{"headers": ["合同编号", "合同名称", "合同类型", "合同类别", "签约单位", "合同金额", "合同状态【流程节点】审核人", "申请人", "申请部门"], "header_count": 9, "row_count": 63}, {"headers": ["合同编号", "合同名称", "合同类型", "合同类别", "签约单位", "合同金额", "合同状态【流程节点】审核人", "申请人", "申请部门"], "header_count": 9, "row_count": 1}, {"headers": [], "header_count": 0, "row_count": 20}, {"headers": ["序号"], "header_count": 1, "row_count": 1}, {"headers": [], "header_count": 0, "row_count": 20}], "features": {"has_search": false, "has_pagination": true, "has_add_button": true, "has_export_button": true, "has_edit_button": false, "has_delete_button": false, "has_filter": false, "has_tabs": true, "has_modal": true, "has_upload": false}, "page_title": "基金会业务管理系统", "content_preview": "合同管理作废审批记录\n合同编号：\n合同名称：\n合同类型：\n合同类别：\n合同状态：\n签约单位：\n提醒设置\n新增\n导出n天内即将过期的合同 查询 重置 导出 更多\n\t合同编号\t合同名称\t合同类型\t合同类别\t签约单位\t合同金额\n\t合同状态【流程节点】审核人\t申请人\t申请部门\t申请日期\t签约有效期\t标签\t\t\n\t\nHT2025999\n\t\n资助协议\n\t\n付款\n\t\n资助协议\n\t\n佛吉亚（中国）投资有限公司，无锡市德盛社会工作服务中心\n\t\n118,000.00\n\t\n草稿\n\t\n何松\n\t\n基金会\n\t\n--\n\t\n2021-11-30~2022-08-31\n\t\n--\n\t\n\t\nHT2025998\n\t\n资助协议\n\t\n付款", "url_path": "/contract", "analysis_timestamp": "2025-06-30T17:38:36.127880"}}, {"parent_menu": "预算决算", "sub_menu": "安全审计", "url": "http://csboss.lianquan.org.cn/contract", "title": "基金会业务管理系统", "screenshot": "enhanced_sub_menu_预算决算_安全审计.png", "timestamp": "2025-06-30T17:38:48.447576", "exploration_method": "enhanced", "content_analysis": {"elements": {"buttons_count": 82, "tables_count": 7, "forms_count": 1, "inputs_count": 24}, "buttons": [{"text": "提醒设置", "type": "button", "class": "el-button left el-button--small"}, {"text": "新增", "type": "button", "class": "el-button left middle-space add-button el-button--primary el-button--small"}, {"text": "导出n天内即将过期的合同", "type": "button", "class": "el-button el-button--default el-button--small"}, {"text": "查询", "type": "button", "class": "el-button middle-space el-button--primary el-button--small"}, {"text": "重置", "type": "button", "class": "el-button middle-space el-button--default el-button--small"}, {"text": "导出", "type": "button", "class": "el-button middle-space export-btn el-button--default el-button--small"}, {"text": "更多", "type": "button", "class": "el-button middle-space el-button--default el-button--small"}], "tables": [{"headers": ["合同编号", "合同名称", "合同类型", "合同类别", "签约单位", "合同金额", "合同状态【流程节点】审核人", "申请人", "申请部门"], "header_count": 9, "row_count": 63}, {"headers": ["合同编号", "合同名称", "合同类型", "合同类别", "签约单位", "合同金额", "合同状态【流程节点】审核人", "申请人", "申请部门"], "header_count": 9, "row_count": 1}, {"headers": [], "header_count": 0, "row_count": 20}, {"headers": ["序号"], "header_count": 1, "row_count": 1}, {"headers": [], "header_count": 0, "row_count": 20}], "features": {"has_search": false, "has_pagination": true, "has_add_button": true, "has_export_button": true, "has_edit_button": false, "has_delete_button": false, "has_filter": false, "has_tabs": true, "has_modal": true, "has_upload": false}, "page_title": "基金会业务管理系统", "content_preview": "合同管理作废审批记录\n合同编号：\n合同名称：\n合同类型：\n合同类别：\n合同状态：\n签约单位：\n提醒设置\n新增\n导出n天内即将过期的合同 查询 重置 导出 更多\n\t合同编号\t合同名称\t合同类型\t合同类别\t签约单位\t合同金额\n\t合同状态【流程节点】审核人\t申请人\t申请部门\t申请日期\t签约有效期\t标签\t\t\n\t\nHT2025999\n\t\n资助协议\n\t\n付款\n\t\n资助协议\n\t\n佛吉亚（中国）投资有限公司，无锡市德盛社会工作服务中心\n\t\n118,000.00\n\t\n草稿\n\t\n何松\n\t\n基金会\n\t\n--\n\t\n2021-11-30~2022-08-31\n\t\n--\n\t\n\t\nHT2025998\n\t\n资助协议\n\t\n付款", "url_path": "/contract", "analysis_timestamp": "2025-06-30T17:38:48.624258"}}, {"parent_menu": "预算决算", "sub_menu": "开票管理", "url": "http://csboss.lianquan.org.cn/contract", "title": "基金会业务管理系统", "screenshot": "enhanced_sub_menu_预算决算_开票管理.png", "timestamp": "2025-06-30T17:39:45.791405", "exploration_method": "enhanced", "content_analysis": {"elements": {"buttons_count": 82, "tables_count": 7, "forms_count": 1, "inputs_count": 24}, "buttons": [{"text": "提醒设置", "type": "button", "class": "el-button left el-button--small"}, {"text": "新增", "type": "button", "class": "el-button left middle-space add-button el-button--primary el-button--small"}, {"text": "导出n天内即将过期的合同", "type": "button", "class": "el-button el-button--default el-button--small"}, {"text": "查询", "type": "button", "class": "el-button middle-space el-button--primary el-button--small"}, {"text": "重置", "type": "button", "class": "el-button middle-space el-button--default el-button--small"}, {"text": "导出", "type": "button", "class": "el-button middle-space export-btn el-button--default el-button--small"}, {"text": "更多", "type": "button", "class": "el-button middle-space el-button--default el-button--small"}], "tables": [{"headers": ["合同编号", "合同名称", "合同类型", "合同类别", "签约单位", "合同金额", "合同状态【流程节点】审核人", "申请人", "申请部门"], "header_count": 9, "row_count": 63}, {"headers": ["合同编号", "合同名称", "合同类型", "合同类别", "签约单位", "合同金额", "合同状态【流程节点】审核人", "申请人", "申请部门"], "header_count": 9, "row_count": 1}, {"headers": [], "header_count": 0, "row_count": 20}, {"headers": ["序号"], "header_count": 1, "row_count": 1}, {"headers": [], "header_count": 0, "row_count": 20}], "features": {"has_search": false, "has_pagination": true, "has_add_button": true, "has_export_button": true, "has_edit_button": false, "has_delete_button": false, "has_filter": false, "has_tabs": true, "has_modal": true, "has_upload": false}, "page_title": "基金会业务管理系统", "content_preview": "合同管理作废审批记录\n合同编号：\n合同名称：\n合同类型：\n合同类别：\n合同状态：\n签约单位：\n提醒设置\n新增\n导出n天内即将过期的合同 查询 重置 导出 更多\n\t合同编号\t合同名称\t合同类型\t合同类别\t签约单位\t合同金额\n\t合同状态【流程节点】审核人\t申请人\t申请部门\t申请日期\t签约有效期\t标签\t\t\n\t\nHT2025999\n\t\n资助协议\n\t\n付款\n\t\n资助协议\n\t\n佛吉亚（中国）投资有限公司，无锡市德盛社会工作服务中心\n\t\n118,000.00\n\t\n草稿\n\t\n何松\n\t\n基金会\n\t\n--\n\t\n2021-11-30~2022-08-31\n\t\n--\n\t\n\t\nHT2025998\n\t\n资助协议\n\t\n付款", "url_path": "/contract", "analysis_timestamp": "2025-06-30T17:39:45.970674"}}, {"parent_menu": "预算决算", "sub_menu": "捐方管理", "url": "http://csboss.lianquan.org.cn/donor/manage", "title": "基金会业务管理系统", "screenshot": "enhanced_sub_menu_预算决算_捐方管理.png", "timestamp": "2025-06-30T17:39:58.323966", "exploration_method": "enhanced", "content_analysis": {"elements": {"buttons_count": 15, "tables_count": 7, "forms_count": 1, "inputs_count": 24}, "buttons": [{"text": "新增", "type": "button", "class": "el-button left middle-space add-button el-button--primary el-button--small"}, {"text": "查询", "type": "button", "class": "el-button middle-space el-button--primary el-button--small"}, {"text": "重置", "type": "button", "class": "el-button middle-space el-button--default el-button--small"}, {"text": "导出", "type": "button", "class": "el-button middle-space export-btn el-button--default el-button--small"}, {"text": "更多", "type": "button", "class": "el-button middle-space el-button--default el-button--small"}, {"text": "关闭", "type": "button", "class": "el-button el-button--default el-button--small"}, {"text": "确定", "type": "button", "class": "el-button el-button--primary el-button--small"}], "tables": [{"headers": ["捐方编号", "捐方名称", "捐方简称", "所属小组", "行业类别", "捐方规模", "年营业收入", "企业性质", "上市状态"], "header_count": 9, "row_count": 3}, {"headers": ["捐方编号", "捐方名称", "捐方简称", "所属小组", "行业类别", "捐方规模", "年营业收入", "企业性质", "上市状态"], "header_count": 9, "row_count": 1}, {"headers": [], "header_count": 0, "row_count": 0}, {"headers": ["序号"], "header_count": 1, "row_count": 1}, {"headers": [], "header_count": 0, "row_count": 0}], "features": {"has_search": false, "has_pagination": true, "has_add_button": true, "has_export_button": true, "has_edit_button": false, "has_delete_button": false, "has_filter": false, "has_tabs": true, "has_modal": true, "has_upload": false}, "page_title": "基金会业务管理系统", "content_preview": "企业类型社会组织其他组织个人\n捐方编号：\n捐方名称：\n捐方简称：\n所属小组：\n行业类别：\n捐方规模：\n年营业收入：\n新增\n查询 重置 导出 更多\n\t捐方编号\t捐方名称\t捐方简称\t所属小组\t行业类别\t捐方规模\t年营业收入\t企业性质\t上市状态\t企业成立日期\t业务覆盖范围\t联系地址\t标签\t联系人姓名\t累计管理费金额\t单笔捐赠最大金额\n\t累计捐赠金额\t累计管理费比例\t最新捐赠日期\t累计捐赠项目数量\n\t资助最多次项目名称\t\n\n暂无记录，请更换条件重新搜索\n\n序号\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t操作\n共 0 条\n1\n前往页", "url_path": "/donor/manage", "analysis_timestamp": "2025-06-30T17:39:58.472371"}}, {"parent_menu": "物资管理", "sub_menu": "业务管理", "url": "http://csboss.lianquan.org.cn/statistic/bill/board", "title": "基金会业务管理系统", "screenshot": "enhanced_sub_menu_物资管理_业务管理.png", "timestamp": "2025-06-30T17:40:20.777957", "exploration_method": "enhanced", "content_analysis": {"elements": {"buttons_count": 2, "tables_count": 0, "forms_count": 0, "inputs_count": 5}, "buttons": [], "tables": [], "features": {"has_search": false, "has_pagination": false, "has_add_button": false, "has_export_button": false, "has_edit_button": false, "has_delete_button": false, "has_filter": false, "has_tabs": false, "has_modal": true, "has_upload": false}, "page_title": "基金会业务管理系统", "content_preview": "", "url_path": "/statistic/bill/board", "analysis_timestamp": "2025-06-30T17:40:20.801420"}}, {"parent_menu": "物资管理", "sub_menu": "仓库管理", "url": "http://csboss.lianquan.org.cn/goods/store/manage", "title": "基金会业务管理系统", "screenshot": "enhanced_sub_menu_物资管理_仓库管理.png", "timestamp": "2025-06-30T17:40:26.469262", "exploration_method": "enhanced", "content_analysis": {"elements": {"buttons_count": 25, "tables_count": 14, "forms_count": 2, "inputs_count": 15}, "buttons": [{"text": "添加子类型", "type": "button", "class": "el-button add-button el-button--primary el-button--small"}, {"text": "取 消", "type": "button", "class": "el-button el-button--default el-button--small"}, {"text": "确 定", "type": "button", "class": "el-button el-button--primary el-button--small"}, {"text": "编辑", "type": "button", "class": "el-button el-button--text el-button--small"}, {"text": "停用", "type": "button", "class": "el-button el-button--text el-button--small"}, {"text": "编辑", "type": "button", "class": "el-button el-button--text el-button--small"}], "tables": [{"headers": ["类型名称", "状态", "序号"], "header_count": 3, "row_count": 6}, {"headers": ["类型名称", "状态"], "header_count": 2, "row_count": 1}, {"headers": [], "header_count": 0, "row_count": 1}, {"headers": ["序号"], "header_count": 1, "row_count": 1}, {"headers": [], "header_count": 0, "row_count": 1}], "features": {"has_search": false, "has_pagination": true, "has_add_button": true, "has_export_button": false, "has_edit_button": true, "has_delete_button": false, "has_filter": false, "has_tabs": true, "has_modal": true, "has_upload": false}, "page_title": "基金会业务管理系统", "content_preview": "类型管理仓库管理\n添加子类型\n\t类型名称\t状态\t\n\t\n未分类\n\t\n已启用\n\t\n序号\t\t\t\n1\n\t\t\t\n\t\t\t操作\n\t\t\t\n编辑 停用\n共 1 条\n1\n前往页", "url_path": "/goods/store/manage", "analysis_timestamp": "2025-06-30T17:40:26.590564"}}, {"parent_menu": "物资管理", "sub_menu": "仓管单据", "url": "http://csboss.lianquan.org.cn/goods/income/manage", "title": "基金会业务管理系统", "screenshot": "enhanced_sub_menu_物资管理_仓管单据.png", "timestamp": "2025-06-30T17:40:32.265939", "exploration_method": "enhanced", "content_analysis": {"elements": {"buttons_count": 15, "tables_count": 7, "forms_count": 1, "inputs_count": 26}, "buttons": [{"text": "新增", "type": "button", "class": "el-button left middle-space add-button el-button--primary el-button--small"}, {"text": "查询", "type": "button", "class": "el-button middle-space el-button--primary el-button--small"}, {"text": "重置", "type": "button", "class": "el-button middle-space el-button--default el-button--small"}, {"text": "导出", "type": "button", "class": "el-button middle-space export-btn el-button--default el-button--small"}, {"text": "更多", "type": "button", "class": "el-button middle-space el-button--default el-button--small"}], "tables": [{"headers": ["入库编号", "筹款产品", "仓库名称", "预期入库日期", "入库资金池", "来源类型", "资产类型", "捐方", "入库价值(元)"], "header_count": 9, "row_count": 3}, {"headers": ["入库编号", "筹款产品", "仓库名称", "预期入库日期", "入库资金池", "来源类型", "资产类型", "捐方", "入库价值(元)"], "header_count": 9, "row_count": 1}, {"headers": [], "header_count": 0, "row_count": 0}, {"headers": ["序号"], "header_count": 1, "row_count": 1}, {"headers": [], "header_count": 0, "row_count": 0}], "features": {"has_search": true, "has_pagination": true, "has_add_button": true, "has_export_button": true, "has_edit_button": false, "has_delete_button": false, "has_filter": false, "has_tabs": true, "has_modal": true, "has_upload": false}, "page_title": "基金会业务管理系统", "content_preview": "入库单出库单结转单报废单盘亏单盘盈单\n入库编号：\n筹款产品：\n来源类型：\n状态：\n捐方：\n申请人：\n新增\n查询 重置 导出 更多\n\t入库编号\t筹款产品\t仓库名称\t预期入库日期\t入库资金池\t来源类型\t资产类型\t捐方\t入库价值(元)\t物资明细\t结转信息\t状态【流程节点】审核人\t申请人\t申请时间\t申请部门\t\n\n暂无记录，请更换条件重新搜索\n\n序号\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t操作\n共 0 条\n1\n前往页", "url_path": "/goods/income/manage", "analysis_timestamp": "2025-06-30T17:40:32.404988"}}, {"parent_menu": "物资管理", "sub_menu": "可变配置", "url": "http://csboss.lianquan.org.cn/goods/income/manage", "title": "基金会业务管理系统", "screenshot": "enhanced_sub_menu_物资管理_可变配置.png", "timestamp": "2025-06-30T17:40:44.711556", "exploration_method": "enhanced", "content_analysis": {"elements": {"buttons_count": 15, "tables_count": 7, "forms_count": 1, "inputs_count": 26}, "buttons": [{"text": "新增", "type": "button", "class": "el-button left middle-space add-button el-button--primary el-button--small"}, {"text": "查询", "type": "button", "class": "el-button middle-space el-button--primary el-button--small"}, {"text": "重置", "type": "button", "class": "el-button middle-space el-button--default el-button--small"}, {"text": "导出", "type": "button", "class": "el-button middle-space export-btn el-button--default el-button--small"}, {"text": "更多", "type": "button", "class": "el-button middle-space el-button--default el-button--small"}], "tables": [{"headers": ["入库编号", "筹款产品", "仓库名称", "预期入库日期", "入库资金池", "来源类型", "资产类型", "捐方", "入库价值(元)"], "header_count": 9, "row_count": 3}, {"headers": ["入库编号", "筹款产品", "仓库名称", "预期入库日期", "入库资金池", "来源类型", "资产类型", "捐方", "入库价值(元)"], "header_count": 9, "row_count": 1}, {"headers": [], "header_count": 0, "row_count": 0}, {"headers": ["序号"], "header_count": 1, "row_count": 1}, {"headers": [], "header_count": 0, "row_count": 0}], "features": {"has_search": true, "has_pagination": true, "has_add_button": true, "has_export_button": true, "has_edit_button": false, "has_delete_button": false, "has_filter": false, "has_tabs": true, "has_modal": true, "has_upload": false}, "page_title": "基金会业务管理系统", "content_preview": "入库单出库单结转单报废单盘亏单盘盈单\n入库编号：\n筹款产品：\n来源类型：\n状态：\n捐方：\n申请人：\n新增\n查询 重置 导出 更多\n\t入库编号\t筹款产品\t仓库名称\t预期入库日期\t入库资金池\t来源类型\t资产类型\t捐方\t入库价值(元)\t物资明细\t结转信息\t状态【流程节点】审核人\t申请人\t申请时间\t申请部门\t\n\n暂无记录，请更换条件重新搜索\n\n序号\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t操作\n共 0 条\n1\n前往页", "url_path": "/goods/income/manage", "analysis_timestamp": "2025-06-30T17:40:44.895539"}}, {"parent_menu": "物资管理", "sub_menu": "合作方管理", "url": "http://csboss.lianquan.org.cn/ins", "title": "基金会业务管理系统", "screenshot": "enhanced_sub_menu_物资管理_合作方管理.png", "timestamp": "2025-06-30T17:40:57.265713", "exploration_method": "enhanced", "content_analysis": {"elements": {"buttons_count": 85, "tables_count": 7, "forms_count": 1, "inputs_count": 23}, "buttons": [{"text": "相关说明", "type": "button", "class": "el-button middle-space el-button--primary el-button--small"}, {"text": "审核者设置", "type": "button", "class": "el-button el-button--default el-button--small"}, {"text": "新增", "type": "button", "class": "el-button middle-space add-button el-button--primary el-button--small"}, {"text": "查询", "type": "button", "class": "el-button middle-space el-button--primary el-button--small"}, {"text": "重置", "type": "button", "class": "el-button middle-space el-button--default el-button--small"}, {"text": "导出", "type": "button", "class": "el-button middle-space export-btn el-button--default el-button--small"}], "tables": [{"headers": ["合作方编号", "合作方类型", "机构名称", "机构对接人姓名", "合作期间", "合作状态", "累计合作项目数量", "进行中的合作项目", "标签"], "header_count": 9, "row_count": 63}, {"headers": ["合作方编号", "合作方类型", "机构名称", "机构对接人姓名", "合作期间", "合作状态", "累计合作项目数量", "进行中的合作项目", "标签"], "header_count": 9, "row_count": 1}, {"headers": [], "header_count": 0, "row_count": 20}, {"headers": ["序号"], "header_count": 1, "row_count": 1}, {"headers": [], "header_count": 0, "row_count": 20}], "features": {"has_search": true, "has_pagination": true, "has_add_button": true, "has_export_button": true, "has_edit_button": false, "has_delete_button": false, "has_filter": false, "has_tabs": true, "has_modal": true, "has_upload": false}, "page_title": "基金会业务管理系统", "content_preview": "社会组织个人类型企业类型其他组织\n合作方类型：\n机构名称：\n对接人姓名：\n合作状态：\n标签：\n联劝对接人：\n相关说明 审核者设置 新增 查询 重置 导出\n\t合作方编号\t合作方类型\t机构名称\t机构对接人姓名\t合作期间\t合作状态\t累计合作项目数量\t进行中的合作项目\t标签\t联劝对接人\t\t\n\t\nINSGG202500592\n\t\n--\n\t\n福建省担当者行动教育基金会2\n\t\n安庆旭\n\t\n~\n\t\n未合作\n\t\n--\n\t\n--\n\t\n--\n\t\n--\n\t\n\t\nINSGG202500910\n\t\n执行方\n\t\n佳县慈爱社会公益事业服务中心\n\t\n屈红艳\n\t\n~\n\t\n未合作\n\t\n--\n\t\n--\n\t\n--\n\t\n--\n\t\n\t\nI", "url_path": "/ins", "analysis_timestamp": "2025-06-30T17:40:57.457343"}}, {"parent_menu": "物资管理", "sub_menu": "合同管理", "url": "http://csboss.lianquan.org.cn/contract", "title": "基金会业务管理系统", "screenshot": "enhanced_sub_menu_物资管理_合同管理.png", "timestamp": "2025-06-30T17:41:09.955231", "exploration_method": "enhanced", "content_analysis": {"elements": {"buttons_count": 82, "tables_count": 7, "forms_count": 1, "inputs_count": 24}, "buttons": [{"text": "提醒设置", "type": "button", "class": "el-button left el-button--small"}, {"text": "新增", "type": "button", "class": "el-button left middle-space add-button el-button--primary el-button--small"}, {"text": "导出n天内即将过期的合同", "type": "button", "class": "el-button el-button--default el-button--small"}, {"text": "查询", "type": "button", "class": "el-button middle-space el-button--primary el-button--small"}, {"text": "重置", "type": "button", "class": "el-button middle-space el-button--default el-button--small"}, {"text": "导出", "type": "button", "class": "el-button middle-space export-btn el-button--default el-button--small"}, {"text": "更多", "type": "button", "class": "el-button middle-space el-button--default el-button--small"}], "tables": [{"headers": ["合同编号", "合同名称", "合同类型", "合同类别", "签约单位", "合同金额", "合同状态【流程节点】审核人", "申请人", "申请部门"], "header_count": 9, "row_count": 63}, {"headers": ["合同编号", "合同名称", "合同类型", "合同类别", "签约单位", "合同金额", "合同状态【流程节点】审核人", "申请人", "申请部门"], "header_count": 9, "row_count": 1}, {"headers": [], "header_count": 0, "row_count": 20}, {"headers": ["序号"], "header_count": 1, "row_count": 1}, {"headers": [], "header_count": 0, "row_count": 20}], "features": {"has_search": false, "has_pagination": true, "has_add_button": true, "has_export_button": true, "has_edit_button": false, "has_delete_button": false, "has_filter": false, "has_tabs": true, "has_modal": true, "has_upload": false}, "page_title": "基金会业务管理系统", "content_preview": "合同管理作废审批记录\n合同编号：\n合同名称：\n合同类型：\n合同类别：\n合同状态：\n签约单位：\n提醒设置\n新增\n导出n天内即将过期的合同 查询 重置 导出 更多\n\t合同编号\t合同名称\t合同类型\t合同类别\t签约单位\t合同金额\n\t合同状态【流程节点】审核人\t申请人\t申请部门\t申请日期\t签约有效期\t标签\t\t\n\t\nHT2025999\n\t\n资助协议\n\t\n付款\n\t\n资助协议\n\t\n佛吉亚（中国）投资有限公司，无锡市德盛社会工作服务中心\n\t\n118,000.00\n\t\n草稿\n\t\n何松\n\t\n基金会\n\t\n--\n\t\n2021-11-30~2022-08-31\n\t\n--\n\t\n\t\nHT2025998\n\t\n资助协议\n\t\n付款", "url_path": "/contract", "analysis_timestamp": "2025-06-30T17:41:10.152722"}}, {"parent_menu": "物资管理", "sub_menu": "安全审计", "url": "http://csboss.lianquan.org.cn/contract", "title": "基金会业务管理系统", "screenshot": "enhanced_sub_menu_物资管理_安全审计.png", "timestamp": "2025-06-30T17:41:22.457061", "exploration_method": "enhanced", "content_analysis": {"elements": {"buttons_count": 82, "tables_count": 7, "forms_count": 1, "inputs_count": 24}, "buttons": [{"text": "提醒设置", "type": "button", "class": "el-button left el-button--small"}, {"text": "新增", "type": "button", "class": "el-button left middle-space add-button el-button--primary el-button--small"}, {"text": "导出n天内即将过期的合同", "type": "button", "class": "el-button el-button--default el-button--small"}, {"text": "查询", "type": "button", "class": "el-button middle-space el-button--primary el-button--small"}, {"text": "重置", "type": "button", "class": "el-button middle-space el-button--default el-button--small"}, {"text": "导出", "type": "button", "class": "el-button middle-space export-btn el-button--default el-button--small"}, {"text": "更多", "type": "button", "class": "el-button middle-space el-button--default el-button--small"}], "tables": [{"headers": ["合同编号", "合同名称", "合同类型", "合同类别", "签约单位", "合同金额", "合同状态【流程节点】审核人", "申请人", "申请部门"], "header_count": 9, "row_count": 63}, {"headers": ["合同编号", "合同名称", "合同类型", "合同类别", "签约单位", "合同金额", "合同状态【流程节点】审核人", "申请人", "申请部门"], "header_count": 9, "row_count": 1}, {"headers": [], "header_count": 0, "row_count": 20}, {"headers": ["序号"], "header_count": 1, "row_count": 1}, {"headers": [], "header_count": 0, "row_count": 20}], "features": {"has_search": false, "has_pagination": true, "has_add_button": true, "has_export_button": true, "has_edit_button": false, "has_delete_button": false, "has_filter": false, "has_tabs": true, "has_modal": true, "has_upload": false}, "page_title": "基金会业务管理系统", "content_preview": "合同管理作废审批记录\n合同编号：\n合同名称：\n合同类型：\n合同类别：\n合同状态：\n签约单位：\n提醒设置\n新增\n导出n天内即将过期的合同 查询 重置 导出 更多\n\t合同编号\t合同名称\t合同类型\t合同类别\t签约单位\t合同金额\n\t合同状态【流程节点】审核人\t申请人\t申请部门\t申请日期\t签约有效期\t标签\t\t\n\t\nHT2025999\n\t\n资助协议\n\t\n付款\n\t\n资助协议\n\t\n佛吉亚（中国）投资有限公司，无锡市德盛社会工作服务中心\n\t\n118,000.00\n\t\n草稿\n\t\n何松\n\t\n基金会\n\t\n--\n\t\n2021-11-30~2022-08-31\n\t\n--\n\t\n\t\nHT2025998\n\t\n资助协议\n\t\n付款", "url_path": "/contract", "analysis_timestamp": "2025-06-30T17:41:22.646886"}}, {"parent_menu": "支出管理", "sub_menu": "业务管理", "url": "http://csboss.lianquan.org.cn/statistic/bill/board", "title": "基金会业务管理系统", "screenshot": "enhanced_sub_menu_支出管理_业务管理.png", "timestamp": "2025-06-30T17:42:29.923836", "exploration_method": "enhanced", "content_analysis": {"elements": {"buttons_count": 2, "tables_count": 0, "forms_count": 0, "inputs_count": 5}, "buttons": [], "tables": [], "features": {"has_search": false, "has_pagination": false, "has_add_button": false, "has_export_button": false, "has_edit_button": false, "has_delete_button": false, "has_filter": false, "has_tabs": false, "has_modal": true, "has_upload": false}, "page_title": "基金会业务管理系统", "content_preview": "", "url_path": "/statistic/bill/board", "analysis_timestamp": "2025-06-30T17:42:29.944867"}}, {"parent_menu": "支出管理", "sub_menu": "可变配置", "url": "http://csboss.lianquan.org.cn/statistic/bill/board", "title": "基金会业务管理系统", "screenshot": "enhanced_sub_menu_支出管理_可变配置.png", "timestamp": "2025-06-30T17:42:42.305751", "exploration_method": "enhanced", "content_analysis": {"elements": {"buttons_count": 2, "tables_count": 0, "forms_count": 0, "inputs_count": 5}, "buttons": [], "tables": [], "features": {"has_search": false, "has_pagination": false, "has_add_button": false, "has_export_button": false, "has_edit_button": false, "has_delete_button": false, "has_filter": false, "has_tabs": false, "has_modal": true, "has_upload": false}, "page_title": "基金会业务管理系统", "content_preview": "", "url_path": "/statistic/bill/board", "analysis_timestamp": "2025-06-30T17:42:42.328055"}}, {"parent_menu": "支出管理", "sub_menu": "合作方管理", "url": "http://csboss.lianquan.org.cn/ins", "title": "基金会业务管理系统", "screenshot": "enhanced_sub_menu_支出管理_合作方管理.png", "timestamp": "2025-06-30T17:42:54.704705", "exploration_method": "enhanced", "content_analysis": {"elements": {"buttons_count": 85, "tables_count": 7, "forms_count": 1, "inputs_count": 23}, "buttons": [{"text": "相关说明", "type": "button", "class": "el-button middle-space el-button--primary el-button--small"}, {"text": "审核者设置", "type": "button", "class": "el-button el-button--default el-button--small"}, {"text": "新增", "type": "button", "class": "el-button middle-space add-button el-button--primary el-button--small"}, {"text": "查询", "type": "button", "class": "el-button middle-space el-button--primary el-button--small"}, {"text": "重置", "type": "button", "class": "el-button middle-space el-button--default el-button--small"}, {"text": "导出", "type": "button", "class": "el-button middle-space export-btn el-button--default el-button--small"}], "tables": [{"headers": ["合作方编号", "合作方类型", "机构名称", "机构对接人姓名", "合作期间", "合作状态", "累计合作项目数量", "进行中的合作项目", "标签"], "header_count": 9, "row_count": 63}, {"headers": ["合作方编号", "合作方类型", "机构名称", "机构对接人姓名", "合作期间", "合作状态", "累计合作项目数量", "进行中的合作项目", "标签"], "header_count": 9, "row_count": 1}, {"headers": [], "header_count": 0, "row_count": 20}, {"headers": ["序号"], "header_count": 1, "row_count": 1}, {"headers": [], "header_count": 0, "row_count": 20}], "features": {"has_search": true, "has_pagination": true, "has_add_button": true, "has_export_button": true, "has_edit_button": false, "has_delete_button": false, "has_filter": false, "has_tabs": true, "has_modal": true, "has_upload": false}, "page_title": "基金会业务管理系统", "content_preview": "社会组织个人类型企业类型其他组织\n合作方类型：\n机构名称：\n对接人姓名：\n合作状态：\n标签：\n联劝对接人：\n相关说明 审核者设置 新增 查询 重置 导出\n\t合作方编号\t合作方类型\t机构名称\t机构对接人姓名\t合作期间\t合作状态\t累计合作项目数量\t进行中的合作项目\t标签\t联劝对接人\t\t\n\t\nINSGG202500592\n\t\n--\n\t\n福建省担当者行动教育基金会2\n\t\n安庆旭\n\t\n~\n\t\n未合作\n\t\n--\n\t\n--\n\t\n--\n\t\n--\n\t\n\t\nINSGG202500910\n\t\n执行方\n\t\n佳县慈爱社会公益事业服务中心\n\t\n屈红艳\n\t\n~\n\t\n未合作\n\t\n--\n\t\n--\n\t\n--\n\t\n--\n\t\n\t\nI", "url_path": "/ins", "analysis_timestamp": "2025-06-30T17:42:54.897597"}}, {"parent_menu": "支出管理", "sub_menu": "合同管理", "url": "http://csboss.lianquan.org.cn/contract", "title": "基金会业务管理系统", "screenshot": "enhanced_sub_menu_支出管理_合同管理.png", "timestamp": "2025-06-30T17:43:07.283258", "exploration_method": "enhanced", "content_analysis": {"elements": {"buttons_count": 82, "tables_count": 7, "forms_count": 1, "inputs_count": 24}, "buttons": [{"text": "提醒设置", "type": "button", "class": "el-button left el-button--small"}, {"text": "新增", "type": "button", "class": "el-button left middle-space add-button el-button--primary el-button--small"}, {"text": "导出n天内即将过期的合同", "type": "button", "class": "el-button el-button--default el-button--small"}, {"text": "查询", "type": "button", "class": "el-button middle-space el-button--primary el-button--small"}, {"text": "重置", "type": "button", "class": "el-button middle-space el-button--default el-button--small"}, {"text": "导出", "type": "button", "class": "el-button middle-space export-btn el-button--default el-button--small"}, {"text": "更多", "type": "button", "class": "el-button middle-space el-button--default el-button--small"}], "tables": [{"headers": ["合同编号", "合同名称", "合同类型", "合同类别", "签约单位", "合同金额", "合同状态【流程节点】审核人", "申请人", "申请部门"], "header_count": 9, "row_count": 63}, {"headers": ["合同编号", "合同名称", "合同类型", "合同类别", "签约单位", "合同金额", "合同状态【流程节点】审核人", "申请人", "申请部门"], "header_count": 9, "row_count": 1}, {"headers": [], "header_count": 0, "row_count": 20}, {"headers": ["序号"], "header_count": 1, "row_count": 1}, {"headers": [], "header_count": 0, "row_count": 20}], "features": {"has_search": false, "has_pagination": true, "has_add_button": true, "has_export_button": true, "has_edit_button": false, "has_delete_button": false, "has_filter": false, "has_tabs": true, "has_modal": true, "has_upload": false}, "page_title": "基金会业务管理系统", "content_preview": "合同管理作废审批记录\n合同编号：\n合同名称：\n合同类型：\n合同类别：\n合同状态：\n签约单位：\n提醒设置\n新增\n导出n天内即将过期的合同 查询 重置 导出 更多\n\t合同编号\t合同名称\t合同类型\t合同类别\t签约单位\t合同金额\n\t合同状态【流程节点】审核人\t申请人\t申请部门\t申请日期\t签约有效期\t标签\t\t\n\t\nHT2025999\n\t\n资助协议\n\t\n付款\n\t\n资助协议\n\t\n佛吉亚（中国）投资有限公司，无锡市德盛社会工作服务中心\n\t\n118,000.00\n\t\n草稿\n\t\n何松\n\t\n基金会\n\t\n--\n\t\n2021-11-30~2022-08-31\n\t\n--\n\t\n\t\nHT2025998\n\t\n资助协议\n\t\n付款", "url_path": "/contract", "analysis_timestamp": "2025-06-30T17:43:07.485610"}}, {"parent_menu": "支出管理", "sub_menu": "安全审计", "url": "http://csboss.lianquan.org.cn/contract", "title": "基金会业务管理系统", "screenshot": "enhanced_sub_menu_支出管理_安全审计.png", "timestamp": "2025-06-30T17:45:34.365551", "exploration_method": "enhanced", "content_analysis": {"elements": {"buttons_count": 82, "tables_count": 7, "forms_count": 1, "inputs_count": 24}, "buttons": [{"text": "提醒设置", "type": "button", "class": "el-button left el-button--small"}, {"text": "新增", "type": "button", "class": "el-button left middle-space add-button el-button--primary el-button--small"}, {"text": "导出n天内即将过期的合同", "type": "button", "class": "el-button el-button--default el-button--small"}, {"text": "查询", "type": "button", "class": "el-button middle-space el-button--primary el-button--small"}, {"text": "重置", "type": "button", "class": "el-button middle-space el-button--default el-button--small"}, {"text": "导出", "type": "button", "class": "el-button middle-space export-btn el-button--default el-button--small"}, {"text": "更多", "type": "button", "class": "el-button middle-space el-button--default el-button--small"}], "tables": [{"headers": ["合同编号", "合同名称", "合同类型", "合同类别", "签约单位", "合同金额", "合同状态【流程节点】审核人", "申请人", "申请部门"], "header_count": 9, "row_count": 63}, {"headers": ["合同编号", "合同名称", "合同类型", "合同类别", "签约单位", "合同金额", "合同状态【流程节点】审核人", "申请人", "申请部门"], "header_count": 9, "row_count": 1}, {"headers": [], "header_count": 0, "row_count": 20}, {"headers": ["序号"], "header_count": 1, "row_count": 1}, {"headers": [], "header_count": 0, "row_count": 20}], "features": {"has_search": false, "has_pagination": true, "has_add_button": true, "has_export_button": true, "has_edit_button": false, "has_delete_button": false, "has_filter": false, "has_tabs": true, "has_modal": true, "has_upload": false}, "page_title": "基金会业务管理系统", "content_preview": "合同管理作废审批记录\n合同编号：\n合同名称：\n合同类型：\n合同类别：\n合同状态：\n签约单位：\n提醒设置\n新增\n导出n天内即将过期的合同 查询 重置 导出 更多\n\t合同编号\t合同名称\t合同类型\t合同类别\t签约单位\t合同金额\n\t合同状态【流程节点】审核人\t申请人\t申请部门\t申请日期\t签约有效期\t标签\t\t\n\t\nHT2025999\n\t\n资助协议\n\t\n付款\n\t\n资助协议\n\t\n佛吉亚（中国）投资有限公司，无锡市德盛社会工作服务中心\n\t\n118,000.00\n\t\n草稿\n\t\n何松\n\t\n基金会\n\t\n--\n\t\n2021-11-30~2022-08-31\n\t\n--\n\t\n\t\nHT2025998\n\t\n资助协议\n\t\n付款", "url_path": "/contract", "analysis_timestamp": "2025-06-30T17:45:34.543036"}}, {"parent_menu": "业务管理", "sub_menu": "业务管理", "url": "http://csboss.lianquan.org.cn/business/project/gyzzhz", "title": "基金会业务管理系统", "screenshot": "enhanced_sub_menu_业务管理_业务管理.png", "timestamp": "2025-06-30T17:45:58.540281", "exploration_method": "enhanced", "content_analysis": {"elements": {"buttons_count": 72, "tables_count": 7, "forms_count": 2, "inputs_count": 22}, "buttons": [{"text": "新增", "type": "button", "class": "el-button middle-space add-button el-button--primary el-button--small"}, {"text": "查询", "type": "button", "class": "el-button middle-space el-button--primary el-button--small"}, {"text": "重置", "type": "button", "class": "el-button middle-space el-button--default el-button--small"}, {"text": "导出", "type": "button", "class": "el-button middle-space el-button--small"}], "tables": [{"headers": ["申请编号", "名称", "申请日期", "状态【流程节点】审核人", "负责人", "申请部门", "业务领域", "标签"], "header_count": 8, "row_count": 63}, {"headers": ["申请编号", "名称", "申请日期", "状态【流程节点】审核人", "负责人", "申请部门", "业务领域", "标签"], "header_count": 8, "row_count": 1}, {"headers": [], "header_count": 0, "row_count": 20}, {"headers": ["序号"], "header_count": 1, "row_count": 1}, {"headers": [], "header_count": 0, "row_count": 20}], "features": {"has_search": false, "has_pagination": true, "has_add_button": true, "has_export_button": true, "has_edit_button": false, "has_delete_button": false, "has_filter": false, "has_tabs": true, "has_modal": true, "has_upload": false}, "page_title": "基金会业务管理系统", "content_preview": "公益组织合作企业合作捐赠人合作统筹项目其他项目服务项目投资理财修改审批记录\n申请编号：\n名称：\n状态：\n负责人：\n业务领域：\n标签：\n新增 查询 重置 导出\n\t申请编号\t名称\t申请日期\t状态【流程节点】审核人\t负责人\t申请部门\t业务领域\t标签\t\t\n\t\nYWXM202501050\n\t\n联劝（北京乐平）项目\n\t\n2025-05-08\n\t\n审核通过\n\t\n张慧雯\n\t\n大客户快动组\n\t\n社区建设\n\t\n--\n\t\n\t\nYWXM202501048\n\t\n联劝（它基金）项目\n\t\n2025-03-24\n\t\n审核通过\n\t\n张慧雯\n\t\n大客户快动组\n\t\n社区建设\n\t\n--\n\t\n\t\nYWXM202501047\n\t\n联劝（", "url_path": "/business/project/gyzzhz", "analysis_timestamp": "2025-06-30T17:45:58.712345"}}, {"parent_menu": "业务管理", "sub_menu": "可变配置", "url": "http://csboss.lianquan.org.cn/business/project/gyzzhz", "title": "基金会业务管理系统", "screenshot": "enhanced_sub_menu_业务管理_可变配置.png", "timestamp": "2025-06-30T17:46:11.028472", "exploration_method": "enhanced", "content_analysis": {"elements": {"buttons_count": 72, "tables_count": 7, "forms_count": 2, "inputs_count": 22}, "buttons": [{"text": "新增", "type": "button", "class": "el-button middle-space add-button el-button--primary el-button--small"}, {"text": "查询", "type": "button", "class": "el-button middle-space el-button--primary el-button--small"}, {"text": "重置", "type": "button", "class": "el-button middle-space el-button--default el-button--small"}, {"text": "导出", "type": "button", "class": "el-button middle-space el-button--small"}], "tables": [{"headers": ["申请编号", "名称", "申请日期", "状态【流程节点】审核人", "负责人", "申请部门", "业务领域", "标签"], "header_count": 8, "row_count": 63}, {"headers": ["申请编号", "名称", "申请日期", "状态【流程节点】审核人", "负责人", "申请部门", "业务领域", "标签"], "header_count": 8, "row_count": 1}, {"headers": [], "header_count": 0, "row_count": 20}, {"headers": ["序号"], "header_count": 1, "row_count": 1}, {"headers": [], "header_count": 0, "row_count": 20}], "features": {"has_search": false, "has_pagination": true, "has_add_button": true, "has_export_button": true, "has_edit_button": false, "has_delete_button": false, "has_filter": false, "has_tabs": true, "has_modal": true, "has_upload": false}, "page_title": "基金会业务管理系统", "content_preview": "公益组织合作企业合作捐赠人合作统筹项目其他项目服务项目投资理财修改审批记录\n申请编号：\n名称：\n状态：\n负责人：\n业务领域：\n标签：\n新增 查询 重置 导出\n\t申请编号\t名称\t申请日期\t状态【流程节点】审核人\t负责人\t申请部门\t业务领域\t标签\t\t\n\t\nYWXM202501050\n\t\n联劝（北京乐平）项目\n\t\n2025-05-08\n\t\n审核通过\n\t\n张慧雯\n\t\n大客户快动组\n\t\n社区建设\n\t\n--\n\t\n\t\nYWXM202501048\n\t\n联劝（它基金）项目\n\t\n2025-03-24\n\t\n审核通过\n\t\n张慧雯\n\t\n大客户快动组\n\t\n社区建设\n\t\n--\n\t\n\t\nYWXM202501047\n\t\n联劝（", "url_path": "/business/project/gyzzhz", "analysis_timestamp": "2025-06-30T17:46:11.195608"}}, {"parent_menu": "业务管理", "sub_menu": "合作方管理", "url": "http://csboss.lianquan.org.cn/ins", "title": "基金会业务管理系统", "screenshot": "enhanced_sub_menu_业务管理_合作方管理.png", "timestamp": "2025-06-30T17:46:23.595174", "exploration_method": "enhanced", "content_analysis": {"elements": {"buttons_count": 85, "tables_count": 7, "forms_count": 1, "inputs_count": 23}, "buttons": [{"text": "相关说明", "type": "button", "class": "el-button middle-space el-button--primary el-button--small"}, {"text": "审核者设置", "type": "button", "class": "el-button el-button--default el-button--small"}, {"text": "新增", "type": "button", "class": "el-button middle-space add-button el-button--primary el-button--small"}, {"text": "查询", "type": "button", "class": "el-button middle-space el-button--primary el-button--small"}, {"text": "重置", "type": "button", "class": "el-button middle-space el-button--default el-button--small"}, {"text": "导出", "type": "button", "class": "el-button middle-space export-btn el-button--default el-button--small"}], "tables": [{"headers": ["合作方编号", "合作方类型", "机构名称", "机构对接人姓名", "合作期间", "合作状态", "累计合作项目数量", "进行中的合作项目", "标签"], "header_count": 9, "row_count": 63}, {"headers": ["合作方编号", "合作方类型", "机构名称", "机构对接人姓名", "合作期间", "合作状态", "累计合作项目数量", "进行中的合作项目", "标签"], "header_count": 9, "row_count": 1}, {"headers": [], "header_count": 0, "row_count": 20}, {"headers": ["序号"], "header_count": 1, "row_count": 1}, {"headers": [], "header_count": 0, "row_count": 20}], "features": {"has_search": true, "has_pagination": true, "has_add_button": true, "has_export_button": true, "has_edit_button": false, "has_delete_button": false, "has_filter": false, "has_tabs": true, "has_modal": true, "has_upload": false}, "page_title": "基金会业务管理系统", "content_preview": "社会组织个人类型企业类型其他组织\n合作方类型：\n机构名称：\n对接人姓名：\n合作状态：\n标签：\n联劝对接人：\n相关说明 审核者设置 新增 查询 重置 导出\n\t合作方编号\t合作方类型\t机构名称\t机构对接人姓名\t合作期间\t合作状态\t累计合作项目数量\t进行中的合作项目\t标签\t联劝对接人\t\t\n\t\nINSGG202500592\n\t\n--\n\t\n福建省担当者行动教育基金会2\n\t\n安庆旭\n\t\n~\n\t\n未合作\n\t\n--\n\t\n--\n\t\n--\n\t\n--\n\t\n\t\nINSGG202500910\n\t\n执行方\n\t\n佳县慈爱社会公益事业服务中心\n\t\n屈红艳\n\t\n~\n\t\n未合作\n\t\n--\n\t\n--\n\t\n--\n\t\n--\n\t\n\t\nI", "url_path": "/ins", "analysis_timestamp": "2025-06-30T17:46:23.795105"}}, {"parent_menu": "业务管理", "sub_menu": "合同管理", "url": "http://csboss.lianquan.org.cn/contract", "title": "基金会业务管理系统", "screenshot": "enhanced_sub_menu_业务管理_合同管理.png", "timestamp": "2025-06-30T17:46:36.180055", "exploration_method": "enhanced", "content_analysis": {"elements": {"buttons_count": 82, "tables_count": 7, "forms_count": 1, "inputs_count": 24}, "buttons": [{"text": "提醒设置", "type": "button", "class": "el-button left el-button--small"}, {"text": "新增", "type": "button", "class": "el-button left middle-space add-button el-button--primary el-button--small"}, {"text": "导出n天内即将过期的合同", "type": "button", "class": "el-button el-button--default el-button--small"}, {"text": "查询", "type": "button", "class": "el-button middle-space el-button--primary el-button--small"}, {"text": "重置", "type": "button", "class": "el-button middle-space el-button--default el-button--small"}, {"text": "导出", "type": "button", "class": "el-button middle-space export-btn el-button--default el-button--small"}, {"text": "更多", "type": "button", "class": "el-button middle-space el-button--default el-button--small"}], "tables": [{"headers": ["合同编号", "合同名称", "合同类型", "合同类别", "签约单位", "合同金额", "合同状态【流程节点】审核人", "申请人", "申请部门"], "header_count": 9, "row_count": 63}, {"headers": ["合同编号", "合同名称", "合同类型", "合同类别", "签约单位", "合同金额", "合同状态【流程节点】审核人", "申请人", "申请部门"], "header_count": 9, "row_count": 1}, {"headers": [], "header_count": 0, "row_count": 20}, {"headers": ["序号"], "header_count": 1, "row_count": 1}, {"headers": [], "header_count": 0, "row_count": 20}], "features": {"has_search": false, "has_pagination": true, "has_add_button": true, "has_export_button": true, "has_edit_button": false, "has_delete_button": false, "has_filter": false, "has_tabs": true, "has_modal": true, "has_upload": false}, "page_title": "基金会业务管理系统", "content_preview": "合同管理作废审批记录\n合同编号：\n合同名称：\n合同类型：\n合同类别：\n合同状态：\n签约单位：\n提醒设置\n新增\n导出n天内即将过期的合同 查询 重置 导出 更多\n\t合同编号\t合同名称\t合同类型\t合同类别\t签约单位\t合同金额\n\t合同状态【流程节点】审核人\t申请人\t申请部门\t申请日期\t签约有效期\t标签\t\t\n\t\nHT2025999\n\t\n资助协议\n\t\n付款\n\t\n资助协议\n\t\n佛吉亚（中国）投资有限公司，无锡市德盛社会工作服务中心\n\t\n118,000.00\n\t\n草稿\n\t\n何松\n\t\n基金会\n\t\n--\n\t\n2021-11-30~2022-08-31\n\t\n--\n\t\n\t\nHT2025998\n\t\n资助协议\n\t\n付款", "url_path": "/contract", "analysis_timestamp": "2025-06-30T17:46:36.364916"}}, {"parent_menu": "业务管理", "sub_menu": "安全审计", "url": "http://csboss.lianquan.org.cn/contract", "title": "基金会业务管理系统", "screenshot": "enhanced_sub_menu_业务管理_安全审计.png", "timestamp": "2025-06-30T17:46:48.678910", "exploration_method": "enhanced", "content_analysis": {"elements": {"buttons_count": 82, "tables_count": 7, "forms_count": 1, "inputs_count": 24}, "buttons": [{"text": "提醒设置", "type": "button", "class": "el-button left el-button--small"}, {"text": "新增", "type": "button", "class": "el-button left middle-space add-button el-button--primary el-button--small"}, {"text": "导出n天内即将过期的合同", "type": "button", "class": "el-button el-button--default el-button--small"}, {"text": "查询", "type": "button", "class": "el-button middle-space el-button--primary el-button--small"}, {"text": "重置", "type": "button", "class": "el-button middle-space el-button--default el-button--small"}, {"text": "导出", "type": "button", "class": "el-button middle-space export-btn el-button--default el-button--small"}, {"text": "更多", "type": "button", "class": "el-button middle-space el-button--default el-button--small"}], "tables": [{"headers": ["合同编号", "合同名称", "合同类型", "合同类别", "签约单位", "合同金额", "合同状态【流程节点】审核人", "申请人", "申请部门"], "header_count": 9, "row_count": 63}, {"headers": ["合同编号", "合同名称", "合同类型", "合同类别", "签约单位", "合同金额", "合同状态【流程节点】审核人", "申请人", "申请部门"], "header_count": 9, "row_count": 1}, {"headers": [], "header_count": 0, "row_count": 20}, {"headers": ["序号"], "header_count": 1, "row_count": 1}, {"headers": [], "header_count": 0, "row_count": 20}], "features": {"has_search": false, "has_pagination": true, "has_add_button": true, "has_export_button": true, "has_edit_button": false, "has_delete_button": false, "has_filter": false, "has_tabs": true, "has_modal": true, "has_upload": false}, "page_title": "基金会业务管理系统", "content_preview": "合同管理作废审批记录\n合同编号：\n合同名称：\n合同类型：\n合同类别：\n合同状态：\n签约单位：\n提醒设置\n新增\n导出n天内即将过期的合同 查询 重置 导出 更多\n\t合同编号\t合同名称\t合同类型\t合同类别\t签约单位\t合同金额\n\t合同状态【流程节点】审核人\t申请人\t申请部门\t申请日期\t签约有效期\t标签\t\t\n\t\nHT2025999\n\t\n资助协议\n\t\n付款\n\t\n资助协议\n\t\n佛吉亚（中国）投资有限公司，无锡市德盛社会工作服务中心\n\t\n118,000.00\n\t\n草稿\n\t\n何松\n\t\n基金会\n\t\n--\n\t\n2021-11-30~2022-08-31\n\t\n--\n\t\n\t\nHT2025998\n\t\n资助协议\n\t\n付款", "url_path": "/contract", "analysis_timestamp": "2025-06-30T17:46:48.858342"}}, {"parent_menu": "业务管理", "sub_menu": "开票管理", "url": "http://csboss.lianquan.org.cn/contract", "title": "基金会业务管理系统", "screenshot": "enhanced_sub_menu_业务管理_开票管理.png", "timestamp": "2025-06-30T17:47:01.174272", "exploration_method": "enhanced", "content_analysis": {"elements": {"buttons_count": 82, "tables_count": 7, "forms_count": 1, "inputs_count": 24}, "buttons": [{"text": "提醒设置", "type": "button", "class": "el-button left el-button--small"}, {"text": "新增", "type": "button", "class": "el-button left middle-space add-button el-button--primary el-button--small"}, {"text": "导出n天内即将过期的合同", "type": "button", "class": "el-button el-button--default el-button--small"}, {"text": "查询", "type": "button", "class": "el-button middle-space el-button--primary el-button--small"}, {"text": "重置", "type": "button", "class": "el-button middle-space el-button--default el-button--small"}, {"text": "导出", "type": "button", "class": "el-button middle-space export-btn el-button--default el-button--small"}, {"text": "更多", "type": "button", "class": "el-button middle-space el-button--default el-button--small"}], "tables": [{"headers": ["合同编号", "合同名称", "合同类型", "合同类别", "签约单位", "合同金额", "合同状态【流程节点】审核人", "申请人", "申请部门"], "header_count": 9, "row_count": 63}, {"headers": ["合同编号", "合同名称", "合同类型", "合同类别", "签约单位", "合同金额", "合同状态【流程节点】审核人", "申请人", "申请部门"], "header_count": 9, "row_count": 1}, {"headers": [], "header_count": 0, "row_count": 20}, {"headers": ["序号"], "header_count": 1, "row_count": 1}, {"headers": [], "header_count": 0, "row_count": 20}], "features": {"has_search": false, "has_pagination": true, "has_add_button": true, "has_export_button": true, "has_edit_button": false, "has_delete_button": false, "has_filter": false, "has_tabs": true, "has_modal": true, "has_upload": false}, "page_title": "基金会业务管理系统", "content_preview": "合同管理作废审批记录\n合同编号：\n合同名称：\n合同类型：\n合同类别：\n合同状态：\n签约单位：\n提醒设置\n新增\n导出n天内即将过期的合同 查询 重置 导出 更多\n\t合同编号\t合同名称\t合同类型\t合同类别\t签约单位\t合同金额\n\t合同状态【流程节点】审核人\t申请人\t申请部门\t申请日期\t签约有效期\t标签\t\t\n\t\nHT2025999\n\t\n资助协议\n\t\n付款\n\t\n资助协议\n\t\n佛吉亚（中国）投资有限公司，无锡市德盛社会工作服务中心\n\t\n118,000.00\n\t\n草稿\n\t\n何松\n\t\n基金会\n\t\n--\n\t\n2021-11-30~2022-08-31\n\t\n--\n\t\n\t\nHT2025998\n\t\n资助协议\n\t\n付款", "url_path": "/contract", "analysis_timestamp": "2025-06-30T17:47:01.354823"}}, {"parent_menu": "业务管理", "sub_menu": "捐方管理", "url": "http://csboss.lianquan.org.cn/donor/manage", "title": "基金会业务管理系统", "screenshot": "enhanced_sub_menu_业务管理_捐方管理.png", "timestamp": "2025-06-30T17:47:13.714552", "exploration_method": "enhanced", "content_analysis": {"elements": {"buttons_count": 15, "tables_count": 7, "forms_count": 1, "inputs_count": 24}, "buttons": [{"text": "新增", "type": "button", "class": "el-button left middle-space add-button el-button--primary el-button--small"}, {"text": "查询", "type": "button", "class": "el-button middle-space el-button--primary el-button--small"}, {"text": "重置", "type": "button", "class": "el-button middle-space el-button--default el-button--small"}, {"text": "导出", "type": "button", "class": "el-button middle-space export-btn el-button--default el-button--small"}, {"text": "更多", "type": "button", "class": "el-button middle-space el-button--default el-button--small"}, {"text": "关闭", "type": "button", "class": "el-button el-button--default el-button--small"}, {"text": "确定", "type": "button", "class": "el-button el-button--primary el-button--small"}], "tables": [{"headers": ["捐方编号", "捐方名称", "捐方简称", "所属小组", "行业类别", "捐方规模", "年营业收入", "企业性质", "上市状态"], "header_count": 9, "row_count": 3}, {"headers": ["捐方编号", "捐方名称", "捐方简称", "所属小组", "行业类别", "捐方规模", "年营业收入", "企业性质", "上市状态"], "header_count": 9, "row_count": 1}, {"headers": [], "header_count": 0, "row_count": 0}, {"headers": ["序号"], "header_count": 1, "row_count": 1}, {"headers": [], "header_count": 0, "row_count": 0}], "features": {"has_search": false, "has_pagination": true, "has_add_button": true, "has_export_button": true, "has_edit_button": false, "has_delete_button": false, "has_filter": false, "has_tabs": true, "has_modal": true, "has_upload": false}, "page_title": "基金会业务管理系统", "content_preview": "企业类型社会组织其他组织个人\n捐方编号：\n捐方名称：\n捐方简称：\n所属小组：\n行业类别：\n捐方规模：\n年营业收入：\n新增\n查询 重置 导出 更多\n\t捐方编号\t捐方名称\t捐方简称\t所属小组\t行业类别\t捐方规模\t年营业收入\t企业性质\t上市状态\t企业成立日期\t业务覆盖范围\t联系地址\t标签\t联系人姓名\t累计管理费金额\t单笔捐赠最大金额\n\t累计捐赠金额\t累计管理费比例\t最新捐赠日期\t累计捐赠项目数量\n\t资助最多次项目名称\t\n\n暂无记录，请更换条件重新搜索\n\n序号\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t操作\n共 0 条\n1\n前往页", "url_path": "/donor/manage", "analysis_timestamp": "2025-06-30T17:47:13.869248"}}, {"parent_menu": "业务管理", "sub_menu": "支出管理", "url": "http://csboss.lianquan.org.cn/donor/manage", "title": "基金会业务管理系统", "screenshot": "enhanced_sub_menu_业务管理_支出管理.png", "timestamp": "2025-06-30T17:47:26.186089", "exploration_method": "enhanced", "content_analysis": {"elements": {"buttons_count": 15, "tables_count": 7, "forms_count": 1, "inputs_count": 24}, "buttons": [{"text": "新增", "type": "button", "class": "el-button left middle-space add-button el-button--primary el-button--small"}, {"text": "查询", "type": "button", "class": "el-button middle-space el-button--primary el-button--small"}, {"text": "重置", "type": "button", "class": "el-button middle-space el-button--default el-button--small"}, {"text": "导出", "type": "button", "class": "el-button middle-space export-btn el-button--default el-button--small"}, {"text": "更多", "type": "button", "class": "el-button middle-space el-button--default el-button--small"}, {"text": "关闭", "type": "button", "class": "el-button el-button--default el-button--small"}, {"text": "确定", "type": "button", "class": "el-button el-button--primary el-button--small"}], "tables": [{"headers": ["捐方编号", "捐方名称", "捐方简称", "所属小组", "行业类别", "捐方规模", "年营业收入", "企业性质", "上市状态"], "header_count": 9, "row_count": 3}, {"headers": ["捐方编号", "捐方名称", "捐方简称", "所属小组", "行业类别", "捐方规模", "年营业收入", "企业性质", "上市状态"], "header_count": 9, "row_count": 1}, {"headers": [], "header_count": 0, "row_count": 0}, {"headers": ["序号"], "header_count": 1, "row_count": 1}, {"headers": [], "header_count": 0, "row_count": 0}], "features": {"has_search": false, "has_pagination": true, "has_add_button": true, "has_export_button": true, "has_edit_button": false, "has_delete_button": false, "has_filter": false, "has_tabs": true, "has_modal": true, "has_upload": false}, "page_title": "基金会业务管理系统", "content_preview": "企业类型社会组织其他组织个人\n捐方编号：\n捐方名称：\n捐方简称：\n所属小组：\n行业类别：\n捐方规模：\n年营业收入：\n新增\n查询 重置 导出 更多\n\t捐方编号\t捐方名称\t捐方简称\t所属小组\t行业类别\t捐方规模\t年营业收入\t企业性质\t上市状态\t企业成立日期\t业务覆盖范围\t联系地址\t标签\t联系人姓名\t累计管理费金额\t单笔捐赠最大金额\n\t累计捐赠金额\t累计管理费比例\t最新捐赠日期\t累计捐赠项目数量\n\t资助最多次项目名称\t\n\n暂无记录，请更换条件重新搜索\n\n序号\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t操作\n共 0 条\n1\n前往页", "url_path": "/donor/manage", "analysis_timestamp": "2025-06-30T17:47:26.444904"}}, {"parent_menu": "资助管理", "sub_menu": "业务管理", "url": "http://csboss.lianquan.org.cn/funding/plan/manage", "title": "基金会业务管理系统", "screenshot": "enhanced_sub_menu_资助管理_业务管理.png", "timestamp": "2025-06-30T17:47:50.434020", "exploration_method": "enhanced", "content_analysis": {"elements": {"buttons_count": 77, "tables_count": 7, "forms_count": 1, "inputs_count": 25}, "buttons": [{"text": "新增", "type": "button", "class": "el-button middle-space add-button el-button--primary el-button--small"}, {"text": "查询", "type": "button", "class": "el-button middle-space el-button--primary el-button--small"}, {"text": "重置", "type": "button", "class": "el-button middle-space el-button--default el-button--small"}, {"text": "导出", "type": "button", "class": "el-button middle-space export-btn el-button--default el-button--small"}], "tables": [{"headers": ["计划编号", "计划名称", "资助类型", "资助领域", "资助申请开放日期", "申请日期", "状态【流程节点】审核人", "标签", "执行机构"], "header_count": 9, "row_count": 63}, {"headers": ["计划编号", "计划名称", "资助类型", "资助领域", "资助申请开放日期", "申请日期", "状态【流程节点】审核人", "标签", "执行机构"], "header_count": 9, "row_count": 1}, {"headers": [], "header_count": 0, "row_count": 20}, {"headers": ["序号"], "header_count": 1, "row_count": 1}, {"headers": [], "header_count": 0, "row_count": 20}], "features": {"has_search": false, "has_pagination": true, "has_add_button": true, "has_export_button": true, "has_edit_button": false, "has_delete_button": false, "has_filter": false, "has_tabs": false, "has_modal": true, "has_upload": false}, "page_title": "基金会业务管理系统", "content_preview": "计划名称：\n资助类型：\n资助领域：\n状态：\n标签：\n执行机构：\n出资资金池：\n新增 查询 重置 导出\n\t计划编号\t计划名称\t资助类型\t资助领域\t资助申请开放日期\t申请日期\t状态【流程节点】审核人\t标签\t执行机构\t出资资金池\t资助项目总数\t已受益总人数\t总资助金额/支出进度\t\t\n\t\nZZJH202500216\n\t\n联劝捐赠发展\n\t\n统筹项目\n\t\n--\n\t\n2025-06-05 ~ 2030-06-30\n\t\n2025-06-05\n\t\n审核通过\n\t\n--\n\t\n查看\n\t\n查看\n\t\n--\n\t\n--\n\t\n0.00\n/\n0.00\n\t\n\t\nZZJH202500215\n\t\n联劝捐赠发展资助\n\t\n统筹项目\n\t\n-", "url_path": "/funding/plan/manage", "analysis_timestamp": "2025-06-30T17:47:50.615721"}}, {"parent_menu": "资助管理", "sub_menu": "可变配置", "url": "http://csboss.lianquan.org.cn/funding/plan/manage", "title": "基金会业务管理系统", "screenshot": "enhanced_sub_menu_资助管理_可变配置.png", "timestamp": "2025-06-30T17:48:02.938106", "exploration_method": "enhanced", "content_analysis": {"elements": {"buttons_count": 77, "tables_count": 7, "forms_count": 1, "inputs_count": 25}, "buttons": [{"text": "新增", "type": "button", "class": "el-button middle-space add-button el-button--primary el-button--small"}, {"text": "查询", "type": "button", "class": "el-button middle-space el-button--primary el-button--small"}, {"text": "重置", "type": "button", "class": "el-button middle-space el-button--default el-button--small"}, {"text": "导出", "type": "button", "class": "el-button middle-space export-btn el-button--default el-button--small"}], "tables": [{"headers": ["计划编号", "计划名称", "资助类型", "资助领域", "资助申请开放日期", "申请日期", "状态【流程节点】审核人", "标签", "执行机构"], "header_count": 9, "row_count": 63}, {"headers": ["计划编号", "计划名称", "资助类型", "资助领域", "资助申请开放日期", "申请日期", "状态【流程节点】审核人", "标签", "执行机构"], "header_count": 9, "row_count": 1}, {"headers": [], "header_count": 0, "row_count": 20}, {"headers": ["序号"], "header_count": 1, "row_count": 1}, {"headers": [], "header_count": 0, "row_count": 20}], "features": {"has_search": false, "has_pagination": true, "has_add_button": true, "has_export_button": true, "has_edit_button": false, "has_delete_button": false, "has_filter": false, "has_tabs": false, "has_modal": true, "has_upload": false}, "page_title": "基金会业务管理系统", "content_preview": "计划名称：\n资助类型：\n资助领域：\n状态：\n标签：\n执行机构：\n出资资金池：\n新增 查询 重置 导出\n\t计划编号\t计划名称\t资助类型\t资助领域\t资助申请开放日期\t申请日期\t状态【流程节点】审核人\t标签\t执行机构\t出资资金池\t资助项目总数\t已受益总人数\t总资助金额/支出进度\t\t\n\t\nZZJH202500216\n\t\n联劝捐赠发展\n\t\n统筹项目\n\t\n--\n\t\n2025-06-05 ~ 2030-06-30\n\t\n2025-06-05\n\t\n审核通过\n\t\n--\n\t\n查看\n\t\n查看\n\t\n--\n\t\n--\n\t\n0.00\n/\n0.00\n\t\n\t\nZZJH202500215\n\t\n联劝捐赠发展资助\n\t\n统筹项目\n\t\n-", "url_path": "/funding/plan/manage", "analysis_timestamp": "2025-06-30T17:48:03.121363"}}, {"parent_menu": "资助管理", "sub_menu": "合作方管理", "url": "http://csboss.lianquan.org.cn/ins", "title": "基金会业务管理系统", "screenshot": "enhanced_sub_menu_资助管理_合作方管理.png", "timestamp": "2025-06-30T17:48:15.506576", "exploration_method": "enhanced", "content_analysis": {"elements": {"buttons_count": 85, "tables_count": 7, "forms_count": 1, "inputs_count": 23}, "buttons": [{"text": "相关说明", "type": "button", "class": "el-button middle-space el-button--primary el-button--small"}, {"text": "审核者设置", "type": "button", "class": "el-button el-button--default el-button--small"}, {"text": "新增", "type": "button", "class": "el-button middle-space add-button el-button--primary el-button--small"}, {"text": "查询", "type": "button", "class": "el-button middle-space el-button--primary el-button--small"}, {"text": "重置", "type": "button", "class": "el-button middle-space el-button--default el-button--small"}, {"text": "导出", "type": "button", "class": "el-button middle-space export-btn el-button--default el-button--small"}], "tables": [{"headers": ["合作方编号", "合作方类型", "机构名称", "机构对接人姓名", "合作期间", "合作状态", "累计合作项目数量", "进行中的合作项目", "标签"], "header_count": 9, "row_count": 63}, {"headers": ["合作方编号", "合作方类型", "机构名称", "机构对接人姓名", "合作期间", "合作状态", "累计合作项目数量", "进行中的合作项目", "标签"], "header_count": 9, "row_count": 1}, {"headers": [], "header_count": 0, "row_count": 20}, {"headers": ["序号"], "header_count": 1, "row_count": 1}, {"headers": [], "header_count": 0, "row_count": 20}], "features": {"has_search": true, "has_pagination": true, "has_add_button": true, "has_export_button": true, "has_edit_button": false, "has_delete_button": false, "has_filter": false, "has_tabs": true, "has_modal": true, "has_upload": false}, "page_title": "基金会业务管理系统", "content_preview": "社会组织个人类型企业类型其他组织\n合作方类型：\n机构名称：\n对接人姓名：\n合作状态：\n标签：\n联劝对接人：\n相关说明 审核者设置 新增 查询 重置 导出\n\t合作方编号\t合作方类型\t机构名称\t机构对接人姓名\t合作期间\t合作状态\t累计合作项目数量\t进行中的合作项目\t标签\t联劝对接人\t\t\n\t\nINSGG202500592\n\t\n--\n\t\n福建省担当者行动教育基金会2\n\t\n安庆旭\n\t\n~\n\t\n未合作\n\t\n--\n\t\n--\n\t\n--\n\t\n--\n\t\n\t\nINSGG202500910\n\t\n执行方\n\t\n佳县慈爱社会公益事业服务中心\n\t\n屈红艳\n\t\n~\n\t\n未合作\n\t\n--\n\t\n--\n\t\n--\n\t\n--\n\t\n\t\nI", "url_path": "/ins", "analysis_timestamp": "2025-06-30T17:48:15.695213"}}, {"parent_menu": "资助管理", "sub_menu": "合同管理", "url": "http://csboss.lianquan.org.cn/contract", "title": "基金会业务管理系统", "screenshot": "enhanced_sub_menu_资助管理_合同管理.png", "timestamp": "2025-06-30T17:48:28.055287", "exploration_method": "enhanced", "content_analysis": {"elements": {"buttons_count": 82, "tables_count": 7, "forms_count": 1, "inputs_count": 24}, "buttons": [{"text": "提醒设置", "type": "button", "class": "el-button left el-button--small"}, {"text": "新增", "type": "button", "class": "el-button left middle-space add-button el-button--primary el-button--small"}, {"text": "导出n天内即将过期的合同", "type": "button", "class": "el-button el-button--default el-button--small"}, {"text": "查询", "type": "button", "class": "el-button middle-space el-button--primary el-button--small"}, {"text": "重置", "type": "button", "class": "el-button middle-space el-button--default el-button--small"}, {"text": "导出", "type": "button", "class": "el-button middle-space export-btn el-button--default el-button--small"}, {"text": "更多", "type": "button", "class": "el-button middle-space el-button--default el-button--small"}], "tables": [{"headers": ["合同编号", "合同名称", "合同类型", "合同类别", "签约单位", "合同金额", "合同状态【流程节点】审核人", "申请人", "申请部门"], "header_count": 9, "row_count": 63}, {"headers": ["合同编号", "合同名称", "合同类型", "合同类别", "签约单位", "合同金额", "合同状态【流程节点】审核人", "申请人", "申请部门"], "header_count": 9, "row_count": 1}, {"headers": [], "header_count": 0, "row_count": 20}, {"headers": ["序号"], "header_count": 1, "row_count": 1}, {"headers": [], "header_count": 0, "row_count": 20}], "features": {"has_search": false, "has_pagination": true, "has_add_button": true, "has_export_button": true, "has_edit_button": false, "has_delete_button": false, "has_filter": false, "has_tabs": true, "has_modal": true, "has_upload": false}, "page_title": "基金会业务管理系统", "content_preview": "合同管理作废审批记录\n合同编号：\n合同名称：\n合同类型：\n合同类别：\n合同状态：\n签约单位：\n提醒设置\n新增\n导出n天内即将过期的合同 查询 重置 导出 更多\n\t合同编号\t合同名称\t合同类型\t合同类别\t签约单位\t合同金额\n\t合同状态【流程节点】审核人\t申请人\t申请部门\t申请日期\t签约有效期\t标签\t\t\n\t\nHT2025999\n\t\n资助协议\n\t\n付款\n\t\n资助协议\n\t\n佛吉亚（中国）投资有限公司，无锡市德盛社会工作服务中心\n\t\n118,000.00\n\t\n草稿\n\t\n何松\n\t\n基金会\n\t\n--\n\t\n2021-11-30~2022-08-31\n\t\n--\n\t\n\t\nHT2025998\n\t\n资助协议\n\t\n付款", "url_path": "/contract", "analysis_timestamp": "2025-06-30T17:48:28.244260"}}, {"parent_menu": "资助管理", "sub_menu": "安全审计", "url": "http://csboss.lianquan.org.cn/contract", "title": "基金会业务管理系统", "screenshot": "enhanced_sub_menu_资助管理_安全审计.png", "timestamp": "2025-06-30T17:48:40.557737", "exploration_method": "enhanced", "content_analysis": {"elements": {"buttons_count": 82, "tables_count": 7, "forms_count": 1, "inputs_count": 24}, "buttons": [{"text": "提醒设置", "type": "button", "class": "el-button left el-button--small"}, {"text": "新增", "type": "button", "class": "el-button left middle-space add-button el-button--primary el-button--small"}, {"text": "导出n天内即将过期的合同", "type": "button", "class": "el-button el-button--default el-button--small"}, {"text": "查询", "type": "button", "class": "el-button middle-space el-button--primary el-button--small"}, {"text": "重置", "type": "button", "class": "el-button middle-space el-button--default el-button--small"}, {"text": "导出", "type": "button", "class": "el-button middle-space export-btn el-button--default el-button--small"}, {"text": "更多", "type": "button", "class": "el-button middle-space el-button--default el-button--small"}], "tables": [{"headers": ["合同编号", "合同名称", "合同类型", "合同类别", "签约单位", "合同金额", "合同状态【流程节点】审核人", "申请人", "申请部门"], "header_count": 9, "row_count": 63}, {"headers": ["合同编号", "合同名称", "合同类型", "合同类别", "签约单位", "合同金额", "合同状态【流程节点】审核人", "申请人", "申请部门"], "header_count": 9, "row_count": 1}, {"headers": [], "header_count": 0, "row_count": 20}, {"headers": ["序号"], "header_count": 1, "row_count": 1}, {"headers": [], "header_count": 0, "row_count": 20}], "features": {"has_search": false, "has_pagination": true, "has_add_button": true, "has_export_button": true, "has_edit_button": false, "has_delete_button": false, "has_filter": false, "has_tabs": true, "has_modal": true, "has_upload": false}, "page_title": "基金会业务管理系统", "content_preview": "合同管理作废审批记录\n合同编号：\n合同名称：\n合同类型：\n合同类别：\n合同状态：\n签约单位：\n提醒设置\n新增\n导出n天内即将过期的合同 查询 重置 导出 更多\n\t合同编号\t合同名称\t合同类型\t合同类别\t签约单位\t合同金额\n\t合同状态【流程节点】审核人\t申请人\t申请部门\t申请日期\t签约有效期\t标签\t\t\n\t\nHT2025999\n\t\n资助协议\n\t\n付款\n\t\n资助协议\n\t\n佛吉亚（中国）投资有限公司，无锡市德盛社会工作服务中心\n\t\n118,000.00\n\t\n草稿\n\t\n何松\n\t\n基金会\n\t\n--\n\t\n2021-11-30~2022-08-31\n\t\n--\n\t\n\t\nHT2025998\n\t\n资助协议\n\t\n付款", "url_path": "/contract", "analysis_timestamp": "2025-06-30T17:48:40.764992"}}, {"parent_menu": "资助管理", "sub_menu": "开票管理", "url": "http://csboss.lianquan.org.cn/contract", "title": "基金会业务管理系统", "screenshot": "enhanced_sub_menu_资助管理_开票管理.png", "timestamp": "2025-06-30T17:48:53.088920", "exploration_method": "enhanced", "content_analysis": {"elements": {"buttons_count": 82, "tables_count": 7, "forms_count": 1, "inputs_count": 24}, "buttons": [{"text": "提醒设置", "type": "button", "class": "el-button left el-button--small"}, {"text": "新增", "type": "button", "class": "el-button left middle-space add-button el-button--primary el-button--small"}, {"text": "导出n天内即将过期的合同", "type": "button", "class": "el-button el-button--default el-button--small"}, {"text": "查询", "type": "button", "class": "el-button middle-space el-button--primary el-button--small"}, {"text": "重置", "type": "button", "class": "el-button middle-space el-button--default el-button--small"}, {"text": "导出", "type": "button", "class": "el-button middle-space export-btn el-button--default el-button--small"}, {"text": "更多", "type": "button", "class": "el-button middle-space el-button--default el-button--small"}], "tables": [{"headers": ["合同编号", "合同名称", "合同类型", "合同类别", "签约单位", "合同金额", "合同状态【流程节点】审核人", "申请人", "申请部门"], "header_count": 9, "row_count": 63}, {"headers": ["合同编号", "合同名称", "合同类型", "合同类别", "签约单位", "合同金额", "合同状态【流程节点】审核人", "申请人", "申请部门"], "header_count": 9, "row_count": 1}, {"headers": [], "header_count": 0, "row_count": 20}, {"headers": ["序号"], "header_count": 1, "row_count": 1}, {"headers": [], "header_count": 0, "row_count": 20}], "features": {"has_search": false, "has_pagination": true, "has_add_button": true, "has_export_button": true, "has_edit_button": false, "has_delete_button": false, "has_filter": false, "has_tabs": true, "has_modal": true, "has_upload": false}, "page_title": "基金会业务管理系统", "content_preview": "合同管理作废审批记录\n合同编号：\n合同名称：\n合同类型：\n合同类别：\n合同状态：\n签约单位：\n提醒设置\n新增\n导出n天内即将过期的合同 查询 重置 导出 更多\n\t合同编号\t合同名称\t合同类型\t合同类别\t签约单位\t合同金额\n\t合同状态【流程节点】审核人\t申请人\t申请部门\t申请日期\t签约有效期\t标签\t\t\n\t\nHT2025999\n\t\n资助协议\n\t\n付款\n\t\n资助协议\n\t\n佛吉亚（中国）投资有限公司，无锡市德盛社会工作服务中心\n\t\n118,000.00\n\t\n草稿\n\t\n何松\n\t\n基金会\n\t\n--\n\t\n2021-11-30~2022-08-31\n\t\n--\n\t\n\t\nHT2025998\n\t\n资助协议\n\t\n付款", "url_path": "/contract", "analysis_timestamp": "2025-06-30T17:48:53.284793"}}, {"parent_menu": "资助管理", "sub_menu": "捐方管理", "url": "http://csboss.lianquan.org.cn/donor/manage", "title": "基金会业务管理系统", "screenshot": "enhanced_sub_menu_资助管理_捐方管理.png", "timestamp": "2025-06-30T17:49:05.631639", "exploration_method": "enhanced", "content_analysis": {"elements": {"buttons_count": 15, "tables_count": 7, "forms_count": 1, "inputs_count": 24}, "buttons": [{"text": "新增", "type": "button", "class": "el-button left middle-space add-button el-button--primary el-button--small"}, {"text": "查询", "type": "button", "class": "el-button middle-space el-button--primary el-button--small"}, {"text": "重置", "type": "button", "class": "el-button middle-space el-button--default el-button--small"}, {"text": "导出", "type": "button", "class": "el-button middle-space export-btn el-button--default el-button--small"}, {"text": "更多", "type": "button", "class": "el-button middle-space el-button--default el-button--small"}, {"text": "关闭", "type": "button", "class": "el-button el-button--default el-button--small"}, {"text": "确定", "type": "button", "class": "el-button el-button--primary el-button--small"}], "tables": [{"headers": ["捐方编号", "捐方名称", "捐方简称", "所属小组", "行业类别", "捐方规模", "年营业收入", "企业性质", "上市状态"], "header_count": 9, "row_count": 3}, {"headers": ["捐方编号", "捐方名称", "捐方简称", "所属小组", "行业类别", "捐方规模", "年营业收入", "企业性质", "上市状态"], "header_count": 9, "row_count": 1}, {"headers": [], "header_count": 0, "row_count": 0}, {"headers": ["序号"], "header_count": 1, "row_count": 1}, {"headers": [], "header_count": 0, "row_count": 0}], "features": {"has_search": false, "has_pagination": true, "has_add_button": true, "has_export_button": true, "has_edit_button": false, "has_delete_button": false, "has_filter": false, "has_tabs": true, "has_modal": true, "has_upload": false}, "page_title": "基金会业务管理系统", "content_preview": "企业类型社会组织其他组织个人\n捐方编号：\n捐方名称：\n捐方简称：\n所属小组：\n行业类别：\n捐方规模：\n年营业收入：\n新增\n查询 重置 导出 更多\n\t捐方编号\t捐方名称\t捐方简称\t所属小组\t行业类别\t捐方规模\t年营业收入\t企业性质\t上市状态\t企业成立日期\t业务覆盖范围\t联系地址\t标签\t联系人姓名\t累计管理费金额\t单笔捐赠最大金额\n\t累计捐赠金额\t累计管理费比例\t最新捐赠日期\t累计捐赠项目数量\n\t资助最多次项目名称\t\n\n暂无记录，请更换条件重新搜索\n\n序号\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t操作\n共 0 条\n1\n前往页", "url_path": "/donor/manage", "analysis_timestamp": "2025-06-30T17:49:05.789117"}}, {"parent_menu": "资助管理", "sub_menu": "支出管理", "url": "http://csboss.lianquan.org.cn/donor/manage", "title": "基金会业务管理系统", "screenshot": "enhanced_sub_menu_资助管理_支出管理.png", "timestamp": "2025-06-30T17:49:18.095683", "exploration_method": "enhanced", "content_analysis": {"elements": {"buttons_count": 15, "tables_count": 7, "forms_count": 1, "inputs_count": 24}, "buttons": [{"text": "新增", "type": "button", "class": "el-button left middle-space add-button el-button--primary el-button--small"}, {"text": "查询", "type": "button", "class": "el-button middle-space el-button--primary el-button--small"}, {"text": "重置", "type": "button", "class": "el-button middle-space el-button--default el-button--small"}, {"text": "导出", "type": "button", "class": "el-button middle-space export-btn el-button--default el-button--small"}, {"text": "更多", "type": "button", "class": "el-button middle-space el-button--default el-button--small"}, {"text": "关闭", "type": "button", "class": "el-button el-button--default el-button--small"}, {"text": "确定", "type": "button", "class": "el-button el-button--primary el-button--small"}], "tables": [{"headers": ["捐方编号", "捐方名称", "捐方简称", "所属小组", "行业类别", "捐方规模", "年营业收入", "企业性质", "上市状态"], "header_count": 9, "row_count": 3}, {"headers": ["捐方编号", "捐方名称", "捐方简称", "所属小组", "行业类别", "捐方规模", "年营业收入", "企业性质", "上市状态"], "header_count": 9, "row_count": 1}, {"headers": [], "header_count": 0, "row_count": 0}, {"headers": ["序号"], "header_count": 1, "row_count": 1}, {"headers": [], "header_count": 0, "row_count": 0}], "features": {"has_search": false, "has_pagination": true, "has_add_button": true, "has_export_button": true, "has_edit_button": false, "has_delete_button": false, "has_filter": false, "has_tabs": true, "has_modal": true, "has_upload": false}, "page_title": "基金会业务管理系统", "content_preview": "企业类型社会组织其他组织个人\n捐方编号：\n捐方名称：\n捐方简称：\n所属小组：\n行业类别：\n捐方规模：\n年营业收入：\n新增\n查询 重置 导出 更多\n\t捐方编号\t捐方名称\t捐方简称\t所属小组\t行业类别\t捐方规模\t年营业收入\t企业性质\t上市状态\t企业成立日期\t业务覆盖范围\t联系地址\t标签\t联系人姓名\t累计管理费金额\t单笔捐赠最大金额\n\t累计捐赠金额\t累计管理费比例\t最新捐赠日期\t累计捐赠项目数量\n\t资助最多次项目名称\t\n\n暂无记录，请更换条件重新搜索\n\n序号\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t操作\n共 0 条\n1\n前往页", "url_path": "/donor/manage", "analysis_timestamp": "2025-06-30T17:49:18.241717"}}, {"parent_menu": "项目管理", "sub_menu": "业务管理", "url": "http://csboss.lianquan.org.cn/exepro/manage", "title": "基金会业务管理系统", "screenshot": "enhanced_sub_menu_项目管理_业务管理.png", "timestamp": "2025-06-30T17:49:42.229991", "exploration_method": "enhanced", "content_analysis": {"elements": {"buttons_count": 144, "tables_count": 7, "forms_count": 1, "inputs_count": 24}, "buttons": [{"text": "新增", "type": "button", "class": "el-button middle-space el-button--primary add-button el-button--default el-button--small"}, {"text": "查询", "type": "button", "class": "el-button middle-space el-button--primary el-button--small"}, {"text": "重置", "type": "button", "class": "el-button middle-space el-button--default el-button--small"}, {"text": "导出", "type": "button", "class": "el-button middle-space export-btn el-button--default el-button--small"}], "tables": [{"headers": ["项目名称", "项目来源", "签约时间", "联劝负责人", "负责部门", "业务领域", "合作方式", "出资资金池名称", "执行机构"], "header_count": 9, "row_count": 63}, {"headers": ["项目名称", "项目来源", "签约时间", "联劝负责人", "负责部门", "业务领域", "合作方式", "出资资金池名称", "执行机构"], "header_count": 9, "row_count": 1}, {"headers": [], "header_count": 0, "row_count": 20}, {"headers": ["序号"], "header_count": 1, "row_count": 1}, {"headers": [], "header_count": 0, "row_count": 20}], "features": {"has_search": false, "has_pagination": true, "has_add_button": true, "has_export_button": true, "has_edit_button": false, "has_delete_button": false, "has_filter": false, "has_tabs": false, "has_modal": true, "has_upload": false}, "page_title": "基金会业务管理系统", "content_preview": "项目名称：\n项目来源：\n联劝负责人：\n负责部门：\n执行机构：\n项目进度：\n标签：\n新增 查询 重置 导出\n\t项目名称\t项目来源\t签约时间\t联劝负责人\t负责部门\t业务领域\t合作方式\t出资资金池名称\t执行机构\t资助计划\t项目款合计（元）\t项目地址\t项目进度【流程节点】审核人\t项目有无调整\t拨款计划\t当前拨款状态\t累计已拨付金额（元）\t已受益人数/计划受益总人数\t已开展活动数/项目总活动数\t执行方联系人名称\t执行方联系方式\t标签\t枢纽NPO监管\t\t\n\t\n2025年“韧性小孩”项目-湖南衡阳\n\t\n资助计划\n\t\n--\n\t\n左璐\n\t\n公益行动快动组\n\t\n儿童关爱（儿童其他）\n\t\n统筹项目\n\t\n韧性小孩心理", "url_path": "/exepro/manage", "analysis_timestamp": "2025-06-30T17:49:42.438438"}}, {"parent_menu": "项目管理", "sub_menu": "可变配置", "url": "http://csboss.lianquan.org.cn/exepro/manage", "title": "基金会业务管理系统", "screenshot": "enhanced_sub_menu_项目管理_可变配置.png", "timestamp": "2025-06-30T17:49:54.758908", "exploration_method": "enhanced", "content_analysis": {"elements": {"buttons_count": 144, "tables_count": 7, "forms_count": 1, "inputs_count": 24}, "buttons": [{"text": "新增", "type": "button", "class": "el-button middle-space el-button--primary add-button el-button--default el-button--small"}, {"text": "查询", "type": "button", "class": "el-button middle-space el-button--primary el-button--small"}, {"text": "重置", "type": "button", "class": "el-button middle-space el-button--default el-button--small"}, {"text": "导出", "type": "button", "class": "el-button middle-space export-btn el-button--default el-button--small"}], "tables": [{"headers": ["项目名称", "项目来源", "签约时间", "联劝负责人", "负责部门", "业务领域", "合作方式", "出资资金池名称", "执行机构"], "header_count": 9, "row_count": 63}, {"headers": ["项目名称", "项目来源", "签约时间", "联劝负责人", "负责部门", "业务领域", "合作方式", "出资资金池名称", "执行机构"], "header_count": 9, "row_count": 1}, {"headers": [], "header_count": 0, "row_count": 20}, {"headers": ["序号"], "header_count": 1, "row_count": 1}, {"headers": [], "header_count": 0, "row_count": 20}], "features": {"has_search": false, "has_pagination": true, "has_add_button": true, "has_export_button": true, "has_edit_button": false, "has_delete_button": false, "has_filter": false, "has_tabs": false, "has_modal": true, "has_upload": false}, "page_title": "基金会业务管理系统", "content_preview": "项目名称：\n项目来源：\n联劝负责人：\n负责部门：\n执行机构：\n项目进度：\n标签：\n新增 查询 重置 导出\n\t项目名称\t项目来源\t签约时间\t联劝负责人\t负责部门\t业务领域\t合作方式\t出资资金池名称\t执行机构\t资助计划\t项目款合计（元）\t项目地址\t项目进度【流程节点】审核人\t项目有无调整\t拨款计划\t当前拨款状态\t累计已拨付金额（元）\t已受益人数/计划受益总人数\t已开展活动数/项目总活动数\t执行方联系人名称\t执行方联系方式\t标签\t枢纽NPO监管\t\t\n\t\n2025年“韧性小孩”项目-湖南衡阳\n\t\n资助计划\n\t\n--\n\t\n左璐\n\t\n公益行动快动组\n\t\n儿童关爱（儿童其他）\n\t\n统筹项目\n\t\n韧性小孩心理", "url_path": "/exepro/manage", "analysis_timestamp": "2025-06-30T17:49:54.983624"}}, {"parent_menu": "项目管理", "sub_menu": "合作方管理", "url": "http://csboss.lianquan.org.cn/ins", "title": "基金会业务管理系统", "screenshot": "enhanced_sub_menu_项目管理_合作方管理.png", "timestamp": "2025-06-30T17:50:07.387372", "exploration_method": "enhanced", "content_analysis": {"elements": {"buttons_count": 85, "tables_count": 7, "forms_count": 1, "inputs_count": 23}, "buttons": [{"text": "相关说明", "type": "button", "class": "el-button middle-space el-button--primary el-button--small"}, {"text": "审核者设置", "type": "button", "class": "el-button el-button--default el-button--small"}, {"text": "新增", "type": "button", "class": "el-button middle-space add-button el-button--primary el-button--small"}, {"text": "查询", "type": "button", "class": "el-button middle-space el-button--primary el-button--small"}, {"text": "重置", "type": "button", "class": "el-button middle-space el-button--default el-button--small"}, {"text": "导出", "type": "button", "class": "el-button middle-space export-btn el-button--default el-button--small"}], "tables": [{"headers": ["合作方编号", "合作方类型", "机构名称", "机构对接人姓名", "合作期间", "合作状态", "累计合作项目数量", "进行中的合作项目", "标签"], "header_count": 9, "row_count": 63}, {"headers": ["合作方编号", "合作方类型", "机构名称", "机构对接人姓名", "合作期间", "合作状态", "累计合作项目数量", "进行中的合作项目", "标签"], "header_count": 9, "row_count": 1}, {"headers": [], "header_count": 0, "row_count": 20}, {"headers": ["序号"], "header_count": 1, "row_count": 1}, {"headers": [], "header_count": 0, "row_count": 20}], "features": {"has_search": true, "has_pagination": true, "has_add_button": true, "has_export_button": true, "has_edit_button": false, "has_delete_button": false, "has_filter": false, "has_tabs": true, "has_modal": true, "has_upload": false}, "page_title": "基金会业务管理系统", "content_preview": "社会组织个人类型企业类型其他组织\n合作方类型：\n机构名称：\n对接人姓名：\n合作状态：\n标签：\n联劝对接人：\n相关说明 审核者设置 新增 查询 重置 导出\n\t合作方编号\t合作方类型\t机构名称\t机构对接人姓名\t合作期间\t合作状态\t累计合作项目数量\t进行中的合作项目\t标签\t联劝对接人\t\t\n\t\nINSGG202500592\n\t\n--\n\t\n福建省担当者行动教育基金会2\n\t\n安庆旭\n\t\n~\n\t\n未合作\n\t\n--\n\t\n--\n\t\n--\n\t\n--\n\t\n\t\nINSGG202500910\n\t\n执行方\n\t\n佳县慈爱社会公益事业服务中心\n\t\n屈红艳\n\t\n~\n\t\n未合作\n\t\n--\n\t\n--\n\t\n--\n\t\n--\n\t\n\t\nI", "url_path": "/ins", "analysis_timestamp": "2025-06-30T17:50:07.580446"}}, {"parent_menu": "项目管理", "sub_menu": "合同管理", "url": "http://csboss.lianquan.org.cn/contract", "title": "基金会业务管理系统", "screenshot": "enhanced_sub_menu_项目管理_合同管理.png", "timestamp": "2025-06-30T17:50:19.962067", "exploration_method": "enhanced", "content_analysis": {"elements": {"buttons_count": 82, "tables_count": 7, "forms_count": 1, "inputs_count": 24}, "buttons": [{"text": "提醒设置", "type": "button", "class": "el-button left el-button--small"}, {"text": "新增", "type": "button", "class": "el-button left middle-space add-button el-button--primary el-button--small"}, {"text": "导出n天内即将过期的合同", "type": "button", "class": "el-button el-button--default el-button--small"}, {"text": "查询", "type": "button", "class": "el-button middle-space el-button--primary el-button--small"}, {"text": "重置", "type": "button", "class": "el-button middle-space el-button--default el-button--small"}, {"text": "导出", "type": "button", "class": "el-button middle-space export-btn el-button--default el-button--small"}, {"text": "更多", "type": "button", "class": "el-button middle-space el-button--default el-button--small"}], "tables": [{"headers": ["合同编号", "合同名称", "合同类型", "合同类别", "签约单位", "合同金额", "合同状态【流程节点】审核人", "申请人", "申请部门"], "header_count": 9, "row_count": 63}, {"headers": ["合同编号", "合同名称", "合同类型", "合同类别", "签约单位", "合同金额", "合同状态【流程节点】审核人", "申请人", "申请部门"], "header_count": 9, "row_count": 1}, {"headers": [], "header_count": 0, "row_count": 20}, {"headers": ["序号"], "header_count": 1, "row_count": 1}, {"headers": [], "header_count": 0, "row_count": 20}], "features": {"has_search": false, "has_pagination": true, "has_add_button": true, "has_export_button": true, "has_edit_button": false, "has_delete_button": false, "has_filter": false, "has_tabs": true, "has_modal": true, "has_upload": false}, "page_title": "基金会业务管理系统", "content_preview": "合同管理作废审批记录\n合同编号：\n合同名称：\n合同类型：\n合同类别：\n合同状态：\n签约单位：\n提醒设置\n新增\n导出n天内即将过期的合同 查询 重置 导出 更多\n\t合同编号\t合同名称\t合同类型\t合同类别\t签约单位\t合同金额\n\t合同状态【流程节点】审核人\t申请人\t申请部门\t申请日期\t签约有效期\t标签\t\t\n\t\nHT2025999\n\t\n资助协议\n\t\n付款\n\t\n资助协议\n\t\n佛吉亚（中国）投资有限公司，无锡市德盛社会工作服务中心\n\t\n118,000.00\n\t\n草稿\n\t\n何松\n\t\n基金会\n\t\n--\n\t\n2021-11-30~2022-08-31\n\t\n--\n\t\n\t\nHT2025998\n\t\n资助协议\n\t\n付款", "url_path": "/contract", "analysis_timestamp": "2025-06-30T17:50:20.149232"}}, {"parent_menu": "项目管理", "sub_menu": "安全审计", "url": "http://csboss.lianquan.org.cn/contract", "title": "基金会业务管理系统", "screenshot": "enhanced_sub_menu_项目管理_安全审计.png", "timestamp": "2025-06-30T17:50:32.477909", "exploration_method": "enhanced", "content_analysis": {"elements": {"buttons_count": 82, "tables_count": 7, "forms_count": 1, "inputs_count": 24}, "buttons": [{"text": "提醒设置", "type": "button", "class": "el-button left el-button--small"}, {"text": "新增", "type": "button", "class": "el-button left middle-space add-button el-button--primary el-button--small"}, {"text": "导出n天内即将过期的合同", "type": "button", "class": "el-button el-button--default el-button--small"}, {"text": "查询", "type": "button", "class": "el-button middle-space el-button--primary el-button--small"}, {"text": "重置", "type": "button", "class": "el-button middle-space el-button--default el-button--small"}, {"text": "导出", "type": "button", "class": "el-button middle-space export-btn el-button--default el-button--small"}, {"text": "更多", "type": "button", "class": "el-button middle-space el-button--default el-button--small"}], "tables": [{"headers": ["合同编号", "合同名称", "合同类型", "合同类别", "签约单位", "合同金额", "合同状态【流程节点】审核人", "申请人", "申请部门"], "header_count": 9, "row_count": 63}, {"headers": ["合同编号", "合同名称", "合同类型", "合同类别", "签约单位", "合同金额", "合同状态【流程节点】审核人", "申请人", "申请部门"], "header_count": 9, "row_count": 1}, {"headers": [], "header_count": 0, "row_count": 20}, {"headers": ["序号"], "header_count": 1, "row_count": 1}, {"headers": [], "header_count": 0, "row_count": 20}], "features": {"has_search": false, "has_pagination": true, "has_add_button": true, "has_export_button": true, "has_edit_button": false, "has_delete_button": false, "has_filter": false, "has_tabs": true, "has_modal": true, "has_upload": false}, "page_title": "基金会业务管理系统", "content_preview": "合同管理作废审批记录\n合同编号：\n合同名称：\n合同类型：\n合同类别：\n合同状态：\n签约单位：\n提醒设置\n新增\n导出n天内即将过期的合同 查询 重置 导出 更多\n\t合同编号\t合同名称\t合同类型\t合同类别\t签约单位\t合同金额\n\t合同状态【流程节点】审核人\t申请人\t申请部门\t申请日期\t签约有效期\t标签\t\t\n\t\nHT2025999\n\t\n资助协议\n\t\n付款\n\t\n资助协议\n\t\n佛吉亚（中国）投资有限公司，无锡市德盛社会工作服务中心\n\t\n118,000.00\n\t\n草稿\n\t\n何松\n\t\n基金会\n\t\n--\n\t\n2021-11-30~2022-08-31\n\t\n--\n\t\n\t\nHT2025998\n\t\n资助协议\n\t\n付款", "url_path": "/contract", "analysis_timestamp": "2025-06-30T17:50:32.662931"}}, {"parent_menu": "项目管理", "sub_menu": "开票管理", "url": "http://csboss.lianquan.org.cn/contract", "title": "基金会业务管理系统", "screenshot": "enhanced_sub_menu_项目管理_开票管理.png", "timestamp": "2025-06-30T17:50:44.995544", "exploration_method": "enhanced", "content_analysis": {"elements": {"buttons_count": 82, "tables_count": 7, "forms_count": 1, "inputs_count": 24}, "buttons": [{"text": "提醒设置", "type": "button", "class": "el-button left el-button--small"}, {"text": "新增", "type": "button", "class": "el-button left middle-space add-button el-button--primary el-button--small"}, {"text": "导出n天内即将过期的合同", "type": "button", "class": "el-button el-button--default el-button--small"}, {"text": "查询", "type": "button", "class": "el-button middle-space el-button--primary el-button--small"}, {"text": "重置", "type": "button", "class": "el-button middle-space el-button--default el-button--small"}, {"text": "导出", "type": "button", "class": "el-button middle-space export-btn el-button--default el-button--small"}, {"text": "更多", "type": "button", "class": "el-button middle-space el-button--default el-button--small"}], "tables": [{"headers": ["合同编号", "合同名称", "合同类型", "合同类别", "签约单位", "合同金额", "合同状态【流程节点】审核人", "申请人", "申请部门"], "header_count": 9, "row_count": 63}, {"headers": ["合同编号", "合同名称", "合同类型", "合同类别", "签约单位", "合同金额", "合同状态【流程节点】审核人", "申请人", "申请部门"], "header_count": 9, "row_count": 1}, {"headers": [], "header_count": 0, "row_count": 20}, {"headers": ["序号"], "header_count": 1, "row_count": 1}, {"headers": [], "header_count": 0, "row_count": 20}], "features": {"has_search": false, "has_pagination": true, "has_add_button": true, "has_export_button": true, "has_edit_button": false, "has_delete_button": false, "has_filter": false, "has_tabs": true, "has_modal": true, "has_upload": false}, "page_title": "基金会业务管理系统", "content_preview": "合同管理作废审批记录\n合同编号：\n合同名称：\n合同类型：\n合同类别：\n合同状态：\n签约单位：\n提醒设置\n新增\n导出n天内即将过期的合同 查询 重置 导出 更多\n\t合同编号\t合同名称\t合同类型\t合同类别\t签约单位\t合同金额\n\t合同状态【流程节点】审核人\t申请人\t申请部门\t申请日期\t签约有效期\t标签\t\t\n\t\nHT2025999\n\t\n资助协议\n\t\n付款\n\t\n资助协议\n\t\n佛吉亚（中国）投资有限公司，无锡市德盛社会工作服务中心\n\t\n118,000.00\n\t\n草稿\n\t\n何松\n\t\n基金会\n\t\n--\n\t\n2021-11-30~2022-08-31\n\t\n--\n\t\n\t\nHT2025998\n\t\n资助协议\n\t\n付款", "url_path": "/contract", "analysis_timestamp": "2025-06-30T17:50:45.173918"}}, {"parent_menu": "项目管理", "sub_menu": "捐方管理", "url": "http://csboss.lianquan.org.cn/donor/manage", "title": "基金会业务管理系统", "screenshot": "enhanced_sub_menu_项目管理_捐方管理.png", "timestamp": "2025-06-30T17:50:57.523495", "exploration_method": "enhanced", "content_analysis": {"elements": {"buttons_count": 15, "tables_count": 7, "forms_count": 1, "inputs_count": 24}, "buttons": [{"text": "新增", "type": "button", "class": "el-button left middle-space add-button el-button--primary el-button--small"}, {"text": "查询", "type": "button", "class": "el-button middle-space el-button--primary el-button--small"}, {"text": "重置", "type": "button", "class": "el-button middle-space el-button--default el-button--small"}, {"text": "导出", "type": "button", "class": "el-button middle-space export-btn el-button--default el-button--small"}, {"text": "更多", "type": "button", "class": "el-button middle-space el-button--default el-button--small"}, {"text": "关闭", "type": "button", "class": "el-button el-button--default el-button--small"}, {"text": "确定", "type": "button", "class": "el-button el-button--primary el-button--small"}], "tables": [{"headers": ["捐方编号", "捐方名称", "捐方简称", "所属小组", "行业类别", "捐方规模", "年营业收入", "企业性质", "上市状态"], "header_count": 9, "row_count": 3}, {"headers": ["捐方编号", "捐方名称", "捐方简称", "所属小组", "行业类别", "捐方规模", "年营业收入", "企业性质", "上市状态"], "header_count": 9, "row_count": 1}, {"headers": [], "header_count": 0, "row_count": 0}, {"headers": ["序号"], "header_count": 1, "row_count": 1}, {"headers": [], "header_count": 0, "row_count": 0}], "features": {"has_search": false, "has_pagination": true, "has_add_button": true, "has_export_button": true, "has_edit_button": false, "has_delete_button": false, "has_filter": false, "has_tabs": true, "has_modal": true, "has_upload": false}, "page_title": "基金会业务管理系统", "content_preview": "企业类型社会组织其他组织个人\n捐方编号：\n捐方名称：\n捐方简称：\n所属小组：\n行业类别：\n捐方规模：\n年营业收入：\n新增\n查询 重置 导出 更多\n\t捐方编号\t捐方名称\t捐方简称\t所属小组\t行业类别\t捐方规模\t年营业收入\t企业性质\t上市状态\t企业成立日期\t业务覆盖范围\t联系地址\t标签\t联系人姓名\t累计管理费金额\t单笔捐赠最大金额\n\t累计捐赠金额\t累计管理费比例\t最新捐赠日期\t累计捐赠项目数量\n\t资助最多次项目名称\t\n\n暂无记录，请更换条件重新搜索\n\n序号\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t操作\n共 0 条\n1\n前往页", "url_path": "/donor/manage", "analysis_timestamp": "2025-06-30T17:50:57.812120"}}, {"parent_menu": "项目管理", "sub_menu": "支出管理", "url": "http://csboss.lianquan.org.cn/donor/manage", "title": "基金会业务管理系统", "screenshot": "enhanced_sub_menu_项目管理_支出管理.png", "timestamp": "2025-06-30T17:51:10.136250", "exploration_method": "enhanced", "content_analysis": {"elements": {"buttons_count": 15, "tables_count": 7, "forms_count": 1, "inputs_count": 24}, "buttons": [{"text": "新增", "type": "button", "class": "el-button left middle-space add-button el-button--primary el-button--small"}, {"text": "查询", "type": "button", "class": "el-button middle-space el-button--primary el-button--small"}, {"text": "重置", "type": "button", "class": "el-button middle-space el-button--default el-button--small"}, {"text": "导出", "type": "button", "class": "el-button middle-space export-btn el-button--default el-button--small"}, {"text": "更多", "type": "button", "class": "el-button middle-space el-button--default el-button--small"}, {"text": "关闭", "type": "button", "class": "el-button el-button--default el-button--small"}, {"text": "确定", "type": "button", "class": "el-button el-button--primary el-button--small"}], "tables": [{"headers": ["捐方编号", "捐方名称", "捐方简称", "所属小组", "行业类别", "捐方规模", "年营业收入", "企业性质", "上市状态"], "header_count": 9, "row_count": 3}, {"headers": ["捐方编号", "捐方名称", "捐方简称", "所属小组", "行业类别", "捐方规模", "年营业收入", "企业性质", "上市状态"], "header_count": 9, "row_count": 1}, {"headers": [], "header_count": 0, "row_count": 0}, {"headers": ["序号"], "header_count": 1, "row_count": 1}, {"headers": [], "header_count": 0, "row_count": 0}], "features": {"has_search": false, "has_pagination": true, "has_add_button": true, "has_export_button": true, "has_edit_button": false, "has_delete_button": false, "has_filter": false, "has_tabs": true, "has_modal": true, "has_upload": false}, "page_title": "基金会业务管理系统", "content_preview": "企业类型社会组织其他组织个人\n捐方编号：\n捐方名称：\n捐方简称：\n所属小组：\n行业类别：\n捐方规模：\n年营业收入：\n新增\n查询 重置 导出 更多\n\t捐方编号\t捐方名称\t捐方简称\t所属小组\t行业类别\t捐方规模\t年营业收入\t企业性质\t上市状态\t企业成立日期\t业务覆盖范围\t联系地址\t标签\t联系人姓名\t累计管理费金额\t单笔捐赠最大金额\n\t累计捐赠金额\t累计管理费比例\t最新捐赠日期\t累计捐赠项目数量\n\t资助最多次项目名称\t\n\n暂无记录，请更换条件重新搜索\n\n序号\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t操作\n共 0 条\n1\n前往页", "url_path": "/donor/manage", "analysis_timestamp": "2025-06-30T17:51:10.281917"}}, {"parent_menu": "开票管理", "sub_menu": "业务管理", "url": "http://csboss.lianquan.org.cn/statistic/bill/board", "title": "基金会业务管理系统", "screenshot": "enhanced_sub_menu_开票管理_业务管理.png", "timestamp": "2025-06-30T17:51:32.718575", "exploration_method": "enhanced", "content_analysis": {"elements": {"buttons_count": 2, "tables_count": 0, "forms_count": 0, "inputs_count": 5}, "buttons": [], "tables": [], "features": {"has_search": false, "has_pagination": false, "has_add_button": false, "has_export_button": false, "has_edit_button": false, "has_delete_button": false, "has_filter": false, "has_tabs": false, "has_modal": true, "has_upload": false}, "page_title": "基金会业务管理系统", "content_preview": "", "url_path": "/statistic/bill/board", "analysis_timestamp": "2025-06-30T17:51:32.740989"}}, {"parent_menu": "开票管理", "sub_menu": "可变配置", "url": "http://csboss.lianquan.org.cn/statistic/bill/board", "title": "基金会业务管理系统", "screenshot": "enhanced_sub_menu_开票管理_可变配置.png", "timestamp": "2025-06-30T17:51:45.101284", "exploration_method": "enhanced", "content_analysis": {"elements": {"buttons_count": 2, "tables_count": 0, "forms_count": 0, "inputs_count": 5}, "buttons": [], "tables": [], "features": {"has_search": false, "has_pagination": false, "has_add_button": false, "has_export_button": false, "has_edit_button": false, "has_delete_button": false, "has_filter": false, "has_tabs": false, "has_modal": true, "has_upload": false}, "page_title": "基金会业务管理系统", "content_preview": "", "url_path": "/statistic/bill/board", "analysis_timestamp": "2025-06-30T17:51:45.121052"}}, {"parent_menu": "开票管理", "sub_menu": "合作方管理", "url": "http://csboss.lianquan.org.cn/ins", "title": "基金会业务管理系统", "screenshot": "enhanced_sub_menu_开票管理_合作方管理.png", "timestamp": "2025-06-30T17:51:57.498096", "exploration_method": "enhanced", "content_analysis": {"elements": {"buttons_count": 85, "tables_count": 7, "forms_count": 1, "inputs_count": 23}, "buttons": [{"text": "相关说明", "type": "button", "class": "el-button middle-space el-button--primary el-button--small"}, {"text": "审核者设置", "type": "button", "class": "el-button el-button--default el-button--small"}, {"text": "新增", "type": "button", "class": "el-button middle-space add-button el-button--primary el-button--small"}, {"text": "查询", "type": "button", "class": "el-button middle-space el-button--primary el-button--small"}, {"text": "重置", "type": "button", "class": "el-button middle-space el-button--default el-button--small"}, {"text": "导出", "type": "button", "class": "el-button middle-space export-btn el-button--default el-button--small"}], "tables": [{"headers": ["合作方编号", "合作方类型", "机构名称", "机构对接人姓名", "合作期间", "合作状态", "累计合作项目数量", "进行中的合作项目", "标签"], "header_count": 9, "row_count": 63}, {"headers": ["合作方编号", "合作方类型", "机构名称", "机构对接人姓名", "合作期间", "合作状态", "累计合作项目数量", "进行中的合作项目", "标签"], "header_count": 9, "row_count": 1}, {"headers": [], "header_count": 0, "row_count": 20}, {"headers": ["序号"], "header_count": 1, "row_count": 1}, {"headers": [], "header_count": 0, "row_count": 20}], "features": {"has_search": true, "has_pagination": true, "has_add_button": true, "has_export_button": true, "has_edit_button": false, "has_delete_button": false, "has_filter": false, "has_tabs": true, "has_modal": true, "has_upload": false}, "page_title": "基金会业务管理系统", "content_preview": "社会组织个人类型企业类型其他组织\n合作方类型：\n机构名称：\n对接人姓名：\n合作状态：\n标签：\n联劝对接人：\n相关说明 审核者设置 新增 查询 重置 导出\n\t合作方编号\t合作方类型\t机构名称\t机构对接人姓名\t合作期间\t合作状态\t累计合作项目数量\t进行中的合作项目\t标签\t联劝对接人\t\t\n\t\nINSGG202500592\n\t\n--\n\t\n福建省担当者行动教育基金会2\n\t\n安庆旭\n\t\n~\n\t\n未合作\n\t\n--\n\t\n--\n\t\n--\n\t\n--\n\t\n\t\nINSGG202500910\n\t\n执行方\n\t\n佳县慈爱社会公益事业服务中心\n\t\n屈红艳\n\t\n~\n\t\n未合作\n\t\n--\n\t\n--\n\t\n--\n\t\n--\n\t\n\t\nI", "url_path": "/ins", "analysis_timestamp": "2025-06-30T17:51:57.685758"}}, {"parent_menu": "开票管理", "sub_menu": "合同管理", "url": "http://csboss.lianquan.org.cn/contract", "title": "基金会业务管理系统", "screenshot": "enhanced_sub_menu_开票管理_合同管理.png", "timestamp": "2025-06-30T17:52:10.062178", "exploration_method": "enhanced", "content_analysis": {"elements": {"buttons_count": 82, "tables_count": 7, "forms_count": 1, "inputs_count": 24}, "buttons": [{"text": "提醒设置", "type": "button", "class": "el-button left el-button--small"}, {"text": "新增", "type": "button", "class": "el-button left middle-space add-button el-button--primary el-button--small"}, {"text": "导出n天内即将过期的合同", "type": "button", "class": "el-button el-button--default el-button--small"}, {"text": "查询", "type": "button", "class": "el-button middle-space el-button--primary el-button--small"}, {"text": "重置", "type": "button", "class": "el-button middle-space el-button--default el-button--small"}, {"text": "导出", "type": "button", "class": "el-button middle-space export-btn el-button--default el-button--small"}, {"text": "更多", "type": "button", "class": "el-button middle-space el-button--default el-button--small"}], "tables": [{"headers": ["合同编号", "合同名称", "合同类型", "合同类别", "签约单位", "合同金额", "合同状态【流程节点】审核人", "申请人", "申请部门"], "header_count": 9, "row_count": 63}, {"headers": ["合同编号", "合同名称", "合同类型", "合同类别", "签约单位", "合同金额", "合同状态【流程节点】审核人", "申请人", "申请部门"], "header_count": 9, "row_count": 1}, {"headers": [], "header_count": 0, "row_count": 20}, {"headers": ["序号"], "header_count": 1, "row_count": 1}, {"headers": [], "header_count": 0, "row_count": 20}], "features": {"has_search": false, "has_pagination": true, "has_add_button": true, "has_export_button": true, "has_edit_button": false, "has_delete_button": false, "has_filter": false, "has_tabs": true, "has_modal": true, "has_upload": false}, "page_title": "基金会业务管理系统", "content_preview": "合同管理作废审批记录\n合同编号：\n合同名称：\n合同类型：\n合同类别：\n合同状态：\n签约单位：\n提醒设置\n新增\n导出n天内即将过期的合同 查询 重置 导出 更多\n\t合同编号\t合同名称\t合同类型\t合同类别\t签约单位\t合同金额\n\t合同状态【流程节点】审核人\t申请人\t申请部门\t申请日期\t签约有效期\t标签\t\t\n\t\nHT2025999\n\t\n资助协议\n\t\n付款\n\t\n资助协议\n\t\n佛吉亚（中国）投资有限公司，无锡市德盛社会工作服务中心\n\t\n118,000.00\n\t\n草稿\n\t\n何松\n\t\n基金会\n\t\n--\n\t\n2021-11-30~2022-08-31\n\t\n--\n\t\n\t\nHT2025998\n\t\n资助协议\n\t\n付款", "url_path": "/contract", "analysis_timestamp": "2025-06-30T17:52:10.260158"}}, {"parent_menu": "开票管理", "sub_menu": "安全审计", "url": "http://csboss.lianquan.org.cn/contract", "title": "基金会业务管理系统", "screenshot": "enhanced_sub_menu_开票管理_安全审计.png", "timestamp": "2025-06-30T17:53:07.428989", "exploration_method": "enhanced", "content_analysis": {"elements": {"buttons_count": 82, "tables_count": 7, "forms_count": 1, "inputs_count": 24}, "buttons": [{"text": "提醒设置", "type": "button", "class": "el-button left el-button--small"}, {"text": "新增", "type": "button", "class": "el-button left middle-space add-button el-button--primary el-button--small"}, {"text": "导出n天内即将过期的合同", "type": "button", "class": "el-button el-button--default el-button--small"}, {"text": "查询", "type": "button", "class": "el-button middle-space el-button--primary el-button--small"}, {"text": "重置", "type": "button", "class": "el-button middle-space el-button--default el-button--small"}, {"text": "导出", "type": "button", "class": "el-button middle-space export-btn el-button--default el-button--small"}, {"text": "更多", "type": "button", "class": "el-button middle-space el-button--default el-button--small"}], "tables": [{"headers": ["合同编号", "合同名称", "合同类型", "合同类别", "签约单位", "合同金额", "合同状态【流程节点】审核人", "申请人", "申请部门"], "header_count": 9, "row_count": 63}, {"headers": ["合同编号", "合同名称", "合同类型", "合同类别", "签约单位", "合同金额", "合同状态【流程节点】审核人", "申请人", "申请部门"], "header_count": 9, "row_count": 1}, {"headers": [], "header_count": 0, "row_count": 20}, {"headers": ["序号"], "header_count": 1, "row_count": 1}, {"headers": [], "header_count": 0, "row_count": 20}], "features": {"has_search": false, "has_pagination": true, "has_add_button": true, "has_export_button": true, "has_edit_button": false, "has_delete_button": false, "has_filter": false, "has_tabs": true, "has_modal": true, "has_upload": false}, "page_title": "基金会业务管理系统", "content_preview": "合同管理作废审批记录\n合同编号：\n合同名称：\n合同类型：\n合同类别：\n合同状态：\n签约单位：\n提醒设置\n新增\n导出n天内即将过期的合同 查询 重置 导出 更多\n\t合同编号\t合同名称\t合同类型\t合同类别\t签约单位\t合同金额\n\t合同状态【流程节点】审核人\t申请人\t申请部门\t申请日期\t签约有效期\t标签\t\t\n\t\nHT2025999\n\t\n资助协议\n\t\n付款\n\t\n资助协议\n\t\n佛吉亚（中国）投资有限公司，无锡市德盛社会工作服务中心\n\t\n118,000.00\n\t\n草稿\n\t\n何松\n\t\n基金会\n\t\n--\n\t\n2021-11-30~2022-08-31\n\t\n--\n\t\n\t\nHT2025998\n\t\n资助协议\n\t\n付款", "url_path": "/contract", "analysis_timestamp": "2025-06-30T17:53:07.613635"}}, {"parent_menu": "开票管理", "sub_menu": "捐方管理", "url": "http://csboss.lianquan.org.cn/donor/manage", "title": "基金会业务管理系统", "screenshot": "enhanced_sub_menu_开票管理_捐方管理.png", "timestamp": "2025-06-30T17:53:19.987089", "exploration_method": "enhanced", "content_analysis": {"elements": {"buttons_count": 15, "tables_count": 7, "forms_count": 1, "inputs_count": 24}, "buttons": [{"text": "新增", "type": "button", "class": "el-button left middle-space add-button el-button--primary el-button--small"}, {"text": "查询", "type": "button", "class": "el-button middle-space el-button--primary el-button--small"}, {"text": "重置", "type": "button", "class": "el-button middle-space el-button--default el-button--small"}, {"text": "导出", "type": "button", "class": "el-button middle-space export-btn el-button--default el-button--small"}, {"text": "更多", "type": "button", "class": "el-button middle-space el-button--default el-button--small"}, {"text": "关闭", "type": "button", "class": "el-button el-button--default el-button--small"}, {"text": "确定", "type": "button", "class": "el-button el-button--primary el-button--small"}], "tables": [{"headers": ["捐方编号", "捐方名称", "捐方简称", "所属小组", "行业类别", "捐方规模", "年营业收入", "企业性质", "上市状态"], "header_count": 9, "row_count": 3}, {"headers": ["捐方编号", "捐方名称", "捐方简称", "所属小组", "行业类别", "捐方规模", "年营业收入", "企业性质", "上市状态"], "header_count": 9, "row_count": 1}, {"headers": [], "header_count": 0, "row_count": 0}, {"headers": ["序号"], "header_count": 1, "row_count": 1}, {"headers": [], "header_count": 0, "row_count": 0}], "features": {"has_search": false, "has_pagination": true, "has_add_button": true, "has_export_button": true, "has_edit_button": false, "has_delete_button": false, "has_filter": false, "has_tabs": true, "has_modal": true, "has_upload": false}, "page_title": "基金会业务管理系统", "content_preview": "企业类型社会组织其他组织个人\n捐方编号：\n捐方名称：\n捐方简称：\n所属小组：\n行业类别：\n捐方规模：\n年营业收入：\n新增\n查询 重置 导出 更多\n\t捐方编号\t捐方名称\t捐方简称\t所属小组\t行业类别\t捐方规模\t年营业收入\t企业性质\t上市状态\t企业成立日期\t业务覆盖范围\t联系地址\t标签\t联系人姓名\t累计管理费金额\t单笔捐赠最大金额\n\t累计捐赠金额\t累计管理费比例\t最新捐赠日期\t累计捐赠项目数量\n\t资助最多次项目名称\t\n\n暂无记录，请更换条件重新搜索\n\n序号\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t操作\n共 0 条\n1\n前往页", "url_path": "/donor/manage", "analysis_timestamp": "2025-06-30T17:53:20.143761"}}, {"parent_menu": "开票管理", "sub_menu": "支出管理", "url": "http://csboss.lianquan.org.cn/donor/manage", "title": "基金会业务管理系统", "screenshot": "enhanced_sub_menu_开票管理_支出管理.png", "timestamp": "2025-06-30T17:53:32.440206", "exploration_method": "enhanced", "content_analysis": {"elements": {"buttons_count": 15, "tables_count": 7, "forms_count": 1, "inputs_count": 24}, "buttons": [{"text": "新增", "type": "button", "class": "el-button left middle-space add-button el-button--primary el-button--small"}, {"text": "查询", "type": "button", "class": "el-button middle-space el-button--primary el-button--small"}, {"text": "重置", "type": "button", "class": "el-button middle-space el-button--default el-button--small"}, {"text": "导出", "type": "button", "class": "el-button middle-space export-btn el-button--default el-button--small"}, {"text": "更多", "type": "button", "class": "el-button middle-space el-button--default el-button--small"}, {"text": "关闭", "type": "button", "class": "el-button el-button--default el-button--small"}, {"text": "确定", "type": "button", "class": "el-button el-button--primary el-button--small"}], "tables": [{"headers": ["捐方编号", "捐方名称", "捐方简称", "所属小组", "行业类别", "捐方规模", "年营业收入", "企业性质", "上市状态"], "header_count": 9, "row_count": 3}, {"headers": ["捐方编号", "捐方名称", "捐方简称", "所属小组", "行业类别", "捐方规模", "年营业收入", "企业性质", "上市状态"], "header_count": 9, "row_count": 1}, {"headers": [], "header_count": 0, "row_count": 0}, {"headers": ["序号"], "header_count": 1, "row_count": 1}, {"headers": [], "header_count": 0, "row_count": 0}], "features": {"has_search": false, "has_pagination": true, "has_add_button": true, "has_export_button": true, "has_edit_button": false, "has_delete_button": false, "has_filter": false, "has_tabs": true, "has_modal": true, "has_upload": false}, "page_title": "基金会业务管理系统", "content_preview": "企业类型社会组织其他组织个人\n捐方编号：\n捐方名称：\n捐方简称：\n所属小组：\n行业类别：\n捐方规模：\n年营业收入：\n新增\n查询 重置 导出 更多\n\t捐方编号\t捐方名称\t捐方简称\t所属小组\t行业类别\t捐方规模\t年营业收入\t企业性质\t上市状态\t企业成立日期\t业务覆盖范围\t联系地址\t标签\t联系人姓名\t累计管理费金额\t单笔捐赠最大金额\n\t累计捐赠金额\t累计管理费比例\t最新捐赠日期\t累计捐赠项目数量\n\t资助最多次项目名称\t\n\n暂无记录，请更换条件重新搜索\n\n序号\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t操作\n共 0 条\n1\n前往页", "url_path": "/donor/manage", "analysis_timestamp": "2025-06-30T17:53:32.582556"}}, {"parent_menu": "合作方管理", "sub_menu": "业务管理", "url": "http://csboss.lianquan.org.cn/ins", "title": "基金会业务管理系统", "screenshot": "enhanced_sub_menu_合作方管理_业务管理.png", "timestamp": "2025-06-30T17:53:56.602330", "exploration_method": "enhanced", "content_analysis": {"elements": {"buttons_count": 85, "tables_count": 7, "forms_count": 1, "inputs_count": 23}, "buttons": [{"text": "相关说明", "type": "button", "class": "el-button middle-space el-button--primary el-button--small"}, {"text": "审核者设置", "type": "button", "class": "el-button el-button--default el-button--small"}, {"text": "新增", "type": "button", "class": "el-button middle-space add-button el-button--primary el-button--small"}, {"text": "查询", "type": "button", "class": "el-button middle-space el-button--primary el-button--small"}, {"text": "重置", "type": "button", "class": "el-button middle-space el-button--default el-button--small"}, {"text": "导出", "type": "button", "class": "el-button middle-space export-btn el-button--default el-button--small"}], "tables": [{"headers": ["合作方编号", "合作方类型", "机构名称", "机构对接人姓名", "合作期间", "合作状态", "累计合作项目数量", "进行中的合作项目", "标签"], "header_count": 9, "row_count": 63}, {"headers": ["合作方编号", "合作方类型", "机构名称", "机构对接人姓名", "合作期间", "合作状态", "累计合作项目数量", "进行中的合作项目", "标签"], "header_count": 9, "row_count": 1}, {"headers": [], "header_count": 0, "row_count": 20}, {"headers": ["序号"], "header_count": 1, "row_count": 1}, {"headers": [], "header_count": 0, "row_count": 20}], "features": {"has_search": true, "has_pagination": true, "has_add_button": true, "has_export_button": true, "has_edit_button": false, "has_delete_button": false, "has_filter": false, "has_tabs": true, "has_modal": true, "has_upload": false}, "page_title": "基金会业务管理系统", "content_preview": "社会组织个人类型企业类型其他组织\n合作方类型：\n机构名称：\n对接人姓名：\n合作状态：\n标签：\n联劝对接人：\n相关说明 审核者设置 新增 查询 重置 导出\n\t合作方编号\t合作方类型\t机构名称\t机构对接人姓名\t合作期间\t合作状态\t累计合作项目数量\t进行中的合作项目\t标签\t联劝对接人\t\t\n\t\nINSGG202500592\n\t\n--\n\t\n福建省担当者行动教育基金会2\n\t\n安庆旭\n\t\n~\n\t\n未合作\n\t\n--\n\t\n--\n\t\n--\n\t\n--\n\t\n\t\nINSGG202500910\n\t\n执行方\n\t\n佳县慈爱社会公益事业服务中心\n\t\n屈红艳\n\t\n~\n\t\n未合作\n\t\n--\n\t\n--\n\t\n--\n\t\n--\n\t\n\t\nI", "url_path": "/ins", "analysis_timestamp": "2025-06-30T17:53:56.798661"}}, {"parent_menu": "合作方管理", "sub_menu": "可变配置", "url": "http://csboss.lianquan.org.cn/ins", "title": "基金会业务管理系统", "screenshot": "enhanced_sub_menu_合作方管理_可变配置.png", "timestamp": "2025-06-30T17:54:09.108103", "exploration_method": "enhanced", "content_analysis": {"elements": {"buttons_count": 85, "tables_count": 7, "forms_count": 1, "inputs_count": 23}, "buttons": [{"text": "相关说明", "type": "button", "class": "el-button middle-space el-button--primary el-button--small"}, {"text": "审核者设置", "type": "button", "class": "el-button el-button--default el-button--small"}, {"text": "新增", "type": "button", "class": "el-button middle-space add-button el-button--primary el-button--small"}, {"text": "查询", "type": "button", "class": "el-button middle-space el-button--primary el-button--small"}, {"text": "重置", "type": "button", "class": "el-button middle-space el-button--default el-button--small"}, {"text": "导出", "type": "button", "class": "el-button middle-space export-btn el-button--default el-button--small"}], "tables": [{"headers": ["合作方编号", "合作方类型", "机构名称", "机构对接人姓名", "合作期间", "合作状态", "累计合作项目数量", "进行中的合作项目", "标签"], "header_count": 9, "row_count": 63}, {"headers": ["合作方编号", "合作方类型", "机构名称", "机构对接人姓名", "合作期间", "合作状态", "累计合作项目数量", "进行中的合作项目", "标签"], "header_count": 9, "row_count": 1}, {"headers": [], "header_count": 0, "row_count": 20}, {"headers": ["序号"], "header_count": 1, "row_count": 1}, {"headers": [], "header_count": 0, "row_count": 20}], "features": {"has_search": true, "has_pagination": true, "has_add_button": true, "has_export_button": true, "has_edit_button": false, "has_delete_button": false, "has_filter": false, "has_tabs": true, "has_modal": true, "has_upload": false}, "page_title": "基金会业务管理系统", "content_preview": "社会组织个人类型企业类型其他组织\n合作方类型：\n机构名称：\n对接人姓名：\n合作状态：\n标签：\n联劝对接人：\n相关说明 审核者设置 新增 查询 重置 导出\n\t合作方编号\t合作方类型\t机构名称\t机构对接人姓名\t合作期间\t合作状态\t累计合作项目数量\t进行中的合作项目\t标签\t联劝对接人\t\t\n\t\nINSGG202500592\n\t\n--\n\t\n福建省担当者行动教育基金会2\n\t\n安庆旭\n\t\n~\n\t\n未合作\n\t\n--\n\t\n--\n\t\n--\n\t\n--\n\t\n\t\nINSGG202500910\n\t\n执行方\n\t\n佳县慈爱社会公益事业服务中心\n\t\n屈红艳\n\t\n~\n\t\n未合作\n\t\n--\n\t\n--\n\t\n--\n\t\n--\n\t\n\t\nI", "url_path": "/ins", "analysis_timestamp": "2025-06-30T17:54:09.294863"}}, {"parent_menu": "合作方管理", "sub_menu": "合作方管理", "url": "http://csboss.lianquan.org.cn/ins", "title": "基金会业务管理系统", "screenshot": "enhanced_sub_menu_合作方管理_合作方管理.png", "timestamp": "2025-06-30T17:54:21.694013", "exploration_method": "enhanced", "content_analysis": {"elements": {"buttons_count": 85, "tables_count": 7, "forms_count": 1, "inputs_count": 23}, "buttons": [{"text": "相关说明", "type": "button", "class": "el-button middle-space el-button--primary el-button--small"}, {"text": "审核者设置", "type": "button", "class": "el-button el-button--default el-button--small"}, {"text": "新增", "type": "button", "class": "el-button middle-space add-button el-button--primary el-button--small"}, {"text": "查询", "type": "button", "class": "el-button middle-space el-button--primary el-button--small"}, {"text": "重置", "type": "button", "class": "el-button middle-space el-button--default el-button--small"}, {"text": "导出", "type": "button", "class": "el-button middle-space export-btn el-button--default el-button--small"}], "tables": [{"headers": ["合作方编号", "合作方类型", "机构名称", "机构对接人姓名", "合作期间", "合作状态", "累计合作项目数量", "进行中的合作项目", "标签"], "header_count": 9, "row_count": 63}, {"headers": ["合作方编号", "合作方类型", "机构名称", "机构对接人姓名", "合作期间", "合作状态", "累计合作项目数量", "进行中的合作项目", "标签"], "header_count": 9, "row_count": 1}, {"headers": [], "header_count": 0, "row_count": 20}, {"headers": ["序号"], "header_count": 1, "row_count": 1}, {"headers": [], "header_count": 0, "row_count": 20}], "features": {"has_search": true, "has_pagination": true, "has_add_button": true, "has_export_button": true, "has_edit_button": false, "has_delete_button": false, "has_filter": false, "has_tabs": true, "has_modal": true, "has_upload": false}, "page_title": "基金会业务管理系统", "content_preview": "社会组织个人类型企业类型其他组织\n合作方类型：\n机构名称：\n对接人姓名：\n合作状态：\n标签：\n联劝对接人：\n相关说明 审核者设置 新增 查询 重置 导出\n\t合作方编号\t合作方类型\t机构名称\t机构对接人姓名\t合作期间\t合作状态\t累计合作项目数量\t进行中的合作项目\t标签\t联劝对接人\t\t\n\t\nINSGG202500592\n\t\n--\n\t\n福建省担当者行动教育基金会2\n\t\n安庆旭\n\t\n~\n\t\n未合作\n\t\n--\n\t\n--\n\t\n--\n\t\n--\n\t\n\t\nINSGG202500910\n\t\n执行方\n\t\n佳县慈爱社会公益事业服务中心\n\t\n屈红艳\n\t\n~\n\t\n未合作\n\t\n--\n\t\n--\n\t\n--\n\t\n--\n\t\n\t\nI", "url_path": "/ins", "analysis_timestamp": "2025-06-30T17:54:21.886942"}}, {"parent_menu": "合作方管理", "sub_menu": "合同管理", "url": "http://csboss.lianquan.org.cn/contract", "title": "基金会业务管理系统", "screenshot": "enhanced_sub_menu_合作方管理_合同管理.png", "timestamp": "2025-06-30T17:54:34.260989", "exploration_method": "enhanced", "content_analysis": {"elements": {"buttons_count": 82, "tables_count": 7, "forms_count": 1, "inputs_count": 24}, "buttons": [{"text": "提醒设置", "type": "button", "class": "el-button left el-button--small"}, {"text": "新增", "type": "button", "class": "el-button left middle-space add-button el-button--primary el-button--small"}, {"text": "导出n天内即将过期的合同", "type": "button", "class": "el-button el-button--default el-button--small"}, {"text": "查询", "type": "button", "class": "el-button middle-space el-button--primary el-button--small"}, {"text": "重置", "type": "button", "class": "el-button middle-space el-button--default el-button--small"}, {"text": "导出", "type": "button", "class": "el-button middle-space export-btn el-button--default el-button--small"}, {"text": "更多", "type": "button", "class": "el-button middle-space el-button--default el-button--small"}], "tables": [{"headers": ["合同编号", "合同名称", "合同类型", "合同类别", "签约单位", "合同金额", "合同状态【流程节点】审核人", "申请人", "申请部门"], "header_count": 9, "row_count": 63}, {"headers": ["合同编号", "合同名称", "合同类型", "合同类别", "签约单位", "合同金额", "合同状态【流程节点】审核人", "申请人", "申请部门"], "header_count": 9, "row_count": 1}, {"headers": [], "header_count": 0, "row_count": 20}, {"headers": ["序号"], "header_count": 1, "row_count": 1}, {"headers": [], "header_count": 0, "row_count": 20}], "features": {"has_search": false, "has_pagination": true, "has_add_button": true, "has_export_button": true, "has_edit_button": false, "has_delete_button": false, "has_filter": false, "has_tabs": true, "has_modal": true, "has_upload": false}, "page_title": "基金会业务管理系统", "content_preview": "合同管理作废审批记录\n合同编号：\n合同名称：\n合同类型：\n合同类别：\n合同状态：\n签约单位：\n提醒设置\n新增\n导出n天内即将过期的合同 查询 重置 导出 更多\n\t合同编号\t合同名称\t合同类型\t合同类别\t签约单位\t合同金额\n\t合同状态【流程节点】审核人\t申请人\t申请部门\t申请日期\t签约有效期\t标签\t\t\n\t\nHT2025999\n\t\n资助协议\n\t\n付款\n\t\n资助协议\n\t\n佛吉亚（中国）投资有限公司，无锡市德盛社会工作服务中心\n\t\n118,000.00\n\t\n草稿\n\t\n何松\n\t\n基金会\n\t\n--\n\t\n2021-11-30~2022-08-31\n\t\n--\n\t\n\t\nHT2025998\n\t\n资助协议\n\t\n付款", "url_path": "/contract", "analysis_timestamp": "2025-06-30T17:54:34.458270"}}, {"parent_menu": "合作方管理", "sub_menu": "安全审计", "url": "http://csboss.lianquan.org.cn/contract", "title": "基金会业务管理系统", "screenshot": "enhanced_sub_menu_合作方管理_安全审计.png", "timestamp": "2025-06-30T17:54:46.779628", "exploration_method": "enhanced", "content_analysis": {"elements": {"buttons_count": 82, "tables_count": 7, "forms_count": 1, "inputs_count": 24}, "buttons": [{"text": "提醒设置", "type": "button", "class": "el-button left el-button--small"}, {"text": "新增", "type": "button", "class": "el-button left middle-space add-button el-button--primary el-button--small"}, {"text": "导出n天内即将过期的合同", "type": "button", "class": "el-button el-button--default el-button--small"}, {"text": "查询", "type": "button", "class": "el-button middle-space el-button--primary el-button--small"}, {"text": "重置", "type": "button", "class": "el-button middle-space el-button--default el-button--small"}, {"text": "导出", "type": "button", "class": "el-button middle-space export-btn el-button--default el-button--small"}, {"text": "更多", "type": "button", "class": "el-button middle-space el-button--default el-button--small"}], "tables": [{"headers": ["合同编号", "合同名称", "合同类型", "合同类别", "签约单位", "合同金额", "合同状态【流程节点】审核人", "申请人", "申请部门"], "header_count": 9, "row_count": 63}, {"headers": ["合同编号", "合同名称", "合同类型", "合同类别", "签约单位", "合同金额", "合同状态【流程节点】审核人", "申请人", "申请部门"], "header_count": 9, "row_count": 1}, {"headers": [], "header_count": 0, "row_count": 20}, {"headers": ["序号"], "header_count": 1, "row_count": 1}, {"headers": [], "header_count": 0, "row_count": 20}], "features": {"has_search": false, "has_pagination": true, "has_add_button": true, "has_export_button": true, "has_edit_button": false, "has_delete_button": false, "has_filter": false, "has_tabs": true, "has_modal": true, "has_upload": false}, "page_title": "基金会业务管理系统", "content_preview": "合同管理作废审批记录\n合同编号：\n合同名称：\n合同类型：\n合同类别：\n合同状态：\n签约单位：\n提醒设置\n新增\n导出n天内即将过期的合同 查询 重置 导出 更多\n\t合同编号\t合同名称\t合同类型\t合同类别\t签约单位\t合同金额\n\t合同状态【流程节点】审核人\t申请人\t申请部门\t申请日期\t签约有效期\t标签\t\t\n\t\nHT2025999\n\t\n资助协议\n\t\n付款\n\t\n资助协议\n\t\n佛吉亚（中国）投资有限公司，无锡市德盛社会工作服务中心\n\t\n118,000.00\n\t\n草稿\n\t\n何松\n\t\n基金会\n\t\n--\n\t\n2021-11-30~2022-08-31\n\t\n--\n\t\n\t\nHT2025998\n\t\n资助协议\n\t\n付款", "url_path": "/contract", "analysis_timestamp": "2025-06-30T17:54:46.964091"}}, {"parent_menu": "合作方管理", "sub_menu": "开票管理", "url": "http://csboss.lianquan.org.cn/contract", "title": "基金会业务管理系统", "screenshot": "enhanced_sub_menu_合作方管理_开票管理.png", "timestamp": "2025-06-30T17:54:59.283731", "exploration_method": "enhanced", "content_analysis": {"elements": {"buttons_count": 82, "tables_count": 7, "forms_count": 1, "inputs_count": 24}, "buttons": [{"text": "提醒设置", "type": "button", "class": "el-button left el-button--small"}, {"text": "新增", "type": "button", "class": "el-button left middle-space add-button el-button--primary el-button--small"}, {"text": "导出n天内即将过期的合同", "type": "button", "class": "el-button el-button--default el-button--small"}, {"text": "查询", "type": "button", "class": "el-button middle-space el-button--primary el-button--small"}, {"text": "重置", "type": "button", "class": "el-button middle-space el-button--default el-button--small"}, {"text": "导出", "type": "button", "class": "el-button middle-space export-btn el-button--default el-button--small"}, {"text": "更多", "type": "button", "class": "el-button middle-space el-button--default el-button--small"}], "tables": [{"headers": ["合同编号", "合同名称", "合同类型", "合同类别", "签约单位", "合同金额", "合同状态【流程节点】审核人", "申请人", "申请部门"], "header_count": 9, "row_count": 63}, {"headers": ["合同编号", "合同名称", "合同类型", "合同类别", "签约单位", "合同金额", "合同状态【流程节点】审核人", "申请人", "申请部门"], "header_count": 9, "row_count": 1}, {"headers": [], "header_count": 0, "row_count": 20}, {"headers": ["序号"], "header_count": 1, "row_count": 1}, {"headers": [], "header_count": 0, "row_count": 20}], "features": {"has_search": false, "has_pagination": true, "has_add_button": true, "has_export_button": true, "has_edit_button": false, "has_delete_button": false, "has_filter": false, "has_tabs": true, "has_modal": true, "has_upload": false}, "page_title": "基金会业务管理系统", "content_preview": "合同管理作废审批记录\n合同编号：\n合同名称：\n合同类型：\n合同类别：\n合同状态：\n签约单位：\n提醒设置\n新增\n导出n天内即将过期的合同 查询 重置 导出 更多\n\t合同编号\t合同名称\t合同类型\t合同类别\t签约单位\t合同金额\n\t合同状态【流程节点】审核人\t申请人\t申请部门\t申请日期\t签约有效期\t标签\t\t\n\t\nHT2025999\n\t\n资助协议\n\t\n付款\n\t\n资助协议\n\t\n佛吉亚（中国）投资有限公司，无锡市德盛社会工作服务中心\n\t\n118,000.00\n\t\n草稿\n\t\n何松\n\t\n基金会\n\t\n--\n\t\n2021-11-30~2022-08-31\n\t\n--\n\t\n\t\nHT2025998\n\t\n资助协议\n\t\n付款", "url_path": "/contract", "analysis_timestamp": "2025-06-30T17:54:59.462435"}}, {"parent_menu": "合作方管理", "sub_menu": "捐方管理", "url": "http://csboss.lianquan.org.cn/donor/manage", "title": "基金会业务管理系统", "screenshot": "enhanced_sub_menu_合作方管理_捐方管理.png", "timestamp": "2025-06-30T17:55:11.813450", "exploration_method": "enhanced", "content_analysis": {"elements": {"buttons_count": 15, "tables_count": 7, "forms_count": 1, "inputs_count": 24}, "buttons": [{"text": "新增", "type": "button", "class": "el-button left middle-space add-button el-button--primary el-button--small"}, {"text": "查询", "type": "button", "class": "el-button middle-space el-button--primary el-button--small"}, {"text": "重置", "type": "button", "class": "el-button middle-space el-button--default el-button--small"}, {"text": "导出", "type": "button", "class": "el-button middle-space export-btn el-button--default el-button--small"}, {"text": "更多", "type": "button", "class": "el-button middle-space el-button--default el-button--small"}, {"text": "关闭", "type": "button", "class": "el-button el-button--default el-button--small"}, {"text": "确定", "type": "button", "class": "el-button el-button--primary el-button--small"}], "tables": [{"headers": ["捐方编号", "捐方名称", "捐方简称", "所属小组", "行业类别", "捐方规模", "年营业收入", "企业性质", "上市状态"], "header_count": 9, "row_count": 3}, {"headers": ["捐方编号", "捐方名称", "捐方简称", "所属小组", "行业类别", "捐方规模", "年营业收入", "企业性质", "上市状态"], "header_count": 9, "row_count": 1}, {"headers": [], "header_count": 0, "row_count": 0}, {"headers": ["序号"], "header_count": 1, "row_count": 1}, {"headers": [], "header_count": 0, "row_count": 0}], "features": {"has_search": false, "has_pagination": true, "has_add_button": true, "has_export_button": true, "has_edit_button": false, "has_delete_button": false, "has_filter": false, "has_tabs": true, "has_modal": true, "has_upload": false}, "page_title": "基金会业务管理系统", "content_preview": "企业类型社会组织其他组织个人\n捐方编号：\n捐方名称：\n捐方简称：\n所属小组：\n行业类别：\n捐方规模：\n年营业收入：\n新增\n查询 重置 导出 更多\n\t捐方编号\t捐方名称\t捐方简称\t所属小组\t行业类别\t捐方规模\t年营业收入\t企业性质\t上市状态\t企业成立日期\t业务覆盖范围\t联系地址\t标签\t联系人姓名\t累计管理费金额\t单笔捐赠最大金额\n\t累计捐赠金额\t累计管理费比例\t最新捐赠日期\t累计捐赠项目数量\n\t资助最多次项目名称\t\n\n暂无记录，请更换条件重新搜索\n\n序号\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t操作\n共 0 条\n1\n前往页", "url_path": "/donor/manage", "analysis_timestamp": "2025-06-30T17:55:11.965065"}}, {"parent_menu": "合作方管理", "sub_menu": "支出管理", "url": "http://csboss.lianquan.org.cn/donor/manage", "title": "基金会业务管理系统", "screenshot": "enhanced_sub_menu_合作方管理_支出管理.png", "timestamp": "2025-06-30T17:55:24.270600", "exploration_method": "enhanced", "content_analysis": {"elements": {"buttons_count": 15, "tables_count": 7, "forms_count": 1, "inputs_count": 24}, "buttons": [{"text": "新增", "type": "button", "class": "el-button left middle-space add-button el-button--primary el-button--small"}, {"text": "查询", "type": "button", "class": "el-button middle-space el-button--primary el-button--small"}, {"text": "重置", "type": "button", "class": "el-button middle-space el-button--default el-button--small"}, {"text": "导出", "type": "button", "class": "el-button middle-space export-btn el-button--default el-button--small"}, {"text": "更多", "type": "button", "class": "el-button middle-space el-button--default el-button--small"}, {"text": "关闭", "type": "button", "class": "el-button el-button--default el-button--small"}, {"text": "确定", "type": "button", "class": "el-button el-button--primary el-button--small"}], "tables": [{"headers": ["捐方编号", "捐方名称", "捐方简称", "所属小组", "行业类别", "捐方规模", "年营业收入", "企业性质", "上市状态"], "header_count": 9, "row_count": 3}, {"headers": ["捐方编号", "捐方名称", "捐方简称", "所属小组", "行业类别", "捐方规模", "年营业收入", "企业性质", "上市状态"], "header_count": 9, "row_count": 1}, {"headers": [], "header_count": 0, "row_count": 0}, {"headers": ["序号"], "header_count": 1, "row_count": 1}, {"headers": [], "header_count": 0, "row_count": 0}], "features": {"has_search": false, "has_pagination": true, "has_add_button": true, "has_export_button": true, "has_edit_button": false, "has_delete_button": false, "has_filter": false, "has_tabs": true, "has_modal": true, "has_upload": false}, "page_title": "基金会业务管理系统", "content_preview": "企业类型社会组织其他组织个人\n捐方编号：\n捐方名称：\n捐方简称：\n所属小组：\n行业类别：\n捐方规模：\n年营业收入：\n新增\n查询 重置 导出 更多\n\t捐方编号\t捐方名称\t捐方简称\t所属小组\t行业类别\t捐方规模\t年营业收入\t企业性质\t上市状态\t企业成立日期\t业务覆盖范围\t联系地址\t标签\t联系人姓名\t累计管理费金额\t单笔捐赠最大金额\n\t累计捐赠金额\t累计管理费比例\t最新捐赠日期\t累计捐赠项目数量\n\t资助最多次项目名称\t\n\n暂无记录，请更换条件重新搜索\n\n序号\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t操作\n共 0 条\n1\n前往页", "url_path": "/donor/manage", "analysis_timestamp": "2025-06-30T17:55:24.411173"}}, {"parent_menu": "合同管理", "sub_menu": "业务管理", "url": "http://csboss.lianquan.org.cn/contract", "title": "基金会业务管理系统", "screenshot": "enhanced_sub_menu_合同管理_业务管理.png", "timestamp": "2025-06-30T17:55:48.370587", "exploration_method": "enhanced", "content_analysis": {"elements": {"buttons_count": 82, "tables_count": 7, "forms_count": 1, "inputs_count": 24}, "buttons": [{"text": "提醒设置", "type": "button", "class": "el-button left el-button--small"}, {"text": "新增", "type": "button", "class": "el-button left middle-space add-button el-button--primary el-button--small"}, {"text": "导出n天内即将过期的合同", "type": "button", "class": "el-button el-button--default el-button--small"}, {"text": "查询", "type": "button", "class": "el-button middle-space el-button--primary el-button--small"}, {"text": "重置", "type": "button", "class": "el-button middle-space el-button--default el-button--small"}, {"text": "导出", "type": "button", "class": "el-button middle-space export-btn el-button--default el-button--small"}, {"text": "更多", "type": "button", "class": "el-button middle-space el-button--default el-button--small"}], "tables": [{"headers": ["合同编号", "合同名称", "合同类型", "合同类别", "签约单位", "合同金额", "合同状态【流程节点】审核人", "申请人", "申请部门"], "header_count": 9, "row_count": 63}, {"headers": ["合同编号", "合同名称", "合同类型", "合同类别", "签约单位", "合同金额", "合同状态【流程节点】审核人", "申请人", "申请部门"], "header_count": 9, "row_count": 1}, {"headers": [], "header_count": 0, "row_count": 20}, {"headers": ["序号"], "header_count": 1, "row_count": 1}, {"headers": [], "header_count": 0, "row_count": 20}], "features": {"has_search": false, "has_pagination": true, "has_add_button": true, "has_export_button": true, "has_edit_button": false, "has_delete_button": false, "has_filter": false, "has_tabs": true, "has_modal": true, "has_upload": false}, "page_title": "基金会业务管理系统", "content_preview": "合同管理作废审批记录\n合同编号：\n合同名称：\n合同类型：\n合同类别：\n合同状态：\n签约单位：\n提醒设置\n新增\n导出n天内即将过期的合同 查询 重置 导出 更多\n\t合同编号\t合同名称\t合同类型\t合同类别\t签约单位\t合同金额\n\t合同状态【流程节点】审核人\t申请人\t申请部门\t申请日期\t签约有效期\t标签\t\t\n\t\nHT2025999\n\t\n资助协议\n\t\n付款\n\t\n资助协议\n\t\n佛吉亚（中国）投资有限公司，无锡市德盛社会工作服务中心\n\t\n118,000.00\n\t\n草稿\n\t\n何松\n\t\n基金会\n\t\n--\n\t\n2021-11-30~2022-08-31\n\t\n--\n\t\n\t\nHT2025998\n\t\n资助协议\n\t\n付款", "url_path": "/contract", "analysis_timestamp": "2025-06-30T17:55:48.661997"}}, {"parent_menu": "合同管理", "sub_menu": "可变配置", "url": "http://csboss.lianquan.org.cn/contract", "title": "基金会业务管理系统", "screenshot": "enhanced_sub_menu_合同管理_可变配置.png", "timestamp": "2025-06-30T17:56:00.987323", "exploration_method": "enhanced", "content_analysis": {"elements": {"buttons_count": 82, "tables_count": 7, "forms_count": 1, "inputs_count": 24}, "buttons": [{"text": "提醒设置", "type": "button", "class": "el-button left el-button--small"}, {"text": "新增", "type": "button", "class": "el-button left middle-space add-button el-button--primary el-button--small"}, {"text": "导出n天内即将过期的合同", "type": "button", "class": "el-button el-button--default el-button--small"}, {"text": "查询", "type": "button", "class": "el-button middle-space el-button--primary el-button--small"}, {"text": "重置", "type": "button", "class": "el-button middle-space el-button--default el-button--small"}, {"text": "导出", "type": "button", "class": "el-button middle-space export-btn el-button--default el-button--small"}, {"text": "更多", "type": "button", "class": "el-button middle-space el-button--default el-button--small"}], "tables": [{"headers": ["合同编号", "合同名称", "合同类型", "合同类别", "签约单位", "合同金额", "合同状态【流程节点】审核人", "申请人", "申请部门"], "header_count": 9, "row_count": 63}, {"headers": ["合同编号", "合同名称", "合同类型", "合同类别", "签约单位", "合同金额", "合同状态【流程节点】审核人", "申请人", "申请部门"], "header_count": 9, "row_count": 1}, {"headers": [], "header_count": 0, "row_count": 20}, {"headers": ["序号"], "header_count": 1, "row_count": 1}, {"headers": [], "header_count": 0, "row_count": 20}], "features": {"has_search": false, "has_pagination": true, "has_add_button": true, "has_export_button": true, "has_edit_button": false, "has_delete_button": false, "has_filter": false, "has_tabs": true, "has_modal": true, "has_upload": false}, "page_title": "基金会业务管理系统", "content_preview": "合同管理作废审批记录\n合同编号：\n合同名称：\n合同类型：\n合同类别：\n合同状态：\n签约单位：\n提醒设置\n新增\n导出n天内即将过期的合同 查询 重置 导出 更多\n\t合同编号\t合同名称\t合同类型\t合同类别\t签约单位\t合同金额\n\t合同状态【流程节点】审核人\t申请人\t申请部门\t申请日期\t签约有效期\t标签\t\t\n\t\nHT2025999\n\t\n资助协议\n\t\n付款\n\t\n资助协议\n\t\n佛吉亚（中国）投资有限公司，无锡市德盛社会工作服务中心\n\t\n118,000.00\n\t\n草稿\n\t\n何松\n\t\n基金会\n\t\n--\n\t\n2021-11-30~2022-08-31\n\t\n--\n\t\n\t\nHT2025998\n\t\n资助协议\n\t\n付款", "url_path": "/contract", "analysis_timestamp": "2025-06-30T17:56:01.174985"}}, {"parent_menu": "合同管理", "sub_menu": "合作方管理", "url": "http://csboss.lianquan.org.cn/ins", "title": "基金会业务管理系统", "screenshot": "enhanced_sub_menu_合同管理_合作方管理.png", "timestamp": "2025-06-30T17:56:13.551842", "exploration_method": "enhanced", "content_analysis": {"elements": {"buttons_count": 85, "tables_count": 7, "forms_count": 1, "inputs_count": 23}, "buttons": [{"text": "相关说明", "type": "button", "class": "el-button middle-space el-button--primary el-button--small"}, {"text": "审核者设置", "type": "button", "class": "el-button el-button--default el-button--small"}, {"text": "新增", "type": "button", "class": "el-button middle-space add-button el-button--primary el-button--small"}, {"text": "查询", "type": "button", "class": "el-button middle-space el-button--primary el-button--small"}, {"text": "重置", "type": "button", "class": "el-button middle-space el-button--default el-button--small"}, {"text": "导出", "type": "button", "class": "el-button middle-space export-btn el-button--default el-button--small"}], "tables": [{"headers": ["合作方编号", "合作方类型", "机构名称", "机构对接人姓名", "合作期间", "合作状态", "累计合作项目数量", "进行中的合作项目", "标签"], "header_count": 9, "row_count": 63}, {"headers": ["合作方编号", "合作方类型", "机构名称", "机构对接人姓名", "合作期间", "合作状态", "累计合作项目数量", "进行中的合作项目", "标签"], "header_count": 9, "row_count": 1}, {"headers": [], "header_count": 0, "row_count": 20}, {"headers": ["序号"], "header_count": 1, "row_count": 1}, {"headers": [], "header_count": 0, "row_count": 20}], "features": {"has_search": true, "has_pagination": true, "has_add_button": true, "has_export_button": true, "has_edit_button": false, "has_delete_button": false, "has_filter": false, "has_tabs": true, "has_modal": true, "has_upload": false}, "page_title": "基金会业务管理系统", "content_preview": "社会组织个人类型企业类型其他组织\n合作方类型：\n机构名称：\n对接人姓名：\n合作状态：\n标签：\n联劝对接人：\n相关说明 审核者设置 新增 查询 重置 导出\n\t合作方编号\t合作方类型\t机构名称\t机构对接人姓名\t合作期间\t合作状态\t累计合作项目数量\t进行中的合作项目\t标签\t联劝对接人\t\t\n\t\nINSGG202500592\n\t\n--\n\t\n福建省担当者行动教育基金会2\n\t\n安庆旭\n\t\n~\n\t\n未合作\n\t\n--\n\t\n--\n\t\n--\n\t\n--\n\t\n\t\nINSGG202500910\n\t\n执行方\n\t\n佳县慈爱社会公益事业服务中心\n\t\n屈红艳\n\t\n~\n\t\n未合作\n\t\n--\n\t\n--\n\t\n--\n\t\n--\n\t\n\t\nI", "url_path": "/ins", "analysis_timestamp": "2025-06-30T17:56:13.737337"}}, {"parent_menu": "合同管理", "sub_menu": "合同管理", "url": "http://csboss.lianquan.org.cn/contract", "title": "基金会业务管理系统", "screenshot": "enhanced_sub_menu_合同管理_合同管理.png", "timestamp": "2025-06-30T17:56:26.211611", "exploration_method": "enhanced", "content_analysis": {"elements": {"buttons_count": 82, "tables_count": 7, "forms_count": 1, "inputs_count": 24}, "buttons": [{"text": "提醒设置", "type": "button", "class": "el-button left el-button--small"}, {"text": "新增", "type": "button", "class": "el-button left middle-space add-button el-button--primary el-button--small"}, {"text": "导出n天内即将过期的合同", "type": "button", "class": "el-button el-button--default el-button--small"}, {"text": "查询", "type": "button", "class": "el-button middle-space el-button--primary el-button--small"}, {"text": "重置", "type": "button", "class": "el-button middle-space el-button--default el-button--small"}, {"text": "导出", "type": "button", "class": "el-button middle-space export-btn el-button--default el-button--small"}, {"text": "更多", "type": "button", "class": "el-button middle-space el-button--default el-button--small"}], "tables": [{"headers": ["合同编号", "合同名称", "合同类型", "合同类别", "签约单位", "合同金额", "合同状态【流程节点】审核人", "申请人", "申请部门"], "header_count": 9, "row_count": 63}, {"headers": ["合同编号", "合同名称", "合同类型", "合同类别", "签约单位", "合同金额", "合同状态【流程节点】审核人", "申请人", "申请部门"], "header_count": 9, "row_count": 1}, {"headers": [], "header_count": 0, "row_count": 20}, {"headers": ["序号"], "header_count": 1, "row_count": 1}, {"headers": [], "header_count": 0, "row_count": 20}], "features": {"has_search": false, "has_pagination": true, "has_add_button": true, "has_export_button": true, "has_edit_button": false, "has_delete_button": false, "has_filter": false, "has_tabs": true, "has_modal": true, "has_upload": false}, "page_title": "基金会业务管理系统", "content_preview": "合同管理作废审批记录\n合同编号：\n合同名称：\n合同类型：\n合同类别：\n合同状态：\n签约单位：\n提醒设置\n新增\n导出n天内即将过期的合同 查询 重置 导出 更多\n\t合同编号\t合同名称\t合同类型\t合同类别\t签约单位\t合同金额\n\t合同状态【流程节点】审核人\t申请人\t申请部门\t申请日期\t签约有效期\t标签\t\t\n\t\nHT2025999\n\t\n资助协议\n\t\n付款\n\t\n资助协议\n\t\n佛吉亚（中国）投资有限公司，无锡市德盛社会工作服务中心\n\t\n118,000.00\n\t\n草稿\n\t\n何松\n\t\n基金会\n\t\n--\n\t\n2021-11-30~2022-08-31\n\t\n--\n\t\n\t\nHT2025998\n\t\n资助协议\n\t\n付款", "url_path": "/contract", "analysis_timestamp": "2025-06-30T17:56:26.390568"}}, {"parent_menu": "合同管理", "sub_menu": "安全审计", "url": "http://csboss.lianquan.org.cn/contract", "title": "基金会业务管理系统", "screenshot": "enhanced_sub_menu_合同管理_安全审计.png", "timestamp": "2025-06-30T17:56:38.711136", "exploration_method": "enhanced", "content_analysis": {"elements": {"buttons_count": 82, "tables_count": 7, "forms_count": 1, "inputs_count": 24}, "buttons": [{"text": "提醒设置", "type": "button", "class": "el-button left el-button--small"}, {"text": "新增", "type": "button", "class": "el-button left middle-space add-button el-button--primary el-button--small"}, {"text": "导出n天内即将过期的合同", "type": "button", "class": "el-button el-button--default el-button--small"}, {"text": "查询", "type": "button", "class": "el-button middle-space el-button--primary el-button--small"}, {"text": "重置", "type": "button", "class": "el-button middle-space el-button--default el-button--small"}, {"text": "导出", "type": "button", "class": "el-button middle-space export-btn el-button--default el-button--small"}, {"text": "更多", "type": "button", "class": "el-button middle-space el-button--default el-button--small"}], "tables": [{"headers": ["合同编号", "合同名称", "合同类型", "合同类别", "签约单位", "合同金额", "合同状态【流程节点】审核人", "申请人", "申请部门"], "header_count": 9, "row_count": 63}, {"headers": ["合同编号", "合同名称", "合同类型", "合同类别", "签约单位", "合同金额", "合同状态【流程节点】审核人", "申请人", "申请部门"], "header_count": 9, "row_count": 1}, {"headers": [], "header_count": 0, "row_count": 20}, {"headers": ["序号"], "header_count": 1, "row_count": 1}, {"headers": [], "header_count": 0, "row_count": 20}], "features": {"has_search": false, "has_pagination": true, "has_add_button": true, "has_export_button": true, "has_edit_button": false, "has_delete_button": false, "has_filter": false, "has_tabs": true, "has_modal": true, "has_upload": false}, "page_title": "基金会业务管理系统", "content_preview": "合同管理作废审批记录\n合同编号：\n合同名称：\n合同类型：\n合同类别：\n合同状态：\n签约单位：\n提醒设置\n新增\n导出n天内即将过期的合同 查询 重置 导出 更多\n\t合同编号\t合同名称\t合同类型\t合同类别\t签约单位\t合同金额\n\t合同状态【流程节点】审核人\t申请人\t申请部门\t申请日期\t签约有效期\t标签\t\t\n\t\nHT2025999\n\t\n资助协议\n\t\n付款\n\t\n资助协议\n\t\n佛吉亚（中国）投资有限公司，无锡市德盛社会工作服务中心\n\t\n118,000.00\n\t\n草稿\n\t\n何松\n\t\n基金会\n\t\n--\n\t\n2021-11-30~2022-08-31\n\t\n--\n\t\n\t\nHT2025998\n\t\n资助协议\n\t\n付款", "url_path": "/contract", "analysis_timestamp": "2025-06-30T17:56:38.904431"}}, {"parent_menu": "合同管理", "sub_menu": "开票管理", "url": "http://csboss.lianquan.org.cn/contract", "title": "基金会业务管理系统", "screenshot": "enhanced_sub_menu_合同管理_开票管理.png", "timestamp": "2025-06-30T17:56:51.225475", "exploration_method": "enhanced", "content_analysis": {"elements": {"buttons_count": 82, "tables_count": 7, "forms_count": 1, "inputs_count": 24}, "buttons": [{"text": "提醒设置", "type": "button", "class": "el-button left el-button--small"}, {"text": "新增", "type": "button", "class": "el-button left middle-space add-button el-button--primary el-button--small"}, {"text": "导出n天内即将过期的合同", "type": "button", "class": "el-button el-button--default el-button--small"}, {"text": "查询", "type": "button", "class": "el-button middle-space el-button--primary el-button--small"}, {"text": "重置", "type": "button", "class": "el-button middle-space el-button--default el-button--small"}, {"text": "导出", "type": "button", "class": "el-button middle-space export-btn el-button--default el-button--small"}, {"text": "更多", "type": "button", "class": "el-button middle-space el-button--default el-button--small"}], "tables": [{"headers": ["合同编号", "合同名称", "合同类型", "合同类别", "签约单位", "合同金额", "合同状态【流程节点】审核人", "申请人", "申请部门"], "header_count": 9, "row_count": 63}, {"headers": ["合同编号", "合同名称", "合同类型", "合同类别", "签约单位", "合同金额", "合同状态【流程节点】审核人", "申请人", "申请部门"], "header_count": 9, "row_count": 1}, {"headers": [], "header_count": 0, "row_count": 20}, {"headers": ["序号"], "header_count": 1, "row_count": 1}, {"headers": [], "header_count": 0, "row_count": 20}], "features": {"has_search": false, "has_pagination": true, "has_add_button": true, "has_export_button": true, "has_edit_button": false, "has_delete_button": false, "has_filter": false, "has_tabs": true, "has_modal": true, "has_upload": false}, "page_title": "基金会业务管理系统", "content_preview": "合同管理作废审批记录\n合同编号：\n合同名称：\n合同类型：\n合同类别：\n合同状态：\n签约单位：\n提醒设置\n新增\n导出n天内即将过期的合同 查询 重置 导出 更多\n\t合同编号\t合同名称\t合同类型\t合同类别\t签约单位\t合同金额\n\t合同状态【流程节点】审核人\t申请人\t申请部门\t申请日期\t签约有效期\t标签\t\t\n\t\nHT2025999\n\t\n资助协议\n\t\n付款\n\t\n资助协议\n\t\n佛吉亚（中国）投资有限公司，无锡市德盛社会工作服务中心\n\t\n118,000.00\n\t\n草稿\n\t\n何松\n\t\n基金会\n\t\n--\n\t\n2021-11-30~2022-08-31\n\t\n--\n\t\n\t\nHT2025998\n\t\n资助协议\n\t\n付款", "url_path": "/contract", "analysis_timestamp": "2025-06-30T17:56:51.393178"}}, {"parent_menu": "合同管理", "sub_menu": "捐方管理", "url": "http://csboss.lianquan.org.cn/donor/manage", "title": "基金会业务管理系统", "screenshot": "enhanced_sub_menu_合同管理_捐方管理.png", "timestamp": "2025-06-30T17:57:03.746204", "exploration_method": "enhanced", "content_analysis": {"elements": {"buttons_count": 15, "tables_count": 7, "forms_count": 1, "inputs_count": 24}, "buttons": [{"text": "新增", "type": "button", "class": "el-button left middle-space add-button el-button--primary el-button--small"}, {"text": "查询", "type": "button", "class": "el-button middle-space el-button--primary el-button--small"}, {"text": "重置", "type": "button", "class": "el-button middle-space el-button--default el-button--small"}, {"text": "导出", "type": "button", "class": "el-button middle-space export-btn el-button--default el-button--small"}, {"text": "更多", "type": "button", "class": "el-button middle-space el-button--default el-button--small"}, {"text": "关闭", "type": "button", "class": "el-button el-button--default el-button--small"}, {"text": "确定", "type": "button", "class": "el-button el-button--primary el-button--small"}], "tables": [{"headers": ["捐方编号", "捐方名称", "捐方简称", "所属小组", "行业类别", "捐方规模", "年营业收入", "企业性质", "上市状态"], "header_count": 9, "row_count": 3}, {"headers": ["捐方编号", "捐方名称", "捐方简称", "所属小组", "行业类别", "捐方规模", "年营业收入", "企业性质", "上市状态"], "header_count": 9, "row_count": 1}, {"headers": [], "header_count": 0, "row_count": 0}, {"headers": ["序号"], "header_count": 1, "row_count": 1}, {"headers": [], "header_count": 0, "row_count": 0}], "features": {"has_search": false, "has_pagination": true, "has_add_button": true, "has_export_button": true, "has_edit_button": false, "has_delete_button": false, "has_filter": false, "has_tabs": true, "has_modal": true, "has_upload": false}, "page_title": "基金会业务管理系统", "content_preview": "企业类型社会组织其他组织个人\n捐方编号：\n捐方名称：\n捐方简称：\n所属小组：\n行业类别：\n捐方规模：\n年营业收入：\n新增\n查询 重置 导出 更多\n\t捐方编号\t捐方名称\t捐方简称\t所属小组\t行业类别\t捐方规模\t年营业收入\t企业性质\t上市状态\t企业成立日期\t业务覆盖范围\t联系地址\t标签\t联系人姓名\t累计管理费金额\t单笔捐赠最大金额\n\t累计捐赠金额\t累计管理费比例\t最新捐赠日期\t累计捐赠项目数量\n\t资助最多次项目名称\t\n\n暂无记录，请更换条件重新搜索\n\n序号\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t操作\n共 0 条\n1\n前往页", "url_path": "/donor/manage", "analysis_timestamp": "2025-06-30T17:57:03.903340"}}, {"parent_menu": "合同管理", "sub_menu": "支出管理", "url": "http://csboss.lianquan.org.cn/donor/manage", "title": "基金会业务管理系统", "screenshot": "enhanced_sub_menu_合同管理_支出管理.png", "timestamp": "2025-06-30T17:57:16.216363", "exploration_method": "enhanced", "content_analysis": {"elements": {"buttons_count": 15, "tables_count": 7, "forms_count": 1, "inputs_count": 24}, "buttons": [{"text": "新增", "type": "button", "class": "el-button left middle-space add-button el-button--primary el-button--small"}, {"text": "查询", "type": "button", "class": "el-button middle-space el-button--primary el-button--small"}, {"text": "重置", "type": "button", "class": "el-button middle-space el-button--default el-button--small"}, {"text": "导出", "type": "button", "class": "el-button middle-space export-btn el-button--default el-button--small"}, {"text": "更多", "type": "button", "class": "el-button middle-space el-button--default el-button--small"}, {"text": "关闭", "type": "button", "class": "el-button el-button--default el-button--small"}, {"text": "确定", "type": "button", "class": "el-button el-button--primary el-button--small"}], "tables": [{"headers": ["捐方编号", "捐方名称", "捐方简称", "所属小组", "行业类别", "捐方规模", "年营业收入", "企业性质", "上市状态"], "header_count": 9, "row_count": 3}, {"headers": ["捐方编号", "捐方名称", "捐方简称", "所属小组", "行业类别", "捐方规模", "年营业收入", "企业性质", "上市状态"], "header_count": 9, "row_count": 1}, {"headers": [], "header_count": 0, "row_count": 0}, {"headers": ["序号"], "header_count": 1, "row_count": 1}, {"headers": [], "header_count": 0, "row_count": 0}], "features": {"has_search": false, "has_pagination": true, "has_add_button": true, "has_export_button": true, "has_edit_button": false, "has_delete_button": false, "has_filter": false, "has_tabs": true, "has_modal": true, "has_upload": false}, "page_title": "基金会业务管理系统", "content_preview": "企业类型社会组织其他组织个人\n捐方编号：\n捐方名称：\n捐方简称：\n所属小组：\n行业类别：\n捐方规模：\n年营业收入：\n新增\n查询 重置 导出 更多\n\t捐方编号\t捐方名称\t捐方简称\t所属小组\t行业类别\t捐方规模\t年营业收入\t企业性质\t上市状态\t企业成立日期\t业务覆盖范围\t联系地址\t标签\t联系人姓名\t累计管理费金额\t单笔捐赠最大金额\n\t累计捐赠金额\t累计管理费比例\t最新捐赠日期\t累计捐赠项目数量\n\t资助最多次项目名称\t\n\n暂无记录，请更换条件重新搜索\n\n序号\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t操作\n共 0 条\n1\n前往页", "url_path": "/donor/manage", "analysis_timestamp": "2025-06-30T17:57:16.364387"}}, {"parent_menu": "捐方管理", "sub_menu": "业务管理", "url": "http://csboss.lianquan.org.cn/donor/manage", "title": "基金会业务管理系统", "screenshot": "enhanced_sub_menu_捐方管理_业务管理.png", "timestamp": "2025-06-30T17:57:40.244585", "exploration_method": "enhanced", "content_analysis": {"elements": {"buttons_count": 15, "tables_count": 7, "forms_count": 1, "inputs_count": 24}, "buttons": [{"text": "新增", "type": "button", "class": "el-button left middle-space add-button el-button--primary el-button--small"}, {"text": "查询", "type": "button", "class": "el-button middle-space el-button--primary el-button--small"}, {"text": "重置", "type": "button", "class": "el-button middle-space el-button--default el-button--small"}, {"text": "导出", "type": "button", "class": "el-button middle-space export-btn el-button--default el-button--small"}, {"text": "更多", "type": "button", "class": "el-button middle-space el-button--default el-button--small"}, {"text": "关闭", "type": "button", "class": "el-button el-button--default el-button--small"}, {"text": "确定", "type": "button", "class": "el-button el-button--primary el-button--small"}], "tables": [{"headers": ["捐方编号", "捐方名称", "捐方简称", "所属小组", "行业类别", "捐方规模", "年营业收入", "企业性质", "上市状态"], "header_count": 9, "row_count": 3}, {"headers": ["捐方编号", "捐方名称", "捐方简称", "所属小组", "行业类别", "捐方规模", "年营业收入", "企业性质", "上市状态"], "header_count": 9, "row_count": 1}, {"headers": [], "header_count": 0, "row_count": 0}, {"headers": ["序号"], "header_count": 1, "row_count": 1}, {"headers": [], "header_count": 0, "row_count": 0}], "features": {"has_search": false, "has_pagination": true, "has_add_button": true, "has_export_button": true, "has_edit_button": false, "has_delete_button": false, "has_filter": false, "has_tabs": true, "has_modal": true, "has_upload": false}, "page_title": "基金会业务管理系统", "content_preview": "企业类型社会组织其他组织个人\n捐方编号：\n捐方名称：\n捐方简称：\n所属小组：\n行业类别：\n捐方规模：\n年营业收入：\n新增\n查询 重置 导出 更多\n\t捐方编号\t捐方名称\t捐方简称\t所属小组\t行业类别\t捐方规模\t年营业收入\t企业性质\t上市状态\t企业成立日期\t业务覆盖范围\t联系地址\t标签\t联系人姓名\t累计管理费金额\t单笔捐赠最大金额\n\t累计捐赠金额\t累计管理费比例\t最新捐赠日期\t累计捐赠项目数量\n\t资助最多次项目名称\t\n\n暂无记录，请更换条件重新搜索\n\n序号\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t操作\n共 0 条\n1\n前往页", "url_path": "/donor/manage", "analysis_timestamp": "2025-06-30T17:57:40.384130"}}, {"parent_menu": "捐方管理", "sub_menu": "可变配置", "url": "http://csboss.lianquan.org.cn/donor/manage", "title": "基金会业务管理系统", "screenshot": "enhanced_sub_menu_捐方管理_可变配置.png", "timestamp": "2025-06-30T17:57:52.681355", "exploration_method": "enhanced", "content_analysis": {"elements": {"buttons_count": 15, "tables_count": 7, "forms_count": 1, "inputs_count": 24}, "buttons": [{"text": "新增", "type": "button", "class": "el-button left middle-space add-button el-button--primary el-button--small"}, {"text": "查询", "type": "button", "class": "el-button middle-space el-button--primary el-button--small"}, {"text": "重置", "type": "button", "class": "el-button middle-space el-button--default el-button--small"}, {"text": "导出", "type": "button", "class": "el-button middle-space export-btn el-button--default el-button--small"}, {"text": "更多", "type": "button", "class": "el-button middle-space el-button--default el-button--small"}, {"text": "关闭", "type": "button", "class": "el-button el-button--default el-button--small"}, {"text": "确定", "type": "button", "class": "el-button el-button--primary el-button--small"}], "tables": [{"headers": ["捐方编号", "捐方名称", "捐方简称", "所属小组", "行业类别", "捐方规模", "年营业收入", "企业性质", "上市状态"], "header_count": 9, "row_count": 3}, {"headers": ["捐方编号", "捐方名称", "捐方简称", "所属小组", "行业类别", "捐方规模", "年营业收入", "企业性质", "上市状态"], "header_count": 9, "row_count": 1}, {"headers": [], "header_count": 0, "row_count": 0}, {"headers": ["序号"], "header_count": 1, "row_count": 1}, {"headers": [], "header_count": 0, "row_count": 0}], "features": {"has_search": false, "has_pagination": true, "has_add_button": true, "has_export_button": true, "has_edit_button": false, "has_delete_button": false, "has_filter": false, "has_tabs": true, "has_modal": true, "has_upload": false}, "page_title": "基金会业务管理系统", "content_preview": "企业类型社会组织其他组织个人\n捐方编号：\n捐方名称：\n捐方简称：\n所属小组：\n行业类别：\n捐方规模：\n年营业收入：\n新增\n查询 重置 导出 更多\n\t捐方编号\t捐方名称\t捐方简称\t所属小组\t行业类别\t捐方规模\t年营业收入\t企业性质\t上市状态\t企业成立日期\t业务覆盖范围\t联系地址\t标签\t联系人姓名\t累计管理费金额\t单笔捐赠最大金额\n\t累计捐赠金额\t累计管理费比例\t最新捐赠日期\t累计捐赠项目数量\n\t资助最多次项目名称\t\n\n暂无记录，请更换条件重新搜索\n\n序号\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t操作\n共 0 条\n1\n前往页", "url_path": "/donor/manage", "analysis_timestamp": "2025-06-30T17:57:52.825006"}}, {"parent_menu": "捐方管理", "sub_menu": "合作方管理", "url": "http://csboss.lianquan.org.cn/ins", "title": "基金会业务管理系统", "screenshot": "enhanced_sub_menu_捐方管理_合作方管理.png", "timestamp": "2025-06-30T17:58:05.186180", "exploration_method": "enhanced", "content_analysis": {"elements": {"buttons_count": 85, "tables_count": 7, "forms_count": 1, "inputs_count": 23}, "buttons": [{"text": "相关说明", "type": "button", "class": "el-button middle-space el-button--primary el-button--small"}, {"text": "审核者设置", "type": "button", "class": "el-button el-button--default el-button--small"}, {"text": "新增", "type": "button", "class": "el-button middle-space add-button el-button--primary el-button--small"}, {"text": "查询", "type": "button", "class": "el-button middle-space el-button--primary el-button--small"}, {"text": "重置", "type": "button", "class": "el-button middle-space el-button--default el-button--small"}, {"text": "导出", "type": "button", "class": "el-button middle-space export-btn el-button--default el-button--small"}], "tables": [{"headers": ["合作方编号", "合作方类型", "机构名称", "机构对接人姓名", "合作期间", "合作状态", "累计合作项目数量", "进行中的合作项目", "标签"], "header_count": 9, "row_count": 63}, {"headers": ["合作方编号", "合作方类型", "机构名称", "机构对接人姓名", "合作期间", "合作状态", "累计合作项目数量", "进行中的合作项目", "标签"], "header_count": 9, "row_count": 1}, {"headers": [], "header_count": 0, "row_count": 20}, {"headers": ["序号"], "header_count": 1, "row_count": 1}, {"headers": [], "header_count": 0, "row_count": 20}], "features": {"has_search": true, "has_pagination": true, "has_add_button": true, "has_export_button": true, "has_edit_button": false, "has_delete_button": false, "has_filter": false, "has_tabs": true, "has_modal": true, "has_upload": false}, "page_title": "基金会业务管理系统", "content_preview": "社会组织个人类型企业类型其他组织\n合作方类型：\n机构名称：\n对接人姓名：\n合作状态：\n标签：\n联劝对接人：\n相关说明 审核者设置 新增 查询 重置 导出\n\t合作方编号\t合作方类型\t机构名称\t机构对接人姓名\t合作期间\t合作状态\t累计合作项目数量\t进行中的合作项目\t标签\t联劝对接人\t\t\n\t\nINSGG202500592\n\t\n--\n\t\n福建省担当者行动教育基金会2\n\t\n安庆旭\n\t\n~\n\t\n未合作\n\t\n--\n\t\n--\n\t\n--\n\t\n--\n\t\n\t\nINSGG202500910\n\t\n执行方\n\t\n佳县慈爱社会公益事业服务中心\n\t\n屈红艳\n\t\n~\n\t\n未合作\n\t\n--\n\t\n--\n\t\n--\n\t\n--\n\t\n\t\nI", "url_path": "/ins", "analysis_timestamp": "2025-06-30T17:58:05.366110"}}, {"parent_menu": "捐方管理", "sub_menu": "合同管理", "url": "http://csboss.lianquan.org.cn/contract", "title": "基金会业务管理系统", "screenshot": "enhanced_sub_menu_捐方管理_合同管理.png", "timestamp": "2025-06-30T17:58:17.713565", "exploration_method": "enhanced", "content_analysis": {"elements": {"buttons_count": 82, "tables_count": 7, "forms_count": 1, "inputs_count": 24}, "buttons": [{"text": "提醒设置", "type": "button", "class": "el-button left el-button--small"}, {"text": "新增", "type": "button", "class": "el-button left middle-space add-button el-button--primary el-button--small"}, {"text": "导出n天内即将过期的合同", "type": "button", "class": "el-button el-button--default el-button--small"}, {"text": "查询", "type": "button", "class": "el-button middle-space el-button--primary el-button--small"}, {"text": "重置", "type": "button", "class": "el-button middle-space el-button--default el-button--small"}, {"text": "导出", "type": "button", "class": "el-button middle-space export-btn el-button--default el-button--small"}, {"text": "更多", "type": "button", "class": "el-button middle-space el-button--default el-button--small"}], "tables": [{"headers": ["合同编号", "合同名称", "合同类型", "合同类别", "签约单位", "合同金额", "合同状态【流程节点】审核人", "申请人", "申请部门"], "header_count": 9, "row_count": 63}, {"headers": ["合同编号", "合同名称", "合同类型", "合同类别", "签约单位", "合同金额", "合同状态【流程节点】审核人", "申请人", "申请部门"], "header_count": 9, "row_count": 1}, {"headers": [], "header_count": 0, "row_count": 20}, {"headers": ["序号"], "header_count": 1, "row_count": 1}, {"headers": [], "header_count": 0, "row_count": 20}], "features": {"has_search": false, "has_pagination": true, "has_add_button": true, "has_export_button": true, "has_edit_button": false, "has_delete_button": false, "has_filter": false, "has_tabs": true, "has_modal": true, "has_upload": false}, "page_title": "基金会业务管理系统", "content_preview": "合同管理作废审批记录\n合同编号：\n合同名称：\n合同类型：\n合同类别：\n合同状态：\n签约单位：\n提醒设置\n新增\n导出n天内即将过期的合同 查询 重置 导出 更多\n\t合同编号\t合同名称\t合同类型\t合同类别\t签约单位\t合同金额\n\t合同状态【流程节点】审核人\t申请人\t申请部门\t申请日期\t签约有效期\t标签\t\t\n\t\nHT2025999\n\t\n资助协议\n\t\n付款\n\t\n资助协议\n\t\n佛吉亚（中国）投资有限公司，无锡市德盛社会工作服务中心\n\t\n118,000.00\n\t\n草稿\n\t\n何松\n\t\n基金会\n\t\n--\n\t\n2021-11-30~2022-08-31\n\t\n--\n\t\n\t\nHT2025998\n\t\n资助协议\n\t\n付款", "url_path": "/contract", "analysis_timestamp": "2025-06-30T17:58:17.911238"}}, {"parent_menu": "捐方管理", "sub_menu": "安全审计", "url": "http://csboss.lianquan.org.cn/contract", "title": "基金会业务管理系统", "screenshot": "enhanced_sub_menu_捐方管理_安全审计.png", "timestamp": "2025-06-30T17:58:30.221482", "exploration_method": "enhanced", "content_analysis": {"elements": {"buttons_count": 82, "tables_count": 7, "forms_count": 1, "inputs_count": 24}, "buttons": [{"text": "提醒设置", "type": "button", "class": "el-button left el-button--small"}, {"text": "新增", "type": "button", "class": "el-button left middle-space add-button el-button--primary el-button--small"}, {"text": "导出n天内即将过期的合同", "type": "button", "class": "el-button el-button--default el-button--small"}, {"text": "查询", "type": "button", "class": "el-button middle-space el-button--primary el-button--small"}, {"text": "重置", "type": "button", "class": "el-button middle-space el-button--default el-button--small"}, {"text": "导出", "type": "button", "class": "el-button middle-space export-btn el-button--default el-button--small"}, {"text": "更多", "type": "button", "class": "el-button middle-space el-button--default el-button--small"}], "tables": [{"headers": ["合同编号", "合同名称", "合同类型", "合同类别", "签约单位", "合同金额", "合同状态【流程节点】审核人", "申请人", "申请部门"], "header_count": 9, "row_count": 63}, {"headers": ["合同编号", "合同名称", "合同类型", "合同类别", "签约单位", "合同金额", "合同状态【流程节点】审核人", "申请人", "申请部门"], "header_count": 9, "row_count": 1}, {"headers": [], "header_count": 0, "row_count": 20}, {"headers": ["序号"], "header_count": 1, "row_count": 1}, {"headers": [], "header_count": 0, "row_count": 20}], "features": {"has_search": false, "has_pagination": true, "has_add_button": true, "has_export_button": true, "has_edit_button": false, "has_delete_button": false, "has_filter": false, "has_tabs": true, "has_modal": true, "has_upload": false}, "page_title": "基金会业务管理系统", "content_preview": "合同管理作废审批记录\n合同编号：\n合同名称：\n合同类型：\n合同类别：\n合同状态：\n签约单位：\n提醒设置\n新增\n导出n天内即将过期的合同 查询 重置 导出 更多\n\t合同编号\t合同名称\t合同类型\t合同类别\t签约单位\t合同金额\n\t合同状态【流程节点】审核人\t申请人\t申请部门\t申请日期\t签约有效期\t标签\t\t\n\t\nHT2025999\n\t\n资助协议\n\t\n付款\n\t\n资助协议\n\t\n佛吉亚（中国）投资有限公司，无锡市德盛社会工作服务中心\n\t\n118,000.00\n\t\n草稿\n\t\n何松\n\t\n基金会\n\t\n--\n\t\n2021-11-30~2022-08-31\n\t\n--\n\t\n\t\nHT2025998\n\t\n资助协议\n\t\n付款", "url_path": "/contract", "analysis_timestamp": "2025-06-30T17:58:30.393182"}}, {"parent_menu": "捐方管理", "sub_menu": "开票管理", "url": "http://csboss.lianquan.org.cn/contract", "title": "基金会业务管理系统", "screenshot": "enhanced_sub_menu_捐方管理_开票管理.png", "timestamp": "2025-06-30T17:58:42.703556", "exploration_method": "enhanced", "content_analysis": {"elements": {"buttons_count": 82, "tables_count": 7, "forms_count": 1, "inputs_count": 24}, "buttons": [{"text": "提醒设置", "type": "button", "class": "el-button left el-button--small"}, {"text": "新增", "type": "button", "class": "el-button left middle-space add-button el-button--primary el-button--small"}, {"text": "导出n天内即将过期的合同", "type": "button", "class": "el-button el-button--default el-button--small"}, {"text": "查询", "type": "button", "class": "el-button middle-space el-button--primary el-button--small"}, {"text": "重置", "type": "button", "class": "el-button middle-space el-button--default el-button--small"}, {"text": "导出", "type": "button", "class": "el-button middle-space export-btn el-button--default el-button--small"}, {"text": "更多", "type": "button", "class": "el-button middle-space el-button--default el-button--small"}], "tables": [{"headers": ["合同编号", "合同名称", "合同类型", "合同类别", "签约单位", "合同金额", "合同状态【流程节点】审核人", "申请人", "申请部门"], "header_count": 9, "row_count": 63}, {"headers": ["合同编号", "合同名称", "合同类型", "合同类别", "签约单位", "合同金额", "合同状态【流程节点】审核人", "申请人", "申请部门"], "header_count": 9, "row_count": 1}, {"headers": [], "header_count": 0, "row_count": 20}, {"headers": ["序号"], "header_count": 1, "row_count": 1}, {"headers": [], "header_count": 0, "row_count": 20}], "features": {"has_search": false, "has_pagination": true, "has_add_button": true, "has_export_button": true, "has_edit_button": false, "has_delete_button": false, "has_filter": false, "has_tabs": true, "has_modal": true, "has_upload": false}, "page_title": "基金会业务管理系统", "content_preview": "合同管理作废审批记录\n合同编号：\n合同名称：\n合同类型：\n合同类别：\n合同状态：\n签约单位：\n提醒设置\n新增\n导出n天内即将过期的合同 查询 重置 导出 更多\n\t合同编号\t合同名称\t合同类型\t合同类别\t签约单位\t合同金额\n\t合同状态【流程节点】审核人\t申请人\t申请部门\t申请日期\t签约有效期\t标签\t\t\n\t\nHT2025999\n\t\n资助协议\n\t\n付款\n\t\n资助协议\n\t\n佛吉亚（中国）投资有限公司，无锡市德盛社会工作服务中心\n\t\n118,000.00\n\t\n草稿\n\t\n何松\n\t\n基金会\n\t\n--\n\t\n2021-11-30~2022-08-31\n\t\n--\n\t\n\t\nHT2025998\n\t\n资助协议\n\t\n付款", "url_path": "/contract", "analysis_timestamp": "2025-06-30T17:58:42.894964"}}, {"parent_menu": "捐方管理", "sub_menu": "捐方管理", "url": "http://csboss.lianquan.org.cn/donor/manage", "title": "基金会业务管理系统", "screenshot": "enhanced_sub_menu_捐方管理_捐方管理.png", "timestamp": "2025-06-30T17:58:55.235876", "exploration_method": "enhanced", "content_analysis": {"elements": {"buttons_count": 15, "tables_count": 7, "forms_count": 1, "inputs_count": 24}, "buttons": [{"text": "新增", "type": "button", "class": "el-button left middle-space add-button el-button--primary el-button--small"}, {"text": "查询", "type": "button", "class": "el-button middle-space el-button--primary el-button--small"}, {"text": "重置", "type": "button", "class": "el-button middle-space el-button--default el-button--small"}, {"text": "导出", "type": "button", "class": "el-button middle-space export-btn el-button--default el-button--small"}, {"text": "更多", "type": "button", "class": "el-button middle-space el-button--default el-button--small"}, {"text": "关闭", "type": "button", "class": "el-button el-button--default el-button--small"}, {"text": "确定", "type": "button", "class": "el-button el-button--primary el-button--small"}], "tables": [{"headers": ["捐方编号", "捐方名称", "捐方简称", "所属小组", "行业类别", "捐方规模", "年营业收入", "企业性质", "上市状态"], "header_count": 9, "row_count": 3}, {"headers": ["捐方编号", "捐方名称", "捐方简称", "所属小组", "行业类别", "捐方规模", "年营业收入", "企业性质", "上市状态"], "header_count": 9, "row_count": 1}, {"headers": [], "header_count": 0, "row_count": 0}, {"headers": ["序号"], "header_count": 1, "row_count": 1}, {"headers": [], "header_count": 0, "row_count": 0}], "features": {"has_search": false, "has_pagination": true, "has_add_button": true, "has_export_button": true, "has_edit_button": false, "has_delete_button": false, "has_filter": false, "has_tabs": true, "has_modal": true, "has_upload": false}, "page_title": "基金会业务管理系统", "content_preview": "企业类型社会组织其他组织个人\n捐方编号：\n捐方名称：\n捐方简称：\n所属小组：\n行业类别：\n捐方规模：\n年营业收入：\n新增\n查询 重置 导出 更多\n\t捐方编号\t捐方名称\t捐方简称\t所属小组\t行业类别\t捐方规模\t年营业收入\t企业性质\t上市状态\t企业成立日期\t业务覆盖范围\t联系地址\t标签\t联系人姓名\t累计管理费金额\t单笔捐赠最大金额\n\t累计捐赠金额\t累计管理费比例\t最新捐赠日期\t累计捐赠项目数量\n\t资助最多次项目名称\t\n\n暂无记录，请更换条件重新搜索\n\n序号\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t操作\n共 0 条\n1\n前往页", "url_path": "/donor/manage", "analysis_timestamp": "2025-06-30T17:58:55.379037"}}, {"parent_menu": "捐方管理", "sub_menu": "支出管理", "url": "http://csboss.lianquan.org.cn/donor/manage", "title": "基金会业务管理系统", "screenshot": "enhanced_sub_menu_捐方管理_支出管理.png", "timestamp": "2025-06-30T17:59:07.678476", "exploration_method": "enhanced", "content_analysis": {"elements": {"buttons_count": 15, "tables_count": 7, "forms_count": 1, "inputs_count": 24}, "buttons": [{"text": "新增", "type": "button", "class": "el-button left middle-space add-button el-button--primary el-button--small"}, {"text": "查询", "type": "button", "class": "el-button middle-space el-button--primary el-button--small"}, {"text": "重置", "type": "button", "class": "el-button middle-space el-button--default el-button--small"}, {"text": "导出", "type": "button", "class": "el-button middle-space export-btn el-button--default el-button--small"}, {"text": "更多", "type": "button", "class": "el-button middle-space el-button--default el-button--small"}, {"text": "关闭", "type": "button", "class": "el-button el-button--default el-button--small"}, {"text": "确定", "type": "button", "class": "el-button el-button--primary el-button--small"}], "tables": [{"headers": ["捐方编号", "捐方名称", "捐方简称", "所属小组", "行业类别", "捐方规模", "年营业收入", "企业性质", "上市状态"], "header_count": 9, "row_count": 3}, {"headers": ["捐方编号", "捐方名称", "捐方简称", "所属小组", "行业类别", "捐方规模", "年营业收入", "企业性质", "上市状态"], "header_count": 9, "row_count": 1}, {"headers": [], "header_count": 0, "row_count": 0}, {"headers": ["序号"], "header_count": 1, "row_count": 1}, {"headers": [], "header_count": 0, "row_count": 0}], "features": {"has_search": false, "has_pagination": true, "has_add_button": true, "has_export_button": true, "has_edit_button": false, "has_delete_button": false, "has_filter": false, "has_tabs": true, "has_modal": true, "has_upload": false}, "page_title": "基金会业务管理系统", "content_preview": "企业类型社会组织其他组织个人\n捐方编号：\n捐方名称：\n捐方简称：\n所属小组：\n行业类别：\n捐方规模：\n年营业收入：\n新增\n查询 重置 导出 更多\n\t捐方编号\t捐方名称\t捐方简称\t所属小组\t行业类别\t捐方规模\t年营业收入\t企业性质\t上市状态\t企业成立日期\t业务覆盖范围\t联系地址\t标签\t联系人姓名\t累计管理费金额\t单笔捐赠最大金额\n\t累计捐赠金额\t累计管理费比例\t最新捐赠日期\t累计捐赠项目数量\n\t资助最多次项目名称\t\n\n暂无记录，请更换条件重新搜索\n\n序号\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t操作\n共 0 条\n1\n前往页", "url_path": "/donor/manage", "analysis_timestamp": "2025-06-30T17:59:07.820539"}}, {"parent_menu": "财务统计", "sub_menu": "业务收支明细", "url": "http://csboss.lianquan.org.cn/bills/report/tagProjectCount", "title": "基金会业务管理系统", "screenshot": "enhanced_sub_menu_财务统计_业务收支明细.png", "timestamp": "2025-06-30T17:59:25.149262", "exploration_method": "enhanced", "content_analysis": {"elements": {"buttons_count": 5, "tables_count": 0, "forms_count": 1, "inputs_count": 7}, "buttons": [{"text": "生成报表", "type": "button", "class": "el-button el-button--primary el-button--small"}, {"text": "导出", "type": "button", "class": "el-button middle-space el-button--small"}], "tables": [], "features": {"has_search": false, "has_pagination": false, "has_add_button": false, "has_export_button": true, "has_edit_button": false, "has_delete_button": false, "has_filter": false, "has_tabs": false, "has_modal": true, "has_upload": false}, "page_title": "基金会业务管理系统", "content_preview": "统计年份：\n业务项目：\n生成报表 导出\n 当前报表的收入已经扣除退款金额\n\n未生成报表", "url_path": "/bills/report/tagProjectCount", "analysis_timestamp": "2025-06-30T17:59:25.194824"}}, {"parent_menu": "财务统计", "sub_menu": "业务收支汇总", "url": "http://csboss.lianquan.org.cn/bills/report/tagTypeCount", "title": "基金会业务管理系统", "screenshot": "enhanced_sub_menu_财务统计_业务收支汇总.png", "timestamp": "2025-06-30T17:59:30.869645", "exploration_method": "enhanced", "content_analysis": {"elements": {"buttons_count": 5, "tables_count": 0, "forms_count": 1, "inputs_count": 7}, "buttons": [{"text": "生成报表", "type": "button", "class": "el-button el-button--primary el-button--small"}, {"text": "导出", "type": "button", "class": "el-button middle-space el-button--small"}], "tables": [], "features": {"has_search": false, "has_pagination": false, "has_add_button": false, "has_export_button": true, "has_edit_button": false, "has_delete_button": false, "has_filter": false, "has_tabs": false, "has_modal": true, "has_upload": false}, "page_title": "基金会业务管理系统", "content_preview": "统计年份：\n业务类型：\n生成报表 导出\n 当前报表的收入已经扣除退款金额\n\n未生成报表", "url_path": "/bills/report/tagTypeCount", "analysis_timestamp": "2025-06-30T17:59:30.917004"}}, {"parent_menu": "财务统计", "sub_menu": "业务管理", "url": "http://csboss.lianquan.org.cn/bills/report/tagTypeCount", "title": "基金会业务管理系统", "screenshot": "enhanced_sub_menu_财务统计_业务管理.png", "timestamp": "2025-06-30T17:59:41.554752", "exploration_method": "enhanced", "content_analysis": {"elements": {"buttons_count": 5, "tables_count": 0, "forms_count": 1, "inputs_count": 7}, "buttons": [{"text": "生成报表", "type": "button", "class": "el-button el-button--primary el-button--small"}, {"text": "导出", "type": "button", "class": "el-button middle-space el-button--small"}], "tables": [], "features": {"has_search": false, "has_pagination": false, "has_add_button": false, "has_export_button": true, "has_edit_button": false, "has_delete_button": false, "has_filter": false, "has_tabs": false, "has_modal": true, "has_upload": false}, "page_title": "基金会业务管理系统", "content_preview": "统计年份：\n业务类型：\n生成报表 导出\n 当前报表的收入已经扣除退款金额\n\n未生成报表", "url_path": "/bills/report/tagTypeCount", "analysis_timestamp": "2025-06-30T17:59:41.588796"}}, {"parent_menu": "财务统计", "sub_menu": "可变配置", "url": "http://csboss.lianquan.org.cn/bills/report/tagTypeCount", "title": "基金会业务管理系统", "screenshot": "enhanced_sub_menu_财务统计_可变配置.png", "timestamp": "2025-06-30T17:59:53.877924", "exploration_method": "enhanced", "content_analysis": {"elements": {"buttons_count": 5, "tables_count": 0, "forms_count": 1, "inputs_count": 7}, "buttons": [{"text": "生成报表", "type": "button", "class": "el-button el-button--primary el-button--small"}, {"text": "导出", "type": "button", "class": "el-button middle-space el-button--small"}], "tables": [], "features": {"has_search": false, "has_pagination": false, "has_add_button": false, "has_export_button": true, "has_edit_button": false, "has_delete_button": false, "has_filter": false, "has_tabs": false, "has_modal": true, "has_upload": false}, "page_title": "基金会业务管理系统", "content_preview": "统计年份：\n业务类型：\n生成报表 导出\n 当前报表的收入已经扣除退款金额\n\n未生成报表", "url_path": "/bills/report/tagTypeCount", "analysis_timestamp": "2025-06-30T17:59:53.910339"}}, {"parent_menu": "财务统计", "sub_menu": "合作方管理", "url": "http://csboss.lianquan.org.cn/ins", "title": "基金会业务管理系统", "screenshot": "enhanced_sub_menu_财务统计_合作方管理.png", "timestamp": "2025-06-30T18:00:06.287776", "exploration_method": "enhanced", "content_analysis": {"elements": {"buttons_count": 85, "tables_count": 7, "forms_count": 1, "inputs_count": 23}, "buttons": [{"text": "相关说明", "type": "button", "class": "el-button middle-space el-button--primary el-button--small"}, {"text": "审核者设置", "type": "button", "class": "el-button el-button--default el-button--small"}, {"text": "新增", "type": "button", "class": "el-button middle-space add-button el-button--primary el-button--small"}, {"text": "查询", "type": "button", "class": "el-button middle-space el-button--primary el-button--small"}, {"text": "重置", "type": "button", "class": "el-button middle-space el-button--default el-button--small"}, {"text": "导出", "type": "button", "class": "el-button middle-space export-btn el-button--default el-button--small"}], "tables": [{"headers": ["合作方编号", "合作方类型", "机构名称", "机构对接人姓名", "合作期间", "合作状态", "累计合作项目数量", "进行中的合作项目", "标签"], "header_count": 9, "row_count": 63}, {"headers": ["合作方编号", "合作方类型", "机构名称", "机构对接人姓名", "合作期间", "合作状态", "累计合作项目数量", "进行中的合作项目", "标签"], "header_count": 9, "row_count": 1}, {"headers": [], "header_count": 0, "row_count": 20}, {"headers": ["序号"], "header_count": 1, "row_count": 1}, {"headers": [], "header_count": 0, "row_count": 20}], "features": {"has_search": true, "has_pagination": true, "has_add_button": true, "has_export_button": true, "has_edit_button": false, "has_delete_button": false, "has_filter": false, "has_tabs": true, "has_modal": true, "has_upload": false}, "page_title": "基金会业务管理系统", "content_preview": "社会组织个人类型企业类型其他组织\n合作方类型：\n机构名称：\n对接人姓名：\n合作状态：\n标签：\n联劝对接人：\n相关说明 审核者设置 新增 查询 重置 导出\n\t合作方编号\t合作方类型\t机构名称\t机构对接人姓名\t合作期间\t合作状态\t累计合作项目数量\t进行中的合作项目\t标签\t联劝对接人\t\t\n\t\nINSGG202500592\n\t\n--\n\t\n福建省担当者行动教育基金会2\n\t\n安庆旭\n\t\n~\n\t\n未合作\n\t\n--\n\t\n--\n\t\n--\n\t\n--\n\t\n\t\nINSGG202500910\n\t\n执行方\n\t\n佳县慈爱社会公益事业服务中心\n\t\n屈红艳\n\t\n~\n\t\n未合作\n\t\n--\n\t\n--\n\t\n--\n\t\n--\n\t\n\t\nI", "url_path": "/ins", "analysis_timestamp": "2025-06-30T18:00:06.472907"}}, {"parent_menu": "财务统计", "sub_menu": "合同管理", "url": "http://csboss.lianquan.org.cn/contract", "title": "基金会业务管理系统", "screenshot": "enhanced_sub_menu_财务统计_合同管理.png", "timestamp": "2025-06-30T18:00:18.836598", "exploration_method": "enhanced", "content_analysis": {"elements": {"buttons_count": 82, "tables_count": 7, "forms_count": 1, "inputs_count": 24}, "buttons": [{"text": "提醒设置", "type": "button", "class": "el-button left el-button--small"}, {"text": "新增", "type": "button", "class": "el-button left middle-space add-button el-button--primary el-button--small"}, {"text": "导出n天内即将过期的合同", "type": "button", "class": "el-button el-button--default el-button--small"}, {"text": "查询", "type": "button", "class": "el-button middle-space el-button--primary el-button--small"}, {"text": "重置", "type": "button", "class": "el-button middle-space el-button--default el-button--small"}, {"text": "导出", "type": "button", "class": "el-button middle-space export-btn el-button--default el-button--small"}, {"text": "更多", "type": "button", "class": "el-button middle-space el-button--default el-button--small"}], "tables": [{"headers": ["合同编号", "合同名称", "合同类型", "合同类别", "签约单位", "合同金额", "合同状态【流程节点】审核人", "申请人", "申请部门"], "header_count": 9, "row_count": 63}, {"headers": ["合同编号", "合同名称", "合同类型", "合同类别", "签约单位", "合同金额", "合同状态【流程节点】审核人", "申请人", "申请部门"], "header_count": 9, "row_count": 1}, {"headers": [], "header_count": 0, "row_count": 20}, {"headers": ["序号"], "header_count": 1, "row_count": 1}, {"headers": [], "header_count": 0, "row_count": 20}], "features": {"has_search": false, "has_pagination": true, "has_add_button": true, "has_export_button": true, "has_edit_button": false, "has_delete_button": false, "has_filter": false, "has_tabs": true, "has_modal": true, "has_upload": false}, "page_title": "基金会业务管理系统", "content_preview": "合同管理作废审批记录\n合同编号：\n合同名称：\n合同类型：\n合同类别：\n合同状态：\n签约单位：\n提醒设置\n新增\n导出n天内即将过期的合同 查询 重置 导出 更多\n\t合同编号\t合同名称\t合同类型\t合同类别\t签约单位\t合同金额\n\t合同状态【流程节点】审核人\t申请人\t申请部门\t申请日期\t签约有效期\t标签\t\t\n\t\nHT2025999\n\t\n资助协议\n\t\n付款\n\t\n资助协议\n\t\n佛吉亚（中国）投资有限公司，无锡市德盛社会工作服务中心\n\t\n118,000.00\n\t\n草稿\n\t\n何松\n\t\n基金会\n\t\n--\n\t\n2021-11-30~2022-08-31\n\t\n--\n\t\n\t\nHT2025998\n\t\n资助协议\n\t\n付款", "url_path": "/contract", "analysis_timestamp": "2025-06-30T18:00:19.020535"}}, {"parent_menu": "财务统计", "sub_menu": "安全审计", "url": "http://csboss.lianquan.org.cn/contract", "title": "基金会业务管理系统", "screenshot": "enhanced_sub_menu_财务统计_安全审计.png", "timestamp": "2025-06-30T18:00:31.339864", "exploration_method": "enhanced", "content_analysis": {"elements": {"buttons_count": 82, "tables_count": 7, "forms_count": 1, "inputs_count": 24}, "buttons": [{"text": "提醒设置", "type": "button", "class": "el-button left el-button--small"}, {"text": "新增", "type": "button", "class": "el-button left middle-space add-button el-button--primary el-button--small"}, {"text": "导出n天内即将过期的合同", "type": "button", "class": "el-button el-button--default el-button--small"}, {"text": "查询", "type": "button", "class": "el-button middle-space el-button--primary el-button--small"}, {"text": "重置", "type": "button", "class": "el-button middle-space el-button--default el-button--small"}, {"text": "导出", "type": "button", "class": "el-button middle-space export-btn el-button--default el-button--small"}, {"text": "更多", "type": "button", "class": "el-button middle-space el-button--default el-button--small"}], "tables": [{"headers": ["合同编号", "合同名称", "合同类型", "合同类别", "签约单位", "合同金额", "合同状态【流程节点】审核人", "申请人", "申请部门"], "header_count": 9, "row_count": 63}, {"headers": ["合同编号", "合同名称", "合同类型", "合同类别", "签约单位", "合同金额", "合同状态【流程节点】审核人", "申请人", "申请部门"], "header_count": 9, "row_count": 1}, {"headers": [], "header_count": 0, "row_count": 20}, {"headers": ["序号"], "header_count": 1, "row_count": 1}, {"headers": [], "header_count": 0, "row_count": 20}], "features": {"has_search": false, "has_pagination": true, "has_add_button": true, "has_export_button": true, "has_edit_button": false, "has_delete_button": false, "has_filter": false, "has_tabs": true, "has_modal": true, "has_upload": false}, "page_title": "基金会业务管理系统", "content_preview": "合同管理作废审批记录\n合同编号：\n合同名称：\n合同类型：\n合同类别：\n合同状态：\n签约单位：\n提醒设置\n新增\n导出n天内即将过期的合同 查询 重置 导出 更多\n\t合同编号\t合同名称\t合同类型\t合同类别\t签约单位\t合同金额\n\t合同状态【流程节点】审核人\t申请人\t申请部门\t申请日期\t签约有效期\t标签\t\t\n\t\nHT2025999\n\t\n资助协议\n\t\n付款\n\t\n资助协议\n\t\n佛吉亚（中国）投资有限公司，无锡市德盛社会工作服务中心\n\t\n118,000.00\n\t\n草稿\n\t\n何松\n\t\n基金会\n\t\n--\n\t\n2021-11-30~2022-08-31\n\t\n--\n\t\n\t\nHT2025998\n\t\n资助协议\n\t\n付款", "url_path": "/contract", "analysis_timestamp": "2025-06-30T18:00:31.516959"}}, {"parent_menu": "财务统计", "sub_menu": "开票管理", "url": "http://csboss.lianquan.org.cn/contract", "title": "基金会业务管理系统", "screenshot": "enhanced_sub_menu_财务统计_开票管理.png", "timestamp": "2025-06-30T18:00:43.831391", "exploration_method": "enhanced", "content_analysis": {"elements": {"buttons_count": 82, "tables_count": 7, "forms_count": 1, "inputs_count": 24}, "buttons": [{"text": "提醒设置", "type": "button", "class": "el-button left el-button--small"}, {"text": "新增", "type": "button", "class": "el-button left middle-space add-button el-button--primary el-button--small"}, {"text": "导出n天内即将过期的合同", "type": "button", "class": "el-button el-button--default el-button--small"}, {"text": "查询", "type": "button", "class": "el-button middle-space el-button--primary el-button--small"}, {"text": "重置", "type": "button", "class": "el-button middle-space el-button--default el-button--small"}, {"text": "导出", "type": "button", "class": "el-button middle-space export-btn el-button--default el-button--small"}, {"text": "更多", "type": "button", "class": "el-button middle-space el-button--default el-button--small"}], "tables": [{"headers": ["合同编号", "合同名称", "合同类型", "合同类别", "签约单位", "合同金额", "合同状态【流程节点】审核人", "申请人", "申请部门"], "header_count": 9, "row_count": 63}, {"headers": ["合同编号", "合同名称", "合同类型", "合同类别", "签约单位", "合同金额", "合同状态【流程节点】审核人", "申请人", "申请部门"], "header_count": 9, "row_count": 1}, {"headers": [], "header_count": 0, "row_count": 20}, {"headers": ["序号"], "header_count": 1, "row_count": 1}, {"headers": [], "header_count": 0, "row_count": 20}], "features": {"has_search": false, "has_pagination": true, "has_add_button": true, "has_export_button": true, "has_edit_button": false, "has_delete_button": false, "has_filter": false, "has_tabs": true, "has_modal": true, "has_upload": false}, "page_title": "基金会业务管理系统", "content_preview": "合同管理作废审批记录\n合同编号：\n合同名称：\n合同类型：\n合同类别：\n合同状态：\n签约单位：\n提醒设置\n新增\n导出n天内即将过期的合同 查询 重置 导出 更多\n\t合同编号\t合同名称\t合同类型\t合同类别\t签约单位\t合同金额\n\t合同状态【流程节点】审核人\t申请人\t申请部门\t申请日期\t签约有效期\t标签\t\t\n\t\nHT2025999\n\t\n资助协议\n\t\n付款\n\t\n资助协议\n\t\n佛吉亚（中国）投资有限公司，无锡市德盛社会工作服务中心\n\t\n118,000.00\n\t\n草稿\n\t\n何松\n\t\n基金会\n\t\n--\n\t\n2021-11-30~2022-08-31\n\t\n--\n\t\n\t\nHT2025998\n\t\n资助协议\n\t\n付款", "url_path": "/contract", "analysis_timestamp": "2025-06-30T18:00:44.182123"}}, {"parent_menu": "用友管理", "sub_menu": "业务管理", "url": "http://csboss.lianquan.org.cn/statistic/bill/board", "title": "基金会业务管理系统", "screenshot": "enhanced_sub_menu_用友管理_业务管理.png", "timestamp": "2025-06-30T18:01:06.450706", "exploration_method": "enhanced", "content_analysis": {"elements": {"buttons_count": 2, "tables_count": 0, "forms_count": 0, "inputs_count": 5}, "buttons": [], "tables": [], "features": {"has_search": false, "has_pagination": false, "has_add_button": false, "has_export_button": false, "has_edit_button": false, "has_delete_button": false, "has_filter": false, "has_tabs": false, "has_modal": true, "has_upload": false}, "page_title": "基金会业务管理系统", "content_preview": "", "url_path": "/statistic/bill/board", "analysis_timestamp": "2025-06-30T18:01:06.476859"}}, {"parent_menu": "用友管理", "sub_menu": "会计科目", "url": "http://csboss.lianquan.org.cn/yonYou/accountingSubject", "title": "基金会业务管理系统", "screenshot": "enhanced_sub_menu_用友管理_会计科目.png", "timestamp": "2025-06-30T18:01:12.161519", "exploration_method": "enhanced", "content_analysis": {"elements": {"buttons_count": 72, "tables_count": 7, "forms_count": 1, "inputs_count": 23}, "buttons": [{"text": "查询", "type": "button", "class": "el-button middle-space el-button--primary el-button--small"}, {"text": "重置", "type": "button", "class": "el-button middle-space el-button--default el-button--small"}, {"text": "导出", "type": "button", "class": "el-button middle-space export-btn el-button--default el-button--small"}, {"text": "更多", "type": "button", "class": "el-button middle-space el-button--default el-button--small"}], "tables": [{"headers": ["科目层级", "编码", "名称", "会计要素", "余额方向", "余额赤字检查", "发生额方向控制", "末级科目", "辅助核算"], "header_count": 9, "row_count": 63}, {"headers": ["科目层级", "编码", "名称", "会计要素", "余额方向", "余额赤字检查", "发生额方向控制", "末级科目", "辅助核算"], "header_count": 9, "row_count": 1}, {"headers": [], "header_count": 0, "row_count": 20}, {"headers": ["序号"], "header_count": 1, "row_count": 1}, {"headers": [], "header_count": 0, "row_count": 20}], "features": {"has_search": true, "has_pagination": true, "has_add_button": false, "has_export_button": true, "has_edit_button": false, "has_delete_button": false, "has_filter": false, "has_tabs": false, "has_modal": true, "has_upload": false}, "page_title": "基金会业务管理系统", "content_preview": "科目编码：\n科目名称：\n会计要素：\n科目状态：\n仅查询末级科目：\n创建日期：\n~\n查询 重置 导出 更多\n\t科目层级\t编码\t名称\t会计要素\t余额方向\t余额赤字检查\t发生额方向控制\t末级科目\t辅助核算\t受控应用\t币种\t现金分类\t凭证必填设置\t默认计量单位\t是否启用\t成本要素\t要素性质\t要素类型\t要素类别\t国家/地区\t所属科目表\t科目说明\t创建人\t创建时间\t修改人\t修改时间\t\t\n\t\n1\n\t\n1001\n\t\n现金\n\t\n资产\n\t\n借\n\t\n错误\n\t\n否\n\t\n是\n\t\n--\n\t\n--\n\t\n--\n\t\n--\n\t\n--\n\t\n--\n\t\n启用\n\t\n--\n\t\n--\n\t\n--\n\t\n--\n\t\n--\n\t\n上海联劝科目表\n\t\n-", "url_path": "/yonYou/accountingSubject", "analysis_timestamp": "2025-06-30T18:01:12.378535"}}, {"parent_menu": "用友管理", "sub_menu": "凭证确认", "url": "http://csboss.lianquan.org.cn/yonYou/bankVoucher", "title": "基金会业务管理系统", "screenshot": "enhanced_sub_menu_用友管理_凭证确认.png", "timestamp": "2025-06-30T18:01:18.060072", "exploration_method": "enhanced", "content_analysis": {"elements": {"buttons_count": 17, "tables_count": 7, "forms_count": 1, "inputs_count": 23}, "buttons": [{"text": "查询", "type": "button", "class": "el-button middle-space el-button--primary el-button--small"}, {"text": "重置", "type": "button", "class": "el-button middle-space el-button--default el-button--small"}, {"text": "导出", "type": "button", "class": "el-button middle-space export-btn el-button--default el-button--small"}, {"text": "取消", "type": "button", "class": "el-button middle-space left el-button--default el-button--small"}], "tables": [{"headers": ["银行账号", "项目名称", "交易流水号", "商户订单号", "捐赠金额", "账户余额", "捐赠时间", "姓名", "捐赠人账号"], "header_count": 9, "row_count": 3}, {"headers": ["银行账号", "项目名称", "交易流水号", "商户订单号", "捐赠金额", "账户余额", "捐赠时间", "姓名", "捐赠人账号"], "header_count": 9, "row_count": 1}, {"headers": [], "header_count": 0, "row_count": 0}, {"headers": ["序号"], "header_count": 1, "row_count": 1}, {"headers": [], "header_count": 0, "row_count": 0}], "features": {"has_search": false, "has_pagination": true, "has_add_button": false, "has_export_button": true, "has_edit_button": false, "has_delete_button": false, "has_filter": false, "has_tabs": false, "has_modal": true, "has_upload": false}, "page_title": "基金会业务管理系统", "content_preview": "银行账号：\n姓名：\n捐赠者账号：\n账单状态：\n凭证状态：\n账单日期：\n~\n查询 重置 导出\n\t银行账号\t项目名称\t交易流水号\t商户订单号\t捐赠金额\t账户余额\t捐赠时间\t姓名\t捐赠人账号\t捐赠人户名\t捐赠人行名\t备注\t商户数据包\t支付渠道\t手机号\t邮箱\t账单状态\t凭证状态\t\n\n暂无记录，请更换条件重新搜索\n\n序号\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t操作\n共 0 条\n1\n前往页", "url_path": "/yonYou/bankVoucher", "analysis_timestamp": "2025-06-30T18:01:18.194159"}}, {"parent_menu": "用友管理", "sub_menu": "凭证管理", "url": "http://csboss.lianquan.org.cn/yonYou/voucher/manage", "title": "基金会业务管理系统", "screenshot": "enhanced_sub_menu_用友管理_凭证管理.png", "timestamp": "2025-06-30T18:01:23.862945", "exploration_method": "enhanced", "content_analysis": {"elements": {"buttons_count": 17, "tables_count": 7, "forms_count": 1, "inputs_count": 23}, "buttons": [{"text": "查询", "type": "button", "class": "el-button middle-space el-button--primary el-button--small"}, {"text": "重置", "type": "button", "class": "el-button middle-space el-button--default el-button--small"}, {"text": "导出", "type": "button", "class": "el-button middle-space export-btn el-button--default el-button--small"}, {"text": "更多", "type": "button", "class": "el-button middle-space el-button--default el-button--small"}], "tables": [{"headers": ["凭证编号", "类型", "账簿编码", "账簿名称", "借贷类型", "会计期间", "凭证号", "凭证时间", "摘要"], "header_count": 9, "row_count": 3}, {"headers": ["凭证编号", "类型", "账簿编码", "账簿名称", "借贷类型", "会计期间", "凭证号", "凭证时间", "摘要"], "header_count": 9, "row_count": 1}, {"headers": [], "header_count": 0, "row_count": 0}, {"headers": ["序号"], "header_count": 1, "row_count": 1}, {"headers": [], "header_count": 0, "row_count": 0}], "features": {"has_search": false, "has_pagination": true, "has_add_button": false, "has_export_button": true, "has_edit_button": false, "has_delete_button": false, "has_filter": false, "has_tabs": false, "has_modal": true, "has_upload": false}, "page_title": "基金会业务管理系统", "content_preview": "凭证编号：\n类型：\n借贷类型：\n凭证号：\n状态：\n凭证日期：\n~\n查询 重置 导出 更多\n\t凭证编号\t类型\t账簿编码\t账簿名称\t借贷类型\t会计期间\t凭证号\t凭证时间\t摘要\t科目编码\t科目名称\t辅助核算\t币种\t本币汇率\t数量\t原币金额\t本币金额\t创建时间\t同步时间\t制单人\t状态\t\n\n暂无记录，请更换条件重新搜索\n\n序号\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t操作\n共 0 条\n1\n前往页", "url_path": "/yonYou/voucher/manage", "analysis_timestamp": "2025-06-30T18:01:24.009526"}}, {"parent_menu": "用友管理", "sub_menu": "可变配置", "url": "http://csboss.lianquan.org.cn/yonYou/voucher/manage", "title": "基金会业务管理系统", "screenshot": "enhanced_sub_menu_用友管理_可变配置.png", "timestamp": "2025-06-30T18:01:36.316888", "exploration_method": "enhanced", "content_analysis": {"elements": {"buttons_count": 17, "tables_count": 7, "forms_count": 1, "inputs_count": 23}, "buttons": [{"text": "查询", "type": "button", "class": "el-button middle-space el-button--primary el-button--small"}, {"text": "重置", "type": "button", "class": "el-button middle-space el-button--default el-button--small"}, {"text": "导出", "type": "button", "class": "el-button middle-space export-btn el-button--default el-button--small"}, {"text": "更多", "type": "button", "class": "el-button middle-space el-button--default el-button--small"}], "tables": [{"headers": ["凭证编号", "类型", "账簿编码", "账簿名称", "借贷类型", "会计期间", "凭证号", "凭证时间", "摘要"], "header_count": 9, "row_count": 3}, {"headers": ["凭证编号", "类型", "账簿编码", "账簿名称", "借贷类型", "会计期间", "凭证号", "凭证时间", "摘要"], "header_count": 9, "row_count": 1}, {"headers": [], "header_count": 0, "row_count": 0}, {"headers": ["序号"], "header_count": 1, "row_count": 1}, {"headers": [], "header_count": 0, "row_count": 0}], "features": {"has_search": false, "has_pagination": true, "has_add_button": false, "has_export_button": true, "has_edit_button": false, "has_delete_button": false, "has_filter": false, "has_tabs": false, "has_modal": true, "has_upload": false}, "page_title": "基金会业务管理系统", "content_preview": "凭证编号：\n类型：\n借贷类型：\n凭证号：\n状态：\n凭证日期：\n~\n查询 重置 导出 更多\n\t凭证编号\t类型\t账簿编码\t账簿名称\t借贷类型\t会计期间\t凭证号\t凭证时间\t摘要\t科目编码\t科目名称\t辅助核算\t币种\t本币汇率\t数量\t原币金额\t本币金额\t创建时间\t同步时间\t制单人\t状态\t\n\n暂无记录，请更换条件重新搜索\n\n序号\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t操作\n共 0 条\n1\n前往页", "url_path": "/yonYou/voucher/manage", "analysis_timestamp": "2025-06-30T18:01:36.469831"}}, {"parent_menu": "用友管理", "sub_menu": "合作方管理", "url": "http://csboss.lianquan.org.cn/ins", "title": "基金会业务管理系统", "screenshot": "enhanced_sub_menu_用友管理_合作方管理.png", "timestamp": "2025-06-30T18:01:48.850259", "exploration_method": "enhanced", "content_analysis": {"elements": {"buttons_count": 85, "tables_count": 7, "forms_count": 1, "inputs_count": 23}, "buttons": [{"text": "相关说明", "type": "button", "class": "el-button middle-space el-button--primary el-button--small"}, {"text": "审核者设置", "type": "button", "class": "el-button el-button--default el-button--small"}, {"text": "新增", "type": "button", "class": "el-button middle-space add-button el-button--primary el-button--small"}, {"text": "查询", "type": "button", "class": "el-button middle-space el-button--primary el-button--small"}, {"text": "重置", "type": "button", "class": "el-button middle-space el-button--default el-button--small"}, {"text": "导出", "type": "button", "class": "el-button middle-space export-btn el-button--default el-button--small"}], "tables": [{"headers": ["合作方编号", "合作方类型", "机构名称", "机构对接人姓名", "合作期间", "合作状态", "累计合作项目数量", "进行中的合作项目", "标签"], "header_count": 9, "row_count": 63}, {"headers": ["合作方编号", "合作方类型", "机构名称", "机构对接人姓名", "合作期间", "合作状态", "累计合作项目数量", "进行中的合作项目", "标签"], "header_count": 9, "row_count": 1}, {"headers": [], "header_count": 0, "row_count": 20}, {"headers": ["序号"], "header_count": 1, "row_count": 1}, {"headers": [], "header_count": 0, "row_count": 20}], "features": {"has_search": true, "has_pagination": true, "has_add_button": true, "has_export_button": true, "has_edit_button": false, "has_delete_button": false, "has_filter": false, "has_tabs": true, "has_modal": true, "has_upload": false}, "page_title": "基金会业务管理系统", "content_preview": "社会组织个人类型企业类型其他组织\n合作方类型：\n机构名称：\n对接人姓名：\n合作状态：\n标签：\n联劝对接人：\n相关说明 审核者设置 新增 查询 重置 导出\n\t合作方编号\t合作方类型\t机构名称\t机构对接人姓名\t合作期间\t合作状态\t累计合作项目数量\t进行中的合作项目\t标签\t联劝对接人\t\t\n\t\nINSGG202500592\n\t\n--\n\t\n福建省担当者行动教育基金会2\n\t\n安庆旭\n\t\n~\n\t\n未合作\n\t\n--\n\t\n--\n\t\n--\n\t\n--\n\t\n\t\nINSGG202500910\n\t\n执行方\n\t\n佳县慈爱社会公益事业服务中心\n\t\n屈红艳\n\t\n~\n\t\n未合作\n\t\n--\n\t\n--\n\t\n--\n\t\n--\n\t\n\t\nI", "url_path": "/ins", "analysis_timestamp": "2025-06-30T18:01:49.023686"}}, {"parent_menu": "用友管理", "sub_menu": "合同管理", "url": "http://csboss.lianquan.org.cn/contract", "title": "基金会业务管理系统", "screenshot": "enhanced_sub_menu_用友管理_合同管理.png", "timestamp": "2025-06-30T18:02:01.375959", "exploration_method": "enhanced", "content_analysis": {"elements": {"buttons_count": 82, "tables_count": 7, "forms_count": 1, "inputs_count": 24}, "buttons": [{"text": "提醒设置", "type": "button", "class": "el-button left el-button--small"}, {"text": "新增", "type": "button", "class": "el-button left middle-space add-button el-button--primary el-button--small"}, {"text": "导出n天内即将过期的合同", "type": "button", "class": "el-button el-button--default el-button--small"}, {"text": "查询", "type": "button", "class": "el-button middle-space el-button--primary el-button--small"}, {"text": "重置", "type": "button", "class": "el-button middle-space el-button--default el-button--small"}, {"text": "导出", "type": "button", "class": "el-button middle-space export-btn el-button--default el-button--small"}, {"text": "更多", "type": "button", "class": "el-button middle-space el-button--default el-button--small"}], "tables": [{"headers": ["合同编号", "合同名称", "合同类型", "合同类别", "签约单位", "合同金额", "合同状态【流程节点】审核人", "申请人", "申请部门"], "header_count": 9, "row_count": 63}, {"headers": ["合同编号", "合同名称", "合同类型", "合同类别", "签约单位", "合同金额", "合同状态【流程节点】审核人", "申请人", "申请部门"], "header_count": 9, "row_count": 1}, {"headers": [], "header_count": 0, "row_count": 20}, {"headers": ["序号"], "header_count": 1, "row_count": 1}, {"headers": [], "header_count": 0, "row_count": 20}], "features": {"has_search": false, "has_pagination": true, "has_add_button": true, "has_export_button": true, "has_edit_button": false, "has_delete_button": false, "has_filter": false, "has_tabs": true, "has_modal": true, "has_upload": false}, "page_title": "基金会业务管理系统", "content_preview": "合同管理作废审批记录\n合同编号：\n合同名称：\n合同类型：\n合同类别：\n合同状态：\n签约单位：\n提醒设置\n新增\n导出n天内即将过期的合同 查询 重置 导出 更多\n\t合同编号\t合同名称\t合同类型\t合同类别\t签约单位\t合同金额\n\t合同状态【流程节点】审核人\t申请人\t申请部门\t申请日期\t签约有效期\t标签\t\t\n\t\nHT2025999\n\t\n资助协议\n\t\n付款\n\t\n资助协议\n\t\n佛吉亚（中国）投资有限公司，无锡市德盛社会工作服务中心\n\t\n118,000.00\n\t\n草稿\n\t\n何松\n\t\n基金会\n\t\n--\n\t\n2021-11-30~2022-08-31\n\t\n--\n\t\n\t\nHT2025998\n\t\n资助协议\n\t\n付款", "url_path": "/contract", "analysis_timestamp": "2025-06-30T18:02:01.561525"}}, {"parent_menu": "用友管理", "sub_menu": "安全审计", "url": "http://csboss.lianquan.org.cn/contract", "title": "基金会业务管理系统", "screenshot": "enhanced_sub_menu_用友管理_安全审计.png", "timestamp": "2025-06-30T18:02:13.871705", "exploration_method": "enhanced", "content_analysis": {"elements": {"buttons_count": 82, "tables_count": 7, "forms_count": 1, "inputs_count": 24}, "buttons": [{"text": "提醒设置", "type": "button", "class": "el-button left el-button--small"}, {"text": "新增", "type": "button", "class": "el-button left middle-space add-button el-button--primary el-button--small"}, {"text": "导出n天内即将过期的合同", "type": "button", "class": "el-button el-button--default el-button--small"}, {"text": "查询", "type": "button", "class": "el-button middle-space el-button--primary el-button--small"}, {"text": "重置", "type": "button", "class": "el-button middle-space el-button--default el-button--small"}, {"text": "导出", "type": "button", "class": "el-button middle-space export-btn el-button--default el-button--small"}, {"text": "更多", "type": "button", "class": "el-button middle-space el-button--default el-button--small"}], "tables": [{"headers": ["合同编号", "合同名称", "合同类型", "合同类别", "签约单位", "合同金额", "合同状态【流程节点】审核人", "申请人", "申请部门"], "header_count": 9, "row_count": 63}, {"headers": ["合同编号", "合同名称", "合同类型", "合同类别", "签约单位", "合同金额", "合同状态【流程节点】审核人", "申请人", "申请部门"], "header_count": 9, "row_count": 1}, {"headers": [], "header_count": 0, "row_count": 20}, {"headers": ["序号"], "header_count": 1, "row_count": 1}, {"headers": [], "header_count": 0, "row_count": 20}], "features": {"has_search": false, "has_pagination": true, "has_add_button": true, "has_export_button": true, "has_edit_button": false, "has_delete_button": false, "has_filter": false, "has_tabs": true, "has_modal": true, "has_upload": false}, "page_title": "基金会业务管理系统", "content_preview": "合同管理作废审批记录\n合同编号：\n合同名称：\n合同类型：\n合同类别：\n合同状态：\n签约单位：\n提醒设置\n新增\n导出n天内即将过期的合同 查询 重置 导出 更多\n\t合同编号\t合同名称\t合同类型\t合同类别\t签约单位\t合同金额\n\t合同状态【流程节点】审核人\t申请人\t申请部门\t申请日期\t签约有效期\t标签\t\t\n\t\nHT2025999\n\t\n资助协议\n\t\n付款\n\t\n资助协议\n\t\n佛吉亚（中国）投资有限公司，无锡市德盛社会工作服务中心\n\t\n118,000.00\n\t\n草稿\n\t\n何松\n\t\n基金会\n\t\n--\n\t\n2021-11-30~2022-08-31\n\t\n--\n\t\n\t\nHT2025998\n\t\n资助协议\n\t\n付款", "url_path": "/contract", "analysis_timestamp": "2025-06-30T18:02:14.068268"}}, {"parent_menu": "可变配置", "sub_menu": "业务管理", "url": "http://csboss.lianquan.org.cn/statistic/bill/board", "title": "基金会业务管理系统", "screenshot": "enhanced_sub_menu_可变配置_业务管理.png", "timestamp": "2025-06-30T18:02:36.346182", "exploration_method": "enhanced", "content_analysis": {"elements": {"buttons_count": 2, "tables_count": 0, "forms_count": 0, "inputs_count": 5}, "buttons": [], "tables": [], "features": {"has_search": false, "has_pagination": false, "has_add_button": false, "has_export_button": false, "has_edit_button": false, "has_delete_button": false, "has_filter": false, "has_tabs": false, "has_modal": true, "has_upload": false}, "page_title": "基金会业务管理系统", "content_preview": "", "url_path": "/statistic/bill/board", "analysis_timestamp": "2025-06-30T18:02:36.368518"}}, {"parent_menu": "可变配置", "sub_menu": "动态标签", "url": "http://csboss.lianquan.org.cn/scrm/label/jzf/manage", "title": "基金会业务管理系统", "screenshot": "enhanced_sub_menu_可变配置_动态标签.png", "timestamp": "2025-06-30T18:02:42.055705", "exploration_method": "enhanced", "content_analysis": {"elements": {"buttons_count": 9, "tables_count": 7, "forms_count": 1, "inputs_count": 14}, "buttons": [{"text": "查询", "type": "button", "class": "el-button middle-space el-button--primary el-button--small"}, {"text": "重置", "type": "button", "class": "el-button middle-space el-button--default el-button--small"}, {"text": "取 消", "type": "button", "class": "el-button el-button--default el-button--small"}, {"text": "确 定", "type": "button", "class": "el-button el-button--primary el-button--small"}], "tables": [{"headers": ["标签名称", "标签ID", "备注", "使用情况", "创建人", "创建时间", "序号"], "header_count": 7, "row_count": 3}, {"headers": ["标签名称", "标签ID", "备注", "使用情况", "创建人", "创建时间"], "header_count": 6, "row_count": 1}, {"headers": [], "header_count": 0, "row_count": 0}, {"headers": ["序号"], "header_count": 1, "row_count": 1}, {"headers": [], "header_count": 0, "row_count": 0}], "features": {"has_search": false, "has_pagination": true, "has_add_button": false, "has_export_button": false, "has_edit_button": false, "has_delete_button": false, "has_filter": false, "has_tabs": true, "has_modal": true, "has_upload": false}, "page_title": "基金会业务管理系统", "content_preview": "捐赠方执行项目收入预算支出预算业务项目资助计划筹款产品合作方合同\n 新增标签类别\n捐赠方标签\n 捐赠方\n标签ID：\n标签名称：\n查询 重置\n\t标签名称\t标签ID\t备注\t使用情况\t创建人\t创建时间\t\n\n暂无记录，请更换条件重新搜索\n\n序号\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t操作\n共 0 条\n1\n前往页", "url_path": "/scrm/label/jzf/manage", "analysis_timestamp": "2025-06-30T18:02:42.178130"}}, {"parent_menu": "可变配置", "sub_menu": "动态表单", "url": "http://csboss.lianquan.org.cn/variable/form/manage?useCase=form", "title": "基金会业务管理系统", "screenshot": "enhanced_sub_menu_可变配置_动态表单.png", "timestamp": "2025-06-30T18:02:47.877966", "exploration_method": "enhanced", "content_analysis": {"elements": {"buttons_count": 23, "tables_count": 7, "forms_count": 0, "inputs_count": 9}, "buttons": [{"text": "添加子类型", "type": "button", "class": "el-button add-button el-button--primary el-button--small"}, {"text": "编辑", "type": "button", "class": "el-button el-button--text el-button--small"}, {"text": "编辑", "type": "button", "class": "el-button el-button--text el-button--small"}, {"text": "编辑", "type": "button", "class": "el-button el-button--text el-button--small"}, {"text": "编辑", "type": "button", "class": "el-button el-button--text el-button--small"}], "tables": [{"headers": ["子类型名称", "子类型表单数量", "序号"], "header_count": 3, "row_count": 15}, {"headers": ["子类型名称", "子类型表单数量"], "header_count": 2, "row_count": 1}, {"headers": [], "header_count": 0, "row_count": 4}, {"headers": ["序号"], "header_count": 1, "row_count": 1}, {"headers": [], "header_count": 0, "row_count": 4}], "features": {"has_search": false, "has_pagination": true, "has_add_button": true, "has_export_button": false, "has_edit_button": true, "has_delete_button": false, "has_filter": false, "has_tabs": true, "has_modal": true, "has_upload": false}, "page_title": "基金会业务管理系统", "content_preview": "添加子类型\n类型管理\n\t子类型名称\t子类型表单数量\t\n\t\n执行项目立项信息\n\t\n1\n\t\n\t\n执行项目执行反馈\n\t\n2\n\t\n\t\n执行项目结项反馈\n\t\n0\n\t\n\t\n投资理财表单\n\t\n0\n\t\n序号\t\t\t\n1\n\t\t\t\n\n2\n\t\t\t\n\n3\n\t\t\t\n\n4\n\t\t\t\n\t\t\t操作\n\t\t\t\n编辑\n\n\t\t\t\n编辑\n\n\t\t\t\n编辑\n\n\t\t\t\n编辑\n共 4 条\n1\n前往页", "url_path": "/variable/form/manage?useCase=form", "analysis_timestamp": "2025-06-30T18:02:47.997543"}}, {"parent_menu": "可变配置", "sub_menu": "合作方管理", "url": "http://csboss.lianquan.org.cn/ins", "title": "基金会业务管理系统", "screenshot": "enhanced_sub_menu_可变配置_合作方管理.png", "timestamp": "2025-06-30T18:03:08.031029", "exploration_method": "enhanced", "content_analysis": {"elements": {"buttons_count": 85, "tables_count": 7, "forms_count": 1, "inputs_count": 23}, "buttons": [{"text": "相关说明", "type": "button", "class": "el-button middle-space el-button--primary el-button--small"}, {"text": "审核者设置", "type": "button", "class": "el-button el-button--default el-button--small"}, {"text": "新增", "type": "button", "class": "el-button middle-space add-button el-button--primary el-button--small"}, {"text": "查询", "type": "button", "class": "el-button middle-space el-button--primary el-button--small"}, {"text": "重置", "type": "button", "class": "el-button middle-space el-button--default el-button--small"}, {"text": "导出", "type": "button", "class": "el-button middle-space export-btn el-button--default el-button--small"}], "tables": [{"headers": ["合作方编号", "合作方类型", "机构名称", "机构对接人姓名", "合作期间", "合作状态", "累计合作项目数量", "进行中的合作项目", "标签"], "header_count": 9, "row_count": 63}, {"headers": ["合作方编号", "合作方类型", "机构名称", "机构对接人姓名", "合作期间", "合作状态", "累计合作项目数量", "进行中的合作项目", "标签"], "header_count": 9, "row_count": 1}, {"headers": [], "header_count": 0, "row_count": 20}, {"headers": ["序号"], "header_count": 1, "row_count": 1}, {"headers": [], "header_count": 0, "row_count": 20}], "features": {"has_search": true, "has_pagination": true, "has_add_button": true, "has_export_button": true, "has_edit_button": false, "has_delete_button": false, "has_filter": false, "has_tabs": true, "has_modal": true, "has_upload": false}, "page_title": "基金会业务管理系统", "content_preview": "社会组织个人类型企业类型其他组织\n合作方类型：\n机构名称：\n对接人姓名：\n合作状态：\n标签：\n联劝对接人：\n相关说明 审核者设置 新增 查询 重置 导出\n\t合作方编号\t合作方类型\t机构名称\t机构对接人姓名\t合作期间\t合作状态\t累计合作项目数量\t进行中的合作项目\t标签\t联劝对接人\t\t\n\t\nINSGG202500592\n\t\n--\n\t\n福建省担当者行动教育基金会2\n\t\n安庆旭\n\t\n~\n\t\n未合作\n\t\n--\n\t\n--\n\t\n--\n\t\n--\n\t\n\t\nINSGG202500910\n\t\n执行方\n\t\n佳县慈爱社会公益事业服务中心\n\t\n屈红艳\n\t\n~\n\t\n未合作\n\t\n--\n\t\n--\n\t\n--\n\t\n--\n\t\n\t\nI", "url_path": "/ins", "analysis_timestamp": "2025-06-30T18:03:08.206827"}}, {"parent_menu": "可变配置", "sub_menu": "合同管理", "url": "http://csboss.lianquan.org.cn/contract", "title": "基金会业务管理系统", "screenshot": "enhanced_sub_menu_可变配置_合同管理.png", "timestamp": "2025-06-30T18:04:05.446633", "exploration_method": "enhanced", "content_analysis": {"elements": {"buttons_count": 82, "tables_count": 7, "forms_count": 1, "inputs_count": 24}, "buttons": [{"text": "提醒设置", "type": "button", "class": "el-button left el-button--small"}, {"text": "新增", "type": "button", "class": "el-button left middle-space add-button el-button--primary el-button--small"}, {"text": "导出n天内即将过期的合同", "type": "button", "class": "el-button el-button--default el-button--small"}, {"text": "查询", "type": "button", "class": "el-button middle-space el-button--primary el-button--small"}, {"text": "重置", "type": "button", "class": "el-button middle-space el-button--default el-button--small"}, {"text": "导出", "type": "button", "class": "el-button middle-space export-btn el-button--default el-button--small"}, {"text": "更多", "type": "button", "class": "el-button middle-space el-button--default el-button--small"}], "tables": [{"headers": ["合同编号", "合同名称", "合同类型", "合同类别", "签约单位", "合同金额", "合同状态【流程节点】审核人", "申请人", "申请部门"], "header_count": 9, "row_count": 63}, {"headers": ["合同编号", "合同名称", "合同类型", "合同类别", "签约单位", "合同金额", "合同状态【流程节点】审核人", "申请人", "申请部门"], "header_count": 9, "row_count": 1}, {"headers": [], "header_count": 0, "row_count": 20}, {"headers": ["序号"], "header_count": 1, "row_count": 1}, {"headers": [], "header_count": 0, "row_count": 20}], "features": {"has_search": false, "has_pagination": true, "has_add_button": true, "has_export_button": true, "has_edit_button": false, "has_delete_button": false, "has_filter": false, "has_tabs": true, "has_modal": true, "has_upload": false}, "page_title": "基金会业务管理系统", "content_preview": "合同管理作废审批记录\n合同编号：\n合同名称：\n合同类型：\n合同类别：\n合同状态：\n签约单位：\n提醒设置\n新增\n导出n天内即将过期的合同 查询 重置 导出 更多\n\t合同编号\t合同名称\t合同类型\t合同类别\t签约单位\t合同金额\n\t合同状态【流程节点】审核人\t申请人\t申请部门\t申请日期\t签约有效期\t标签\t\t\n\t\nHT2025999\n\t\n资助协议\n\t\n付款\n\t\n资助协议\n\t\n佛吉亚（中国）投资有限公司，无锡市德盛社会工作服务中心\n\t\n118,000.00\n\t\n草稿\n\t\n何松\n\t\n基金会\n\t\n--\n\t\n2021-11-30~2022-08-31\n\t\n--\n\t\n\t\nHT2025998\n\t\n资助协议\n\t\n付款", "url_path": "/contract", "analysis_timestamp": "2025-06-30T18:04:05.633840"}}, {"parent_menu": "可变配置", "sub_menu": "安全审计", "url": "http://csboss.lianquan.org.cn/contract", "title": "基金会业务管理系统", "screenshot": "enhanced_sub_menu_可变配置_安全审计.png", "timestamp": "2025-06-30T18:04:17.957289", "exploration_method": "enhanced", "content_analysis": {"elements": {"buttons_count": 82, "tables_count": 7, "forms_count": 1, "inputs_count": 24}, "buttons": [{"text": "提醒设置", "type": "button", "class": "el-button left el-button--small"}, {"text": "新增", "type": "button", "class": "el-button left middle-space add-button el-button--primary el-button--small"}, {"text": "导出n天内即将过期的合同", "type": "button", "class": "el-button el-button--default el-button--small"}, {"text": "查询", "type": "button", "class": "el-button middle-space el-button--primary el-button--small"}, {"text": "重置", "type": "button", "class": "el-button middle-space el-button--default el-button--small"}, {"text": "导出", "type": "button", "class": "el-button middle-space export-btn el-button--default el-button--small"}, {"text": "更多", "type": "button", "class": "el-button middle-space el-button--default el-button--small"}], "tables": [{"headers": ["合同编号", "合同名称", "合同类型", "合同类别", "签约单位", "合同金额", "合同状态【流程节点】审核人", "申请人", "申请部门"], "header_count": 9, "row_count": 63}, {"headers": ["合同编号", "合同名称", "合同类型", "合同类别", "签约单位", "合同金额", "合同状态【流程节点】审核人", "申请人", "申请部门"], "header_count": 9, "row_count": 1}, {"headers": [], "header_count": 0, "row_count": 20}, {"headers": ["序号"], "header_count": 1, "row_count": 1}, {"headers": [], "header_count": 0, "row_count": 20}], "features": {"has_search": false, "has_pagination": true, "has_add_button": true, "has_export_button": true, "has_edit_button": false, "has_delete_button": false, "has_filter": false, "has_tabs": true, "has_modal": true, "has_upload": false}, "page_title": "基金会业务管理系统", "content_preview": "合同管理作废审批记录\n合同编号：\n合同名称：\n合同类型：\n合同类别：\n合同状态：\n签约单位：\n提醒设置\n新增\n导出n天内即将过期的合同 查询 重置 导出 更多\n\t合同编号\t合同名称\t合同类型\t合同类别\t签约单位\t合同金额\n\t合同状态【流程节点】审核人\t申请人\t申请部门\t申请日期\t签约有效期\t标签\t\t\n\t\nHT2025999\n\t\n资助协议\n\t\n付款\n\t\n资助协议\n\t\n佛吉亚（中国）投资有限公司，无锡市德盛社会工作服务中心\n\t\n118,000.00\n\t\n草稿\n\t\n何松\n\t\n基金会\n\t\n--\n\t\n2021-11-30~2022-08-31\n\t\n--\n\t\n\t\nHT2025998\n\t\n资助协议\n\t\n付款", "url_path": "/contract", "analysis_timestamp": "2025-06-30T18:04:18.155665"}}, {"parent_menu": "安全审计", "sub_menu": "业务管理", "url": "http://csboss.lianquan.org.cn/statistic/bill/board", "title": "基金会业务管理系统", "screenshot": "enhanced_sub_menu_安全审计_业务管理.png", "timestamp": "2025-06-30T18:04:40.466784", "exploration_method": "enhanced", "content_analysis": {"elements": {"buttons_count": 2, "tables_count": 0, "forms_count": 0, "inputs_count": 5}, "buttons": [], "tables": [], "features": {"has_search": false, "has_pagination": false, "has_add_button": false, "has_export_button": false, "has_edit_button": false, "has_delete_button": false, "has_filter": false, "has_tabs": false, "has_modal": true, "has_upload": false}, "page_title": "基金会业务管理系统", "content_preview": "", "url_path": "/statistic/bill/board", "analysis_timestamp": "2025-06-30T18:04:40.488768"}}, {"parent_menu": "安全审计", "sub_menu": "可变配置", "url": "http://csboss.lianquan.org.cn/statistic/bill/board", "title": "基金会业务管理系统", "screenshot": "enhanced_sub_menu_安全审计_可变配置.png", "timestamp": "2025-06-30T18:04:52.835595", "exploration_method": "enhanced", "content_analysis": {"elements": {"buttons_count": 2, "tables_count": 0, "forms_count": 0, "inputs_count": 5}, "buttons": [], "tables": [], "features": {"has_search": false, "has_pagination": false, "has_add_button": false, "has_export_button": false, "has_edit_button": false, "has_delete_button": false, "has_filter": false, "has_tabs": false, "has_modal": true, "has_upload": false}, "page_title": "基金会业务管理系统", "content_preview": "", "url_path": "/statistic/bill/board", "analysis_timestamp": "2025-06-30T18:04:52.856320"}}, {"parent_menu": "安全审计", "sub_menu": "合作方管理", "url": "http://csboss.lianquan.org.cn/ins", "title": "基金会业务管理系统", "screenshot": "enhanced_sub_menu_安全审计_合作方管理.png", "timestamp": "2025-06-30T18:05:05.219316", "exploration_method": "enhanced", "content_analysis": {"elements": {"buttons_count": 85, "tables_count": 7, "forms_count": 1, "inputs_count": 23}, "buttons": [{"text": "相关说明", "type": "button", "class": "el-button middle-space el-button--primary el-button--small"}, {"text": "审核者设置", "type": "button", "class": "el-button el-button--default el-button--small"}, {"text": "新增", "type": "button", "class": "el-button middle-space add-button el-button--primary el-button--small"}, {"text": "查询", "type": "button", "class": "el-button middle-space el-button--primary el-button--small"}, {"text": "重置", "type": "button", "class": "el-button middle-space el-button--default el-button--small"}, {"text": "导出", "type": "button", "class": "el-button middle-space export-btn el-button--default el-button--small"}], "tables": [{"headers": ["合作方编号", "合作方类型", "机构名称", "机构对接人姓名", "合作期间", "合作状态", "累计合作项目数量", "进行中的合作项目", "标签"], "header_count": 9, "row_count": 63}, {"headers": ["合作方编号", "合作方类型", "机构名称", "机构对接人姓名", "合作期间", "合作状态", "累计合作项目数量", "进行中的合作项目", "标签"], "header_count": 9, "row_count": 1}, {"headers": [], "header_count": 0, "row_count": 20}, {"headers": ["序号"], "header_count": 1, "row_count": 1}, {"headers": [], "header_count": 0, "row_count": 20}], "features": {"has_search": true, "has_pagination": true, "has_add_button": true, "has_export_button": true, "has_edit_button": false, "has_delete_button": false, "has_filter": false, "has_tabs": true, "has_modal": true, "has_upload": false}, "page_title": "基金会业务管理系统", "content_preview": "社会组织个人类型企业类型其他组织\n合作方类型：\n机构名称：\n对接人姓名：\n合作状态：\n标签：\n联劝对接人：\n相关说明 审核者设置 新增 查询 重置 导出\n\t合作方编号\t合作方类型\t机构名称\t机构对接人姓名\t合作期间\t合作状态\t累计合作项目数量\t进行中的合作项目\t标签\t联劝对接人\t\t\n\t\nINSGG202500592\n\t\n--\n\t\n福建省担当者行动教育基金会2\n\t\n安庆旭\n\t\n~\n\t\n未合作\n\t\n--\n\t\n--\n\t\n--\n\t\n--\n\t\n\t\nINSGG202500910\n\t\n执行方\n\t\n佳县慈爱社会公益事业服务中心\n\t\n屈红艳\n\t\n~\n\t\n未合作\n\t\n--\n\t\n--\n\t\n--\n\t\n--\n\t\n\t\nI", "url_path": "/ins", "analysis_timestamp": "2025-06-30T18:05:05.413912"}}, {"parent_menu": "安全审计", "sub_menu": "合同管理", "url": "http://csboss.lianquan.org.cn/contract", "title": "基金会业务管理系统", "screenshot": "enhanced_sub_menu_安全审计_合同管理.png", "timestamp": "2025-06-30T18:05:17.766798", "exploration_method": "enhanced", "content_analysis": {"elements": {"buttons_count": 82, "tables_count": 7, "forms_count": 1, "inputs_count": 24}, "buttons": [{"text": "提醒设置", "type": "button", "class": "el-button left el-button--small"}, {"text": "新增", "type": "button", "class": "el-button left middle-space add-button el-button--primary el-button--small"}, {"text": "导出n天内即将过期的合同", "type": "button", "class": "el-button el-button--default el-button--small"}, {"text": "查询", "type": "button", "class": "el-button middle-space el-button--primary el-button--small"}, {"text": "重置", "type": "button", "class": "el-button middle-space el-button--default el-button--small"}, {"text": "导出", "type": "button", "class": "el-button middle-space export-btn el-button--default el-button--small"}, {"text": "更多", "type": "button", "class": "el-button middle-space el-button--default el-button--small"}], "tables": [{"headers": ["合同编号", "合同名称", "合同类型", "合同类别", "签约单位", "合同金额", "合同状态【流程节点】审核人", "申请人", "申请部门"], "header_count": 9, "row_count": 63}, {"headers": ["合同编号", "合同名称", "合同类型", "合同类别", "签约单位", "合同金额", "合同状态【流程节点】审核人", "申请人", "申请部门"], "header_count": 9, "row_count": 1}, {"headers": [], "header_count": 0, "row_count": 20}, {"headers": ["序号"], "header_count": 1, "row_count": 1}, {"headers": [], "header_count": 0, "row_count": 20}], "features": {"has_search": false, "has_pagination": true, "has_add_button": true, "has_export_button": true, "has_edit_button": false, "has_delete_button": false, "has_filter": false, "has_tabs": true, "has_modal": true, "has_upload": false}, "page_title": "基金会业务管理系统", "content_preview": "合同管理作废审批记录\n合同编号：\n合同名称：\n合同类型：\n合同类别：\n合同状态：\n签约单位：\n提醒设置\n新增\n导出n天内即将过期的合同 查询 重置 导出 更多\n\t合同编号\t合同名称\t合同类型\t合同类别\t签约单位\t合同金额\n\t合同状态【流程节点】审核人\t申请人\t申请部门\t申请日期\t签约有效期\t标签\t\t\n\t\nHT2025999\n\t\n资助协议\n\t\n付款\n\t\n资助协议\n\t\n佛吉亚（中国）投资有限公司，无锡市德盛社会工作服务中心\n\t\n118,000.00\n\t\n草稿\n\t\n何松\n\t\n基金会\n\t\n--\n\t\n2021-11-30~2022-08-31\n\t\n--\n\t\n\t\nHT2025998\n\t\n资助协议\n\t\n付款", "url_path": "/contract", "analysis_timestamp": "2025-06-30T18:05:17.945765"}}, {"parent_menu": "安全审计", "sub_menu": "开票管理", "url": "http://csboss.lianquan.org.cn/contract", "title": "基金会业务管理系统", "screenshot": "enhanced_sub_menu_安全审计_开票管理.png", "timestamp": "2025-06-30T18:05:37.937438", "exploration_method": "enhanced", "content_analysis": {"elements": {"buttons_count": 82, "tables_count": 7, "forms_count": 1, "inputs_count": 24}, "buttons": [{"text": "提醒设置", "type": "button", "class": "el-button left el-button--small"}, {"text": "新增", "type": "button", "class": "el-button left middle-space add-button el-button--primary el-button--small"}, {"text": "导出n天内即将过期的合同", "type": "button", "class": "el-button el-button--default el-button--small"}, {"text": "查询", "type": "button", "class": "el-button middle-space el-button--primary el-button--small"}, {"text": "重置", "type": "button", "class": "el-button middle-space el-button--default el-button--small"}, {"text": "导出", "type": "button", "class": "el-button middle-space export-btn el-button--default el-button--small"}, {"text": "更多", "type": "button", "class": "el-button middle-space el-button--default el-button--small"}], "tables": [{"headers": ["合同编号", "合同名称", "合同类型", "合同类别", "签约单位", "合同金额", "合同状态【流程节点】审核人", "申请人", "申请部门"], "header_count": 9, "row_count": 63}, {"headers": ["合同编号", "合同名称", "合同类型", "合同类别", "签约单位", "合同金额", "合同状态【流程节点】审核人", "申请人", "申请部门"], "header_count": 9, "row_count": 1}, {"headers": [], "header_count": 0, "row_count": 20}, {"headers": ["序号"], "header_count": 1, "row_count": 1}, {"headers": [], "header_count": 0, "row_count": 20}], "features": {"has_search": false, "has_pagination": true, "has_add_button": true, "has_export_button": true, "has_edit_button": false, "has_delete_button": false, "has_filter": false, "has_tabs": true, "has_modal": true, "has_upload": false}, "page_title": "基金会业务管理系统", "content_preview": "合同管理作废审批记录\n合同编号：\n合同名称：\n合同类型：\n合同类别：\n合同状态：\n签约单位：\n提醒设置\n新增\n导出n天内即将过期的合同 查询 重置 导出 更多\n\t合同编号\t合同名称\t合同类型\t合同类别\t签约单位\t合同金额\n\t合同状态【流程节点】审核人\t申请人\t申请部门\t申请日期\t签约有效期\t标签\t\t\n\t\nHT2025999\n\t\n资助协议\n\t\n付款\n\t\n资助协议\n\t\n佛吉亚（中国）投资有限公司，无锡市德盛社会工作服务中心\n\t\n118,000.00\n\t\n草稿\n\t\n何松\n\t\n基金会\n\t\n--\n\t\n2021-11-30~2022-08-31\n\t\n--\n\t\n\t\nHT2025998\n\t\n资助协议\n\t\n付款", "url_path": "/contract", "analysis_timestamp": "2025-06-30T18:05:38.115187"}}, {"parent_menu": "安全审计", "sub_menu": "捐方管理", "url": "http://csboss.lianquan.org.cn/donor/manage", "title": "基金会业务管理系统", "screenshot": "enhanced_sub_menu_安全审计_捐方管理.png", "timestamp": "2025-06-30T18:05:50.465190", "exploration_method": "enhanced", "content_analysis": {"elements": {"buttons_count": 15, "tables_count": 7, "forms_count": 1, "inputs_count": 24}, "buttons": [{"text": "新增", "type": "button", "class": "el-button left middle-space add-button el-button--primary el-button--small"}, {"text": "查询", "type": "button", "class": "el-button middle-space el-button--primary el-button--small"}, {"text": "重置", "type": "button", "class": "el-button middle-space el-button--default el-button--small"}, {"text": "导出", "type": "button", "class": "el-button middle-space export-btn el-button--default el-button--small"}, {"text": "更多", "type": "button", "class": "el-button middle-space el-button--default el-button--small"}, {"text": "关闭", "type": "button", "class": "el-button el-button--default el-button--small"}, {"text": "确定", "type": "button", "class": "el-button el-button--primary el-button--small"}], "tables": [{"headers": ["捐方编号", "捐方名称", "捐方简称", "所属小组", "行业类别", "捐方规模", "年营业收入", "企业性质", "上市状态"], "header_count": 9, "row_count": 3}, {"headers": ["捐方编号", "捐方名称", "捐方简称", "所属小组", "行业类别", "捐方规模", "年营业收入", "企业性质", "上市状态"], "header_count": 9, "row_count": 1}, {"headers": [], "header_count": 0, "row_count": 0}, {"headers": ["序号"], "header_count": 1, "row_count": 1}, {"headers": [], "header_count": 0, "row_count": 0}], "features": {"has_search": false, "has_pagination": true, "has_add_button": true, "has_export_button": true, "has_edit_button": false, "has_delete_button": false, "has_filter": false, "has_tabs": true, "has_modal": true, "has_upload": false}, "page_title": "基金会业务管理系统", "content_preview": "企业类型社会组织其他组织个人\n捐方编号：\n捐方名称：\n捐方简称：\n所属小组：\n行业类别：\n捐方规模：\n年营业收入：\n新增\n查询 重置 导出 更多\n\t捐方编号\t捐方名称\t捐方简称\t所属小组\t行业类别\t捐方规模\t年营业收入\t企业性质\t上市状态\t企业成立日期\t业务覆盖范围\t联系地址\t标签\t联系人姓名\t累计管理费金额\t单笔捐赠最大金额\n\t累计捐赠金额\t累计管理费比例\t最新捐赠日期\t累计捐赠项目数量\n\t资助最多次项目名称\t\n\n暂无记录，请更换条件重新搜索\n\n序号\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t操作\n共 0 条\n1\n前往页", "url_path": "/donor/manage", "analysis_timestamp": "2025-06-30T18:05:50.613508"}}, {"parent_menu": "组织管理", "sub_menu": "业务管理", "url": "http://csboss.lianquan.org.cn/statistic/bill/board", "title": "基金会业务管理系统", "screenshot": "enhanced_sub_menu_组织管理_业务管理.png", "timestamp": "2025-06-30T18:06:57.747586", "exploration_method": "enhanced", "content_analysis": {"elements": {"buttons_count": 2, "tables_count": 0, "forms_count": 0, "inputs_count": 5}, "buttons": [], "tables": [], "features": {"has_search": false, "has_pagination": false, "has_add_button": false, "has_export_button": false, "has_edit_button": false, "has_delete_button": false, "has_filter": false, "has_tabs": false, "has_modal": true, "has_upload": false}, "page_title": "基金会业务管理系统", "content_preview": "", "url_path": "/statistic/bill/board", "analysis_timestamp": "2025-06-30T18:06:57.769781"}}, {"parent_menu": "组织管理", "sub_menu": "可变配置", "url": "http://csboss.lianquan.org.cn/statistic/bill/board", "title": "基金会业务管理系统", "screenshot": "enhanced_sub_menu_组织管理_可变配置.png", "timestamp": "2025-06-30T18:07:10.108935", "exploration_method": "enhanced", "content_analysis": {"elements": {"buttons_count": 2, "tables_count": 0, "forms_count": 0, "inputs_count": 5}, "buttons": [], "tables": [], "features": {"has_search": false, "has_pagination": false, "has_add_button": false, "has_export_button": false, "has_edit_button": false, "has_delete_button": false, "has_filter": false, "has_tabs": false, "has_modal": true, "has_upload": false}, "page_title": "基金会业务管理系统", "content_preview": "", "url_path": "/statistic/bill/board", "analysis_timestamp": "2025-06-30T18:07:10.130972"}}, {"parent_menu": "组织管理", "sub_menu": "合作方管理", "url": "http://csboss.lianquan.org.cn/ins", "title": "基金会业务管理系统", "screenshot": "enhanced_sub_menu_组织管理_合作方管理.png", "timestamp": "2025-06-30T18:07:22.493598", "exploration_method": "enhanced", "content_analysis": {"elements": {"buttons_count": 85, "tables_count": 7, "forms_count": 1, "inputs_count": 23}, "buttons": [{"text": "相关说明", "type": "button", "class": "el-button middle-space el-button--primary el-button--small"}, {"text": "审核者设置", "type": "button", "class": "el-button el-button--default el-button--small"}, {"text": "新增", "type": "button", "class": "el-button middle-space add-button el-button--primary el-button--small"}, {"text": "查询", "type": "button", "class": "el-button middle-space el-button--primary el-button--small"}, {"text": "重置", "type": "button", "class": "el-button middle-space el-button--default el-button--small"}, {"text": "导出", "type": "button", "class": "el-button middle-space export-btn el-button--default el-button--small"}], "tables": [{"headers": ["合作方编号", "合作方类型", "机构名称", "机构对接人姓名", "合作期间", "合作状态", "累计合作项目数量", "进行中的合作项目", "标签"], "header_count": 9, "row_count": 63}, {"headers": ["合作方编号", "合作方类型", "机构名称", "机构对接人姓名", "合作期间", "合作状态", "累计合作项目数量", "进行中的合作项目", "标签"], "header_count": 9, "row_count": 1}, {"headers": [], "header_count": 0, "row_count": 20}, {"headers": ["序号"], "header_count": 1, "row_count": 1}, {"headers": [], "header_count": 0, "row_count": 20}], "features": {"has_search": true, "has_pagination": true, "has_add_button": true, "has_export_button": true, "has_edit_button": false, "has_delete_button": false, "has_filter": false, "has_tabs": true, "has_modal": true, "has_upload": false}, "page_title": "基金会业务管理系统", "content_preview": "社会组织个人类型企业类型其他组织\n合作方类型：\n机构名称：\n对接人姓名：\n合作状态：\n标签：\n联劝对接人：\n相关说明 审核者设置 新增 查询 重置 导出\n\t合作方编号\t合作方类型\t机构名称\t机构对接人姓名\t合作期间\t合作状态\t累计合作项目数量\t进行中的合作项目\t标签\t联劝对接人\t\t\n\t\nINSGG202500592\n\t\n--\n\t\n福建省担当者行动教育基金会2\n\t\n安庆旭\n\t\n~\n\t\n未合作\n\t\n--\n\t\n--\n\t\n--\n\t\n--\n\t\n\t\nINSGG202500910\n\t\n执行方\n\t\n佳县慈爱社会公益事业服务中心\n\t\n屈红艳\n\t\n~\n\t\n未合作\n\t\n--\n\t\n--\n\t\n--\n\t\n--\n\t\n\t\nI", "url_path": "/ins", "analysis_timestamp": "2025-06-30T18:07:22.679298"}}, {"parent_menu": "组织管理", "sub_menu": "合同管理", "url": "http://csboss.lianquan.org.cn/contract", "title": "基金会业务管理系统", "screenshot": "enhanced_sub_menu_组织管理_合同管理.png", "timestamp": "2025-06-30T18:07:35.030834", "exploration_method": "enhanced", "content_analysis": {"elements": {"buttons_count": 82, "tables_count": 7, "forms_count": 1, "inputs_count": 24}, "buttons": [{"text": "提醒设置", "type": "button", "class": "el-button left el-button--small"}, {"text": "新增", "type": "button", "class": "el-button left middle-space add-button el-button--primary el-button--small"}, {"text": "导出n天内即将过期的合同", "type": "button", "class": "el-button el-button--default el-button--small"}, {"text": "查询", "type": "button", "class": "el-button middle-space el-button--primary el-button--small"}, {"text": "重置", "type": "button", "class": "el-button middle-space el-button--default el-button--small"}, {"text": "导出", "type": "button", "class": "el-button middle-space export-btn el-button--default el-button--small"}, {"text": "更多", "type": "button", "class": "el-button middle-space el-button--default el-button--small"}], "tables": [{"headers": ["合同编号", "合同名称", "合同类型", "合同类别", "签约单位", "合同金额", "合同状态【流程节点】审核人", "申请人", "申请部门"], "header_count": 9, "row_count": 63}, {"headers": ["合同编号", "合同名称", "合同类型", "合同类别", "签约单位", "合同金额", "合同状态【流程节点】审核人", "申请人", "申请部门"], "header_count": 9, "row_count": 1}, {"headers": [], "header_count": 0, "row_count": 20}, {"headers": ["序号"], "header_count": 1, "row_count": 1}, {"headers": [], "header_count": 0, "row_count": 20}], "features": {"has_search": false, "has_pagination": true, "has_add_button": true, "has_export_button": true, "has_edit_button": false, "has_delete_button": false, "has_filter": false, "has_tabs": true, "has_modal": true, "has_upload": false}, "page_title": "基金会业务管理系统", "content_preview": "合同管理作废审批记录\n合同编号：\n合同名称：\n合同类型：\n合同类别：\n合同状态：\n签约单位：\n提醒设置\n新增\n导出n天内即将过期的合同 查询 重置 导出 更多\n\t合同编号\t合同名称\t合同类型\t合同类别\t签约单位\t合同金额\n\t合同状态【流程节点】审核人\t申请人\t申请部门\t申请日期\t签约有效期\t标签\t\t\n\t\nHT2025999\n\t\n资助协议\n\t\n付款\n\t\n资助协议\n\t\n佛吉亚（中国）投资有限公司，无锡市德盛社会工作服务中心\n\t\n118,000.00\n\t\n草稿\n\t\n何松\n\t\n基金会\n\t\n--\n\t\n2021-11-30~2022-08-31\n\t\n--\n\t\n\t\nHT2025998\n\t\n资助协议\n\t\n付款", "url_path": "/contract", "analysis_timestamp": "2025-06-30T18:07:35.204641"}}, {"parent_menu": "组织管理", "sub_menu": "安全审计", "url": "http://csboss.lianquan.org.cn/contract", "title": "基金会业务管理系统", "screenshot": "enhanced_sub_menu_组织管理_安全审计.png", "timestamp": "2025-06-30T18:07:47.511832", "exploration_method": "enhanced", "content_analysis": {"elements": {"buttons_count": 82, "tables_count": 7, "forms_count": 1, "inputs_count": 24}, "buttons": [{"text": "提醒设置", "type": "button", "class": "el-button left el-button--small"}, {"text": "新增", "type": "button", "class": "el-button left middle-space add-button el-button--primary el-button--small"}, {"text": "导出n天内即将过期的合同", "type": "button", "class": "el-button el-button--default el-button--small"}, {"text": "查询", "type": "button", "class": "el-button middle-space el-button--primary el-button--small"}, {"text": "重置", "type": "button", "class": "el-button middle-space el-button--default el-button--small"}, {"text": "导出", "type": "button", "class": "el-button middle-space export-btn el-button--default el-button--small"}, {"text": "更多", "type": "button", "class": "el-button middle-space el-button--default el-button--small"}], "tables": [{"headers": ["合同编号", "合同名称", "合同类型", "合同类别", "签约单位", "合同金额", "合同状态【流程节点】审核人", "申请人", "申请部门"], "header_count": 9, "row_count": 63}, {"headers": ["合同编号", "合同名称", "合同类型", "合同类别", "签约单位", "合同金额", "合同状态【流程节点】审核人", "申请人", "申请部门"], "header_count": 9, "row_count": 1}, {"headers": [], "header_count": 0, "row_count": 20}, {"headers": ["序号"], "header_count": 1, "row_count": 1}, {"headers": [], "header_count": 0, "row_count": 20}], "features": {"has_search": false, "has_pagination": true, "has_add_button": true, "has_export_button": true, "has_edit_button": false, "has_delete_button": false, "has_filter": false, "has_tabs": true, "has_modal": true, "has_upload": false}, "page_title": "基金会业务管理系统", "content_preview": "合同管理作废审批记录\n合同编号：\n合同名称：\n合同类型：\n合同类别：\n合同状态：\n签约单位：\n提醒设置\n新增\n导出n天内即将过期的合同 查询 重置 导出 更多\n\t合同编号\t合同名称\t合同类型\t合同类别\t签约单位\t合同金额\n\t合同状态【流程节点】审核人\t申请人\t申请部门\t申请日期\t签约有效期\t标签\t\t\n\t\nHT2025999\n\t\n资助协议\n\t\n付款\n\t\n资助协议\n\t\n佛吉亚（中国）投资有限公司，无锡市德盛社会工作服务中心\n\t\n118,000.00\n\t\n草稿\n\t\n何松\n\t\n基金会\n\t\n--\n\t\n2021-11-30~2022-08-31\n\t\n--\n\t\n\t\nHT2025998\n\t\n资助协议\n\t\n付款", "url_path": "/contract", "analysis_timestamp": "2025-06-30T18:07:47.695671"}}, {"parent_menu": "组织管理", "sub_menu": "开票管理", "url": "http://csboss.lianquan.org.cn/contract", "title": "基金会业务管理系统", "screenshot": "enhanced_sub_menu_组织管理_开票管理.png", "timestamp": "2025-06-30T18:08:00.002757", "exploration_method": "enhanced", "content_analysis": {"elements": {"buttons_count": 82, "tables_count": 7, "forms_count": 1, "inputs_count": 24}, "buttons": [{"text": "提醒设置", "type": "button", "class": "el-button left el-button--small"}, {"text": "新增", "type": "button", "class": "el-button left middle-space add-button el-button--primary el-button--small"}, {"text": "导出n天内即将过期的合同", "type": "button", "class": "el-button el-button--default el-button--small"}, {"text": "查询", "type": "button", "class": "el-button middle-space el-button--primary el-button--small"}, {"text": "重置", "type": "button", "class": "el-button middle-space el-button--default el-button--small"}, {"text": "导出", "type": "button", "class": "el-button middle-space export-btn el-button--default el-button--small"}, {"text": "更多", "type": "button", "class": "el-button middle-space el-button--default el-button--small"}], "tables": [{"headers": ["合同编号", "合同名称", "合同类型", "合同类别", "签约单位", "合同金额", "合同状态【流程节点】审核人", "申请人", "申请部门"], "header_count": 9, "row_count": 63}, {"headers": ["合同编号", "合同名称", "合同类型", "合同类别", "签约单位", "合同金额", "合同状态【流程节点】审核人", "申请人", "申请部门"], "header_count": 9, "row_count": 1}, {"headers": [], "header_count": 0, "row_count": 20}, {"headers": ["序号"], "header_count": 1, "row_count": 1}, {"headers": [], "header_count": 0, "row_count": 20}], "features": {"has_search": false, "has_pagination": true, "has_add_button": true, "has_export_button": true, "has_edit_button": false, "has_delete_button": false, "has_filter": false, "has_tabs": true, "has_modal": true, "has_upload": false}, "page_title": "基金会业务管理系统", "content_preview": "合同管理作废审批记录\n合同编号：\n合同名称：\n合同类型：\n合同类别：\n合同状态：\n签约单位：\n提醒设置\n新增\n导出n天内即将过期的合同 查询 重置 导出 更多\n\t合同编号\t合同名称\t合同类型\t合同类别\t签约单位\t合同金额\n\t合同状态【流程节点】审核人\t申请人\t申请部门\t申请日期\t签约有效期\t标签\t\t\n\t\nHT2025999\n\t\n资助协议\n\t\n付款\n\t\n资助协议\n\t\n佛吉亚（中国）投资有限公司，无锡市德盛社会工作服务中心\n\t\n118,000.00\n\t\n草稿\n\t\n何松\n\t\n基金会\n\t\n--\n\t\n2021-11-30~2022-08-31\n\t\n--\n\t\n\t\nHT2025998\n\t\n资助协议\n\t\n付款", "url_path": "/contract", "analysis_timestamp": "2025-06-30T18:08:00.181857"}}, {"parent_menu": "组织管理", "sub_menu": "捐方管理", "url": "http://csboss.lianquan.org.cn/donor/manage", "title": "基金会业务管理系统", "screenshot": "enhanced_sub_menu_组织管理_捐方管理.png", "timestamp": "2025-06-30T18:08:12.531505", "exploration_method": "enhanced", "content_analysis": {"elements": {"buttons_count": 15, "tables_count": 7, "forms_count": 1, "inputs_count": 24}, "buttons": [{"text": "新增", "type": "button", "class": "el-button left middle-space add-button el-button--primary el-button--small"}, {"text": "查询", "type": "button", "class": "el-button middle-space el-button--primary el-button--small"}, {"text": "重置", "type": "button", "class": "el-button middle-space el-button--default el-button--small"}, {"text": "导出", "type": "button", "class": "el-button middle-space export-btn el-button--default el-button--small"}, {"text": "更多", "type": "button", "class": "el-button middle-space el-button--default el-button--small"}, {"text": "关闭", "type": "button", "class": "el-button el-button--default el-button--small"}, {"text": "确定", "type": "button", "class": "el-button el-button--primary el-button--small"}], "tables": [{"headers": ["捐方编号", "捐方名称", "捐方简称", "所属小组", "行业类别", "捐方规模", "年营业收入", "企业性质", "上市状态"], "header_count": 9, "row_count": 3}, {"headers": ["捐方编号", "捐方名称", "捐方简称", "所属小组", "行业类别", "捐方规模", "年营业收入", "企业性质", "上市状态"], "header_count": 9, "row_count": 1}, {"headers": [], "header_count": 0, "row_count": 0}, {"headers": ["序号"], "header_count": 1, "row_count": 1}, {"headers": [], "header_count": 0, "row_count": 0}], "features": {"has_search": false, "has_pagination": true, "has_add_button": true, "has_export_button": true, "has_edit_button": false, "has_delete_button": false, "has_filter": false, "has_tabs": true, "has_modal": true, "has_upload": false}, "page_title": "基金会业务管理系统", "content_preview": "企业类型社会组织其他组织个人\n捐方编号：\n捐方名称：\n捐方简称：\n所属小组：\n行业类别：\n捐方规模：\n年营业收入：\n新增\n查询 重置 导出 更多\n\t捐方编号\t捐方名称\t捐方简称\t所属小组\t行业类别\t捐方规模\t年营业收入\t企业性质\t上市状态\t企业成立日期\t业务覆盖范围\t联系地址\t标签\t联系人姓名\t累计管理费金额\t单笔捐赠最大金额\n\t累计捐赠金额\t累计管理费比例\t最新捐赠日期\t累计捐赠项目数量\n\t资助最多次项目名称\t\n\n暂无记录，请更换条件重新搜索\n\n序号\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t操作\n共 0 条\n1\n前往页", "url_path": "/donor/manage", "analysis_timestamp": "2025-06-30T18:08:12.687806"}}, {"parent_menu": "组织管理", "sub_menu": "支出管理", "url": "http://csboss.lianquan.org.cn/donor/manage", "title": "基金会业务管理系统", "screenshot": "enhanced_sub_menu_组织管理_支出管理.png", "timestamp": "2025-06-30T18:08:24.987910", "exploration_method": "enhanced", "content_analysis": {"elements": {"buttons_count": 15, "tables_count": 7, "forms_count": 1, "inputs_count": 24}, "buttons": [{"text": "新增", "type": "button", "class": "el-button left middle-space add-button el-button--primary el-button--small"}, {"text": "查询", "type": "button", "class": "el-button middle-space el-button--primary el-button--small"}, {"text": "重置", "type": "button", "class": "el-button middle-space el-button--default el-button--small"}, {"text": "导出", "type": "button", "class": "el-button middle-space export-btn el-button--default el-button--small"}, {"text": "更多", "type": "button", "class": "el-button middle-space el-button--default el-button--small"}, {"text": "关闭", "type": "button", "class": "el-button el-button--default el-button--small"}, {"text": "确定", "type": "button", "class": "el-button el-button--primary el-button--small"}], "tables": [{"headers": ["捐方编号", "捐方名称", "捐方简称", "所属小组", "行业类别", "捐方规模", "年营业收入", "企业性质", "上市状态"], "header_count": 9, "row_count": 3}, {"headers": ["捐方编号", "捐方名称", "捐方简称", "所属小组", "行业类别", "捐方规模", "年营业收入", "企业性质", "上市状态"], "header_count": 9, "row_count": 1}, {"headers": [], "header_count": 0, "row_count": 0}, {"headers": ["序号"], "header_count": 1, "row_count": 1}, {"headers": [], "header_count": 0, "row_count": 0}], "features": {"has_search": false, "has_pagination": true, "has_add_button": true, "has_export_button": true, "has_edit_button": false, "has_delete_button": false, "has_filter": false, "has_tabs": true, "has_modal": true, "has_upload": false}, "page_title": "基金会业务管理系统", "content_preview": "企业类型社会组织其他组织个人\n捐方编号：\n捐方名称：\n捐方简称：\n所属小组：\n行业类别：\n捐方规模：\n年营业收入：\n新增\n查询 重置 导出 更多\n\t捐方编号\t捐方名称\t捐方简称\t所属小组\t行业类别\t捐方规模\t年营业收入\t企业性质\t上市状态\t企业成立日期\t业务覆盖范围\t联系地址\t标签\t联系人姓名\t累计管理费金额\t单笔捐赠最大金额\n\t累计捐赠金额\t累计管理费比例\t最新捐赠日期\t累计捐赠项目数量\n\t资助最多次项目名称\t\n\n暂无记录，请更换条件重新搜索\n\n序号\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t操作\n共 0 条\n1\n前往页", "url_path": "/donor/manage", "analysis_timestamp": "2025-06-30T18:08:25.144113"}}]