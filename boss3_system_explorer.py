#!/usr/bin/env python3
"""
Boss3系统功能探索脚本
用于自动化遍历Boss3系统的所有功能模块，生成使用手册
"""

import asyncio
import os
import json
import time
from datetime import datetime
from pathlib import Path
from playwright.async_api import async_playwright, <PERSON>, Browser
import pandas as pd

class Boss3SystemExplorer:
    def __init__(self):
        self.base_url = "http://csboss.lianquan.org.cn"
        self.username = "wangjinxiao"
        self.password = "Ok756756@"
        self.screenshots_dir = Path("screenshots")
        self.data_dir = Path("data")
        self.browser = None
        self.page = None
        self.menu_structure = {}
        self.function_details = []
        
        # 创建必要的目录
        self.screenshots_dir.mkdir(exist_ok=True)
        self.data_dir.mkdir(exist_ok=True)
    
    async def start_browser(self):
        """启动浏览器"""
        playwright = await async_playwright().start()
        self.browser = await playwright.chromium.launch(
            headless=False,  # 设置为False以便观察过程
            args=['--no-sandbox', '--disable-dev-shm-usage']
        )
        context = await self.browser.new_context(
            viewport={'width': 1920, 'height': 1080}
        )
        self.page = await context.new_page()
        
    async def login(self):
        """登录系统"""
        print(f"正在访问登录页面: {self.base_url}")
        await self.page.goto(self.base_url)
        await self.page.wait_for_load_state('networkidle')
        
        # 截取登录页面
        await self.page.screenshot(path=self.screenshots_dir / "01_login_page.png")
        
        # 查找并填写用户名
        username_selectors = [
            'input[name="username"]',
            'input[type="text"]',
            '#username',
            '.username',
            'input[placeholder*="用户名"]',
            'input[placeholder*="账号"]'
        ]
        
        username_filled = False
        for selector in username_selectors:
            try:
                await self.page.wait_for_selector(selector, timeout=2000)
                await self.page.fill(selector, self.username)
                username_filled = True
                print(f"用户名已填写: {selector}")
                break
            except:
                continue
        
        if not username_filled:
            print("未找到用户名输入框，尝试查看页面内容...")
            content = await self.page.content()
            print("页面内容预览:", content[:500])
            return False
        
        # 查找并填写密码
        password_selectors = [
            'input[name="password"]',
            'input[type="password"]',
            '#password',
            '.password'
        ]
        
        password_filled = False
        for selector in password_selectors:
            try:
                await self.page.wait_for_selector(selector, timeout=2000)
                await self.page.fill(selector, self.password)
                password_filled = True
                print(f"密码已填写: {selector}")
                break
            except:
                continue
        
        if not password_filled:
            print("未找到密码输入框")
            return False
        
        # 查找并点击登录按钮
        login_selectors = [
            'button[type="submit"]',
            'input[type="submit"]',
            'button:has-text("登录")',
            'button:has-text("登陆")',
            'button:has-text("Login")',
            '.login-btn',
            '#login-btn',
            'button',
            '.btn',
            'input[value*="登录"]',
            'input[value*="登陆"]'
        ]

        login_clicked = False
        for selector in login_selectors:
            try:
                elements = await self.page.query_selector_all(selector)
                for element in elements:
                    text = await element.inner_text() if await element.inner_text() else ""
                    value = await element.get_attribute('value') if await element.get_attribute('value') else ""
                    print(f"找到元素: {selector}, 文本: '{text}', 值: '{value}'")

                    combined_text = (text + value).replace(' ', '').lower()
                    if any(keyword in combined_text for keyword in ['登录', '登陆', 'login', 'submit']):
                        await element.click()
                        login_clicked = True
                        print(f"登录按钮已点击: {selector}")
                        break

                if login_clicked:
                    break
            except Exception as e:
                print(f"尝试选择器 {selector} 时出错: {e}")
                continue

        if not login_clicked:
            print("未找到登录按钮，尝试查看页面中所有按钮...")
            all_buttons = await self.page.query_selector_all('button, input[type="submit"], input[type="button"]')
            for i, btn in enumerate(all_buttons):
                text = await btn.inner_text() if await btn.inner_text() else ""
                value = await btn.get_attribute('value') if await btn.get_attribute('value') else ""
                btn_type = await btn.get_attribute('type') if await btn.get_attribute('type') else ""
                print(f"按钮 {i+1}: 文本='{text}', 值='{value}', 类型='{btn_type}'")
            return False
        
        # 等待登录完成
        await self.page.wait_for_load_state('networkidle')
        await asyncio.sleep(3)
        
        # 检查是否登录成功
        current_url = self.page.url
        print(f"登录后URL: {current_url}")
        
        # 截取登录后页面
        await self.page.screenshot(path=self.screenshots_dir / "02_after_login.png")
        
        return True
    
    async def explore_menu_structure(self):
        """探索菜单结构"""
        print("开始探索菜单结构...")

        # 等待页面完全加载
        await self.page.wait_for_load_state('networkidle')
        await asyncio.sleep(3)

        # 截取当前页面状态
        await self.page.screenshot(path=self.screenshots_dir / "03_main_interface.png")

        # 首先尝试分析页面结构
        await self.analyze_page_structure()

        # 尝试找到侧边栏菜单（通常是主功能菜单）
        sidebar_selectors = [
            '.sidebar',
            '.side-menu',
            '.main-sidebar',
            '.navigation',
            '.nav-sidebar',
            '#sidebar',
            '.left-sidebar'
        ]

        sidebar_found = False
        for selector in sidebar_selectors:
            try:
                sidebar = await self.page.query_selector(selector)
                if sidebar:
                    print(f"找到侧边栏: {selector}")
                    await self.extract_sidebar_menu(sidebar)
                    sidebar_found = True
                    break
            except:
                continue

        if not sidebar_found:
            # 如果没有找到侧边栏，尝试查找其他类型的菜单
            await self.find_alternative_menus()

        return True

    async def analyze_page_structure(self):
        """分析页面结构"""
        print("分析页面结构...")

        # 获取页面标题
        title = await self.page.title()
        print(f"页面标题: {title}")

        # 查找所有可能的菜单容器
        containers = await self.page.query_selector_all('div, nav, aside, ul')
        print(f"找到 {len(containers)} 个容器元素")

        # 分析每个容器的内容
        for i, container in enumerate(containers[:20]):  # 只分析前20个
            try:
                class_name = await container.get_attribute('class')
                id_name = await container.get_attribute('id')
                text_content = await container.inner_text()

                if class_name and ('menu' in class_name.lower() or 'nav' in class_name.lower() or 'sidebar' in class_name.lower()):
                    print(f"容器 {i+1}: class='{class_name}', id='{id_name}', 内容长度={len(text_content) if text_content else 0}")
                    if text_content and len(text_content) > 10:
                        print(f"  内容预览: {text_content[:100]}...")
            except:
                continue

    async def extract_sidebar_menu(self, sidebar_element):
        """提取侧边栏菜单"""
        print("正在提取侧边栏菜单...")

        # 查找侧边栏中的菜单项
        menu_items = await sidebar_element.query_selector_all('a, li, .menu-item, .nav-item')
        print(f"在侧边栏中找到 {len(menu_items)} 个菜单项")

        menu_data = []
        for i, item in enumerate(menu_items):
            try:
                text = await item.inner_text()
                href = await item.get_attribute('href')
                class_name = await item.get_attribute('class')

                if text and text.strip() and len(text.strip()) > 1:
                    menu_info = {
                        'index': i + 1,
                        'text': text.strip(),
                        'href': href,
                        'class': class_name,
                        'type': 'sidebar'
                    }
                    menu_data.append(menu_info)
                    print(f"侧边栏菜单项 {i+1}: {text.strip()} -> {href}")
            except Exception as e:
                print(f"提取侧边栏菜单项 {i+1} 时出错: {e}")

        # 保存侧边栏菜单数据
        with open(self.data_dir / "sidebar_menu.json", 'w', encoding='utf-8') as f:
            json.dump(menu_data, f, ensure_ascii=False, indent=2)

        # 探索侧边栏菜单项
        await self.explore_sidebar_menu_items(menu_data)

    async def find_alternative_menus(self):
        """查找替代菜单"""
        print("查找替代菜单...")

        # 查找页面中所有可能的导航元素
        nav_elements = await self.page.query_selector_all('nav, .navigation, .menu, ul')

        for i, nav in enumerate(nav_elements):
            try:
                class_name = await nav.get_attribute('class')
                text_content = await nav.inner_text()

                if text_content and len(text_content) > 20:
                    print(f"导航元素 {i+1}: class='{class_name}', 内容长度={len(text_content)}")

                    # 如果内容看起来像菜单，就提取它
                    if any(keyword in text_content.lower() for keyword in ['管理', '查询', '统计', '设置', '项目', '用户']):
                        await self.extract_menu_items(nav, f"nav_{i+1}")
            except:
                continue

    async def extract_menu_items(self, menu_element, menu_type="main"):
        """提取菜单项"""
        print(f"正在提取{menu_type}菜单项...")

        # 查找所有菜单项
        menu_items = await menu_element.query_selector_all('a, li, .menu-item')
        print(f"找到 {len(menu_items)} 个菜单项")

        menu_data = []
        for i, item in enumerate(menu_items):
            try:
                text = await item.inner_text()
                href = await item.get_attribute('href')
                class_name = await item.get_attribute('class')

                if text and text.strip() and len(text.strip()) > 1:
                    menu_info = {
                        'index': i + 1,
                        'text': text.strip(),
                        'href': href,
                        'class': class_name,
                        'type': menu_type
                    }
                    menu_data.append(menu_info)
                    print(f"{menu_type}菜单项 {i+1}: {text.strip()} -> {href}")
            except Exception as e:
                print(f"提取{menu_type}菜单项 {i+1} 时出错: {e}")

        # 保存菜单数据
        filename = f"{menu_type}_menu_structure.json"
        with open(self.data_dir / filename, 'w', encoding='utf-8') as f:
            json.dump(menu_data, f, ensure_ascii=False, indent=2)

        # 如果是主菜单或侧边栏菜单，进行深度探索
        if menu_type in ["main", "sidebar"] and menu_data:
            await self.explore_main_menu_items(menu_data)

    async def find_navigation_links(self):
        """查找导航链接"""
        links = await self.page.query_selector_all('a')
        print(f"找到 {len(links)} 个链接")

        nav_links = []
        for i, link in enumerate(links):
            try:
                text = await link.inner_text()
                href = await link.get_attribute('href')
                if text and text.strip() and href:
                    nav_links.append({
                        'text': text.strip(),
                        'href': href
                    })
                    if len(nav_links) <= 20:  # 只显示前20个
                        print(f"链接 {len(nav_links)}: {text.strip()} -> {href}")
            except:
                continue

        # 保存链接数据
        with open(self.data_dir / "navigation_links.json", 'w', encoding='utf-8') as f:
            json.dump(nav_links, f, ensure_ascii=False, indent=2)

    async def explore_sidebar_menu_items(self, menu_data):
        """探索侧边栏菜单项"""
        print("开始探索侧边栏菜单项...")

        # 过滤出有效的菜单项
        valid_items = [item for item in menu_data if item['href'] and not item['href'].startswith('#') and item['href'].startswith('/')]

        for i, item in enumerate(valid_items[:15]):  # 限制探索前15个菜单项
            try:
                print(f"正在探索侧边栏菜单项: {item['text']}")

                # 构建完整URL
                full_url = self.base_url + item['href']

                # 访问菜单项
                await self.page.goto(full_url)
                await self.page.wait_for_load_state('networkidle')
                await asyncio.sleep(2)

                # 截图
                screenshot_name = f"sidebar_{i+1:02d}_{self.sanitize_filename(item['text'])}.png"
                await self.page.screenshot(path=self.screenshots_dir / screenshot_name)

                # 记录页面信息
                page_info = {
                    'menu_item': item['text'],
                    'url': self.page.url,
                    'title': await self.page.title(),
                    'screenshot': screenshot_name,
                    'timestamp': datetime.now().isoformat(),
                    'menu_type': 'sidebar'
                }

                self.function_details.append(page_info)

                # 查找子功能
                await self.explore_sub_functions(item['text'])

            except Exception as e:
                print(f"探索侧边栏菜单项 {item['text']} 时出错: {e}")
                continue

    async def explore_main_menu_items(self, menu_data):
        """探索主要菜单项"""
        print("开始探索主要菜单项...")

        # 过滤出有效的菜单项
        valid_items = [item for item in menu_data if item['href'] and not item['href'].startswith('#')]

        for i, item in enumerate(valid_items[:10]):  # 限制探索前10个主要菜单
            try:
                print(f"正在探索菜单项: {item['text']}")

                # 构建完整URL
                if item['href'].startswith('http'):
                    full_url = item['href']
                elif item['href'].startswith('/'):
                    full_url = self.base_url + item['href']
                else:
                    full_url = self.base_url + '/' + item['href']

                # 访问菜单项
                await self.page.goto(full_url)
                await self.page.wait_for_load_state('networkidle')
                await asyncio.sleep(2)

                # 截图
                screenshot_name = f"menu_{i+1:02d}_{self.sanitize_filename(item['text'])}.png"
                await self.page.screenshot(path=self.screenshots_dir / screenshot_name)

                # 记录页面信息
                page_info = {
                    'menu_item': item['text'],
                    'url': self.page.url,
                    'title': await self.page.title(),
                    'screenshot': screenshot_name,
                    'timestamp': datetime.now().isoformat(),
                    'menu_type': item.get('type', 'main')
                }

                self.function_details.append(page_info)

                # 查找子菜单或功能按钮
                await self.explore_sub_functions(item['text'])

            except Exception as e:
                print(f"探索菜单项 {item['text']} 时出错: {e}")
                continue

    async def explore_sub_functions(self, parent_menu):
        """探索子功能"""
        try:
            # 查找可能的子菜单或功能按钮
            sub_elements = await self.page.query_selector_all(
                'a, button, .btn, .tab, .menu-item, .function-item'
            )

            sub_functions = []
            for element in sub_elements:
                try:
                    text = await element.inner_text()
                    if text and text.strip() and len(text.strip()) < 50:
                        sub_functions.append(text.strip())
                except:
                    continue

            # 去重并过滤
            unique_functions = list(set(sub_functions))
            relevant_functions = [f for f in unique_functions if len(f) > 1 and not f.isdigit()]

            if relevant_functions:
                print(f"  在 {parent_menu} 中找到子功能: {relevant_functions[:10]}")

        except Exception as e:
            print(f"探索 {parent_menu} 的子功能时出错: {e}")

    def sanitize_filename(self, filename):
        """清理文件名"""
        import re
        # 移除或替换不安全的字符
        filename = re.sub(r'[<>:"/\\|?*]', '_', filename)
        filename = filename.replace(' ', '_')
        return filename[:50]  # 限制长度
    
    async def close(self):
        """关闭浏览器"""
        if self.browser:
            await self.browser.close()
    
    async def run_exploration(self):
        """运行完整的探索流程"""
        try:
            await self.start_browser()

            login_success = await self.login()
            if not login_success:
                print("登录失败，无法继续探索")
                return False

            await self.explore_menu_structure()

            # 保存功能详情数据
            if self.function_details:
                with open(self.data_dir / "function_details.json", 'w', encoding='utf-8') as f:
                    json.dump(self.function_details, f, ensure_ascii=False, indent=2)
                print(f"已保存 {len(self.function_details)} 个功能详情")

            print("完整探索完成")
            return True

        except Exception as e:
            print(f"探索过程中出现错误: {e}")
            return False
        finally:
            await self.close()

async def main():
    """主函数"""
    explorer = Boss3SystemExplorer()
    success = await explorer.run_exploration()
    
    if success:
        print("系统访问验证成功！")
    else:
        print("系统访问验证失败！")

if __name__ == "__main__":
    asyncio.run(main())
