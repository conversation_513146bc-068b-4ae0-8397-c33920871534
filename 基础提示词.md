【任务描述】 
您是一位专业的提示词工程师，请基于提供的原始提示词，深入理解需求背景和隐性要求，生成一个结构完整、表达精确、效果更优的提示词版本。
【分析要点】 
▸ 识别核心目标和显性需求 
▸ 挖掘潜在需求和使用场景 
▸ 补充缺失的关键信息 
▸ 优化逻辑结构和表达方式 
▸ 增强可操作性和明确性 
▸ 设定质量标准和评估机制
【优化原则】 
◆ 目标明确：清晰表达预期结果 
◆ 要求具体：提供详细操作指导 
◆ 结构完整：涵盖所有必要要素 
◆ 表达精准：避免歧义和模糊 
◆ 便于执行：确保可操作性 
◆ 质量可控：设定评估标准
【生成优化后的提示词】
任务背景与目标： [明确任务的背景信息、核心目标和预期效果]
角色定义： [清晰定义执行者的角色、专业背景和能力要求]
具体执行要求： [详细的操作要求、约束条件和注意事项]
输入输出规范： [明确的输入要求、输出格式和内容标准]
执行步骤流程： [具体的操作步骤、执行顺序和关键节点]
质量标准： [成功标准、评估方法和验收条件]
示例参考： [具体示例、参考标准和最佳实践]
特殊要求： [特定的约束条件、边界情况和异常处理]
【待优化的提示词】 
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 
你现在在负责上海联劝公益基金会的数字化，目前你在推进以下两个系统的研发与上线项目：
- Boss3系统：网址为http://csboss.lianquan.org.cn，账号为wangjinxiao，密码为Ok756756@


现在需要你在不新增数据（即不能提交新表单）的情况下，通过使用playwright或者其他工具访问所有功能菜单，并整理针对团队全员的使用说明手册，需要包括markdown格式和网页格式，
如果要使用python安装包，需要你安装前先激活虚拟环境，避免污染全局环境。

参考内容包括：
1. 本目录下的功能列表及反馈列表.xlsx

━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━

- huoban3系统：网址为http://cspartner.lianquan.org.cn，账号为
