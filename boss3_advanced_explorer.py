#!/usr/bin/env python3
"""
Boss3系统高级功能探索脚本
通过点击菜单项来探索系统功能
"""

import asyncio
import json
import re
from datetime import datetime
from pathlib import Path
from playwright.async_api import async_playwright

class Boss3AdvancedExplorer:
    def __init__(self):
        self.base_url = "http://csboss.lianquan.org.cn"
        self.username = "wangjin<PERSON>o"
        self.password = "Ok756756@"
        self.screenshots_dir = Path("screenshots")
        self.data_dir = Path("data")
        self.browser = None
        self.page = None
        self.explored_functions = []
        
        # 创建必要的目录
        self.screenshots_dir.mkdir(exist_ok=True)
        self.data_dir.mkdir(exist_ok=True)
    
    async def start_browser(self):
        """启动浏览器"""
        playwright = await async_playwright().start()
        self.browser = await playwright.chromium.launch(
            headless=False,
            args=['--no-sandbox', '--disable-dev-shm-usage']
        )
        context = await self.browser.new_context(
            viewport={'width': 1920, 'height': 1080}
        )
        self.page = await context.new_page()
        
    async def login(self):
        """登录系统"""
        print(f"正在访问登录页面: {self.base_url}")
        await self.page.goto(self.base_url)
        await self.page.wait_for_load_state('networkidle')
        
        # 填写用户名和密码
        await self.page.fill('input[name="username"]', self.username)
        await self.page.fill('input[name="password"]', self.password)
        
        # 点击登录按钮
        login_button = await self.page.query_selector('button:has-text("登 录")')
        if login_button:
            await login_button.click()
        
        await self.page.wait_for_load_state('networkidle')
        await asyncio.sleep(3)
        
        print(f"登录后URL: {self.page.url}")
        return True
    
    async def explore_menu_by_clicking(self):
        """通过点击来探索菜单"""
        print("开始通过点击探索菜单...")
        
        # 截取初始状态
        await self.page.screenshot(path=self.screenshots_dir / "00_initial_state.png")
        
        # 定义要探索的主菜单项
        main_menu_items = [
            "数据看板", "收入管理", "资金池管理", "预算决算", "物资管理", 
            "支出管理", "业务管理", "资助管理", "项目管理", "开票管理",
            "合作方管理", "合同管理", "捐方管理", "财务统计", "用友管理",
            "可变配置", "安全审计", "组织管理"
        ]
        
        for i, menu_item in enumerate(main_menu_items):
            try:
                await self.explore_main_menu_item(menu_item, i + 1)
            except Exception as e:
                print(f"探索菜单项 {menu_item} 时出错: {e}")
                continue
    
    async def explore_main_menu_item(self, menu_name, index):
        """探索单个主菜单项"""
        print(f"\n=== 探索主菜单项: {menu_name} ===")
        
        try:
            # 查找并点击主菜单项
            menu_selector = f'.el-submenu__title:has-text("{menu_name}")'
            menu_element = await self.page.query_selector(menu_selector)
            
            if not menu_element:
                # 尝试其他选择器
                menu_element = await self.page.query_selector(f'text="{menu_name}"')
            
            if menu_element:
                print(f"找到菜单项: {menu_name}")
                await menu_element.click()
                await asyncio.sleep(2)
                
                # 截取展开后的菜单
                screenshot_name = f"main_menu_{index:02d}_{self.sanitize_filename(menu_name)}_expanded.png"
                await self.page.screenshot(path=self.screenshots_dir / screenshot_name)
                
                # 查找子菜单项
                await self.explore_sub_menu_items(menu_name, index)
                
            else:
                print(f"未找到菜单项: {menu_name}")
                
        except Exception as e:
            print(f"探索主菜单项 {menu_name} 时出错: {e}")
    
    async def explore_sub_menu_items(self, parent_menu, parent_index):
        """探索子菜单项"""
        print(f"探索 {parent_menu} 的子菜单...")
        
        # 等待子菜单展开
        await asyncio.sleep(1)
        
        # 查找子菜单项
        sub_menu_selectors = [
            '.el-menu--inline .el-menu-item',
            '.el-menu-item',
            '.submenu-item'
        ]
        
        sub_items = []
        for selector in sub_menu_selectors:
            try:
                elements = await self.page.query_selector_all(selector)
                for element in elements:
                    text = await element.inner_text()
                    if text and text.strip() and len(text.strip()) > 1:
                        sub_items.append((element, text.strip()))
            except:
                continue
        
        # 去重
        unique_sub_items = []
        seen_texts = set()
        for element, text in sub_items:
            if text not in seen_texts:
                unique_sub_items.append((element, text))
                seen_texts.add(text)
        
        print(f"找到 {len(unique_sub_items)} 个子菜单项")
        
        # 点击每个子菜单项
        for i, (element, text) in enumerate(unique_sub_items[:10]):  # 限制前10个
            try:
                await self.explore_sub_menu_item(element, text, parent_menu, parent_index, i + 1)
            except Exception as e:
                print(f"探索子菜单项 {text} 时出错: {e}")
                continue
    
    async def explore_sub_menu_item(self, element, item_name, parent_menu, parent_index, sub_index):
        """探索单个子菜单项"""
        print(f"  点击子菜单项: {item_name}")
        
        try:
            # 点击子菜单项
            await element.click()
            await self.page.wait_for_load_state('networkidle')
            await asyncio.sleep(3)
            
            # 截图
            screenshot_name = f"sub_menu_{parent_index:02d}_{sub_index:02d}_{self.sanitize_filename(parent_menu)}_{self.sanitize_filename(item_name)}.png"
            await self.page.screenshot(path=self.screenshots_dir / screenshot_name)
            
            # 记录功能信息
            function_info = {
                'parent_menu': parent_menu,
                'sub_menu': item_name,
                'url': self.page.url,
                'title': await self.page.title(),
                'screenshot': screenshot_name,
                'timestamp': datetime.now().isoformat()
            }
            
            self.explored_functions.append(function_info)
            
            # 分析页面内容
            await self.analyze_page_content(function_info)
            
        except Exception as e:
            print(f"  探索子菜单项 {item_name} 时出错: {e}")
    
    async def analyze_page_content(self, function_info):
        """分析页面内容"""
        try:
            # 查找页面中的关键元素
            buttons = await self.page.query_selector_all('button, .btn, input[type="button"]')
            tables = await self.page.query_selector_all('table, .table')
            forms = await self.page.query_selector_all('form')
            
            content_analysis = {
                'buttons_count': len(buttons),
                'tables_count': len(tables),
                'forms_count': len(forms),
                'has_search': bool(await self.page.query_selector('input[type="search"], .search')),
                'has_pagination': bool(await self.page.query_selector('.pagination, .page')),
            }
            
            # 获取按钮文本
            button_texts = []
            for button in buttons[:10]:  # 只获取前10个按钮
                try:
                    text = await button.inner_text()
                    if text and text.strip():
                        button_texts.append(text.strip())
                except:
                    continue
            
            content_analysis['button_texts'] = button_texts
            function_info['content_analysis'] = content_analysis
            
        except Exception as e:
            print(f"  分析页面内容时出错: {e}")
    
    def sanitize_filename(self, filename):
        """清理文件名"""
        filename = re.sub(r'[<>:"/\\|?*]', '_', filename)
        filename = filename.replace(' ', '_')
        return filename[:30]  # 限制长度
    
    async def close(self):
        """关闭浏览器"""
        if self.browser:
            await self.browser.close()
    
    async def run_exploration(self):
        """运行完整的探索流程"""
        try:
            await self.start_browser()
            
            login_success = await self.login()
            if not login_success:
                print("登录失败，无法继续探索")
                return False
            
            await self.explore_menu_by_clicking()
            
            # 保存探索结果
            with open(self.data_dir / "explored_functions.json", 'w', encoding='utf-8') as f:
                json.dump(self.explored_functions, f, ensure_ascii=False, indent=2)
            
            print(f"\n探索完成！共探索了 {len(self.explored_functions)} 个功能")
            return True
            
        except Exception as e:
            print(f"探索过程中出现错误: {e}")
            return False
        finally:
            await self.close()

async def main():
    """主函数"""
    explorer = Boss3AdvancedExplorer()
    success = await explorer.run_exploration()
    
    if success:
        print("系统功能探索成功！")
    else:
        print("系统功能探索失败！")

if __name__ == "__main__":
    asyncio.run(main())
