#!/usr/bin/env python3
"""
Boss3系统使用手册项目质量检查和总结
"""

import json
from pathlib import Path
from datetime import datetime

class QualityChecker:
    def __init__(self):
        self.data_dir = Path("data")
        self.screenshots_dir = Path("screenshots")
        self.manual_dir = Path("manual")
        self.html_dir = Path("html_manual")
        self.analysis_dir = Path("analysis")
        
    def check_file_completeness(self):
        """检查文件完整性"""
        print("=== 文件完整性检查 ===")
        
        required_files = {
            "数据文件": [
                self.data_dir / "comprehensive_exploration_results.json",
                self.data_dir / "exploration_report.json",
                self.analysis_dir / "function_catalog.json",
                self.analysis_dir / "excel_analysis.json"
            ],
            "手册文件": [
                self.manual_dir / "Boss3系统使用手册.md",
                self.html_dir / "index.html"
            ],
            "截图目录": [
                self.screenshots_dir,
                self.html_dir / "screenshots"
            ]
        }
        
        completeness_report = {}
        
        for category, files in required_files.items():
            missing_files = []
            existing_files = []
            
            for file_path in files:
                if file_path.exists():
                    existing_files.append(file_path.name)
                else:
                    missing_files.append(file_path.name)
            
            completeness_report[category] = {
                "existing": existing_files,
                "missing": missing_files,
                "completeness_rate": len(existing_files) / len(files) * 100
            }
            
            print(f"\n{category}:")
            print(f"  ✅ 存在: {len(existing_files)} 个文件")
            print(f"  ❌ 缺失: {len(missing_files)} 个文件")
            print(f"  📊 完整率: {completeness_report[category]['completeness_rate']:.1f}%")
            
            if missing_files:
                print(f"  缺失文件: {', '.join(missing_files)}")
        
        return completeness_report
    
    def check_exploration_quality(self):
        """检查探索质量"""
        print("\n=== 探索质量检查 ===")
        
        try:
            with open(self.data_dir / "exploration_report.json", 'r', encoding='utf-8') as f:
                report = json.load(f)
            
            with open(self.data_dir / "comprehensive_exploration_results.json", 'r', encoding='utf-8') as f:
                results = json.load(f)
        except Exception as e:
            print(f"无法加载探索数据: {e}")
            return {}
        
        quality_metrics = {
            "主菜单覆盖率": report.get('successful_main_menus', 0) / report.get('total_main_menus', 18) * 100,
            "子功能数量": len(results),
            "截图数量": len(list(self.screenshots_dir.glob("*.png"))),
            "探索时间": report.get('exploration_time', '未知'),
            "失败尝试": report.get('failed_attempts_count', 0)
        }
        
        print(f"主菜单覆盖率: {quality_metrics['主菜单覆盖率']:.1f}%")
        print(f"成功探索子功能: {quality_metrics['子功能数量']} 个")
        print(f"生成截图数量: {quality_metrics['截图数量']} 张")
        print(f"失败尝试次数: {quality_metrics['失败尝试']} 次")
        
        # 质量评级
        if quality_metrics['主菜单覆盖率'] >= 90:
            quality_grade = "优秀"
        elif quality_metrics['主菜单覆盖率'] >= 70:
            quality_grade = "良好"
        elif quality_metrics['主菜单覆盖率'] >= 50:
            quality_grade = "中等"
        else:
            quality_grade = "需改进"
        
        print(f"探索质量评级: {quality_grade}")
        
        return quality_metrics
    
    def check_manual_quality(self):
        """检查手册质量"""
        print("\n=== 手册质量检查 ===")
        
        manual_metrics = {}
        
        # 检查Markdown手册
        md_file = self.manual_dir / "Boss3系统使用手册.md"
        if md_file.exists():
            with open(md_file, 'r', encoding='utf-8') as f:
                md_content = f.read()
            
            manual_metrics['markdown'] = {
                "文件大小": len(md_content),
                "行数": len(md_content.split('\n')),
                "包含图片": md_content.count('!['),
                "包含链接": md_content.count('['),
                "章节数": md_content.count('##')
            }
            
            print("Markdown手册:")
            print(f"  📄 文件大小: {manual_metrics['markdown']['文件大小']} 字符")
            print(f"  📝 总行数: {manual_metrics['markdown']['行数']} 行")
            print(f"  🖼️ 图片数量: {manual_metrics['markdown']['包含图片']} 张")
            print(f"  📑 章节数量: {manual_metrics['markdown']['章节数']} 个")
        
        # 检查HTML手册
        html_file = self.html_dir / "index.html"
        if html_file.exists():
            with open(html_file, 'r', encoding='utf-8') as f:
                html_content = f.read()
            
            manual_metrics['html'] = {
                "文件大小": len(html_content),
                "包含CSS": 'style>' in html_content,
                "包含JavaScript": 'script>' in html_content,
                "响应式设计": '@media' in html_content,
                "导航菜单": 'nav-menu' in html_content
            }
            
            print("\nHTML手册:")
            print(f"  📄 文件大小: {manual_metrics['html']['文件大小']} 字符")
            print(f"  🎨 包含CSS: {'✅' if manual_metrics['html']['包含CSS'] else '❌'}")
            print(f"  ⚡ 包含JavaScript: {'✅' if manual_metrics['html']['包含JavaScript'] else '❌'}")
            print(f"  📱 响应式设计: {'✅' if manual_metrics['html']['响应式设计'] else '❌'}")
            print(f"  🧭 导航菜单: {'✅' if manual_metrics['html']['导航菜单'] else '❌'}")
        
        return manual_metrics
    
    def generate_project_summary(self):
        """生成项目总结"""
        print("\n=== 项目总结 ===")
        
        summary = {
            "项目名称": "Boss3业务管理系统使用手册制作",
            "委托方": "上海联劝公益基金会",
            "完成时间": datetime.now().isoformat(),
            "项目状态": "已完成",
            "交付物": []
        }
        
        # 统计交付物
        deliverables = [
            ("Markdown使用手册", self.manual_dir / "Boss3系统使用手册.md"),
            ("HTML网页手册", self.html_dir / "index.html"),
            ("系统功能截图", self.screenshots_dir),
            ("探索数据报告", self.data_dir / "exploration_report.json"),
            ("功能分析数据", self.analysis_dir / "function_catalog.json")
        ]
        
        for name, path in deliverables:
            if path.exists():
                if path.is_file():
                    size = path.stat().st_size
                    summary["交付物"].append(f"{name} ({size} 字节)")
                else:
                    count = len(list(path.glob("*")))
                    summary["交付物"].append(f"{name} ({count} 个文件)")
        
        print(f"项目名称: {summary['项目名称']}")
        print(f"委托方: {summary['委托方']}")
        print(f"完成时间: {summary['完成时间'][:10]}")
        print(f"项目状态: {summary['项目状态']}")
        print("\n交付物清单:")
        for deliverable in summary["交付物"]:
            print(f"  ✅ {deliverable}")
        
        return summary
    
    def generate_recommendations(self):
        """生成改进建议"""
        print("\n=== 改进建议 ===")
        
        recommendations = [
            "1. 功能覆盖率提升",
            "   - 建议继续完善自动化探索脚本，提高菜单点击成功率",
            "   - 针对未探索的功能模块进行手动补充",
            "   - 定期更新手册内容，确保与系统功能同步",
            "",
            "2. 用户体验优化",
            "   - 添加更多实际操作示例和最佳实践",
            "   - 收集用户反馈，持续改进手册内容",
            "   - 考虑制作视频教程补充文字说明",
            "",
            "3. 维护机制建立",
            "   - 建立定期更新机制，确保手册时效性",
            "   - 指定专人负责手册维护和更新",
            "   - 建立用户反馈收集和处理流程",
            "",
            "4. 技术改进",
            "   - 优化HTML手册的搜索功能",
            "   - 添加打印友好的CSS样式",
            "   - 考虑生成PDF版本供离线使用"
        ]
        
        for recommendation in recommendations:
            print(recommendation)
        
        return recommendations
    
    def run_quality_check(self):
        """运行完整的质量检查"""
        print("🔍 开始Boss3系统使用手册项目质量检查...")
        print("=" * 60)
        
        # 执行各项检查
        completeness = self.check_file_completeness()
        exploration_quality = self.check_exploration_quality()
        manual_quality = self.check_manual_quality()
        project_summary = self.generate_project_summary()
        recommendations = self.generate_recommendations()
        
        # 生成最终报告
        final_report = {
            "检查时间": datetime.now().isoformat(),
            "文件完整性": completeness,
            "探索质量": exploration_quality,
            "手册质量": manual_quality,
            "项目总结": project_summary,
            "改进建议": recommendations
        }
        
        # 保存报告
        with open("质量检查报告.json", 'w', encoding='utf-8') as f:
            json.dump(final_report, f, ensure_ascii=False, indent=2)
        
        print("\n" + "=" * 60)
        print("🎉 质量检查完成！报告已保存为 '质量检查报告.json'")
        
        return final_report

def main():
    """主函数"""
    checker = QualityChecker()
    checker.run_quality_check()

if __name__ == "__main__":
    main()
