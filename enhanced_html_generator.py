#!/usr/bin/env python3
"""
增强版HTML网页使用手册生成器
使用增强版探索数据生成完整的HTML手册
"""

import json
from pathlib import Path
from datetime import datetime

class EnhancedHTMLGenerator:
    def __init__(self):
        self.data_dir = Path("data")
        self.screenshots_dir = Path("screenshots")
        self.html_dir = Path("html_manual")
        self.html_dir.mkdir(exist_ok=True)
        
        # 加载增强版探索数据
        self.load_enhanced_data()
    
    def load_enhanced_data(self):
        """加载增强版探索数据"""
        try:
            with open(self.data_dir / "enhanced_exploration_results.json", 'r', encoding='utf-8') as f:
                self.exploration_results = json.load(f)
            
            with open(self.data_dir / "enhanced_exploration_report.json", 'r', encoding='utf-8') as f:
                self.exploration_report = json.load(f)
                
            print(f"增强版数据加载成功: {len(self.exploration_results)} 个功能")
        except Exception as e:
            print(f"加载增强版数据时出错: {e}")
            self.exploration_results = []
            self.exploration_report = {}
    
    def create_enhanced_html(self):
        """创建增强版HTML"""
        # 按主菜单分组
        menu_groups = {}
        for result in self.exploration_results:
            parent = result['parent_menu']
            if parent not in menu_groups:
                menu_groups[parent] = []
            menu_groups[parent].append(result)
        
        # 生成导航
        nav_html = self.generate_navigation(menu_groups)
        
        # 生成主要内容
        main_content = self.generate_main_content(menu_groups)
        
        # 生成完整HTML
        html_content = f"""<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Boss3业务管理系统使用手册 - 增强版</title>
    <style>
        * {{
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }}
        
        body {{
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', sans-serif;
            line-height: 1.6;
            color: #333;
            background-color: #f5f5f5;
        }}
        
        .container {{
            display: flex;
            min-height: 100vh;
        }}
        
        .sidebar {{
            width: 300px;
            background: #2c3e50;
            color: white;
            padding: 20px;
            position: fixed;
            height: 100vh;
            overflow-y: auto;
            z-index: 1000;
        }}
        
        .sidebar h1 {{
            font-size: 18px;
            margin-bottom: 20px;
            padding-bottom: 10px;
            border-bottom: 1px solid #34495e;
        }}
        
        .nav-menu {{
            list-style: none;
        }}
        
        .nav-menu li {{
            margin-bottom: 5px;
        }}
        
        .nav-menu a {{
            color: #ecf0f1;
            text-decoration: none;
            display: block;
            padding: 8px 12px;
            border-radius: 4px;
            transition: background-color 0.3s;
        }}
        
        .nav-menu a:hover {{
            background-color: #34495e;
        }}
        
        .nav-submenu {{
            list-style: none;
            margin-left: 15px;
            margin-top: 5px;
        }}
        
        .nav-submenu a {{
            font-size: 14px;
            color: #bdc3c7;
        }}
        
        .main-content {{
            margin-left: 300px;
            flex: 1;
            padding: 20px;
            background: white;
            min-height: 100vh;
        }}
        
        .header {{
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            margin: -20px -20px 30px -20px;
            border-radius: 0 0 10px 10px;
        }}
        
        .header h1 {{
            font-size: 28px;
            margin-bottom: 10px;
        }}
        
        .stats-grid {{
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }}
        
        .stat-card {{
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            border-radius: 8px;
            text-align: center;
        }}
        
        .stat-number {{
            font-size: 32px;
            font-weight: bold;
            margin-bottom: 5px;
        }}
        
        .stat-label {{
            font-size: 14px;
            opacity: 0.9;
        }}
        
        .section {{
            margin-bottom: 40px;
            padding: 20px;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }}
        
        .section h2 {{
            color: #2c3e50;
            font-size: 24px;
            margin-bottom: 20px;
            padding-bottom: 10px;
            border-bottom: 2px solid #3498db;
        }}
        
        .function-card {{
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 6px;
            padding: 15px;
            margin: 15px 0;
        }}
        
        .function-card h4 {{
            color: #495057;
            margin-bottom: 10px;
        }}
        
        .function-meta {{
            display: flex;
            gap: 15px;
            margin-bottom: 10px;
            font-size: 14px;
            color: #6c757d;
            flex-wrap: wrap;
        }}
        
        .function-features {{
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
            margin: 10px 0;
        }}
        
        .feature-tag {{
            background: #e3f2fd;
            color: #1976d2;
            padding: 2px 8px;
            border-radius: 12px;
            font-size: 12px;
        }}
        
        .screenshot {{
            max-width: 100%;
            height: auto;
            border: 1px solid #ddd;
            border-radius: 4px;
            margin: 15px 0;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }}
        
        .search-box {{
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            margin-bottom: 20px;
            font-size: 14px;
        }}
        
        @media (max-width: 768px) {{
            .sidebar {{
                transform: translateX(-100%);
                transition: transform 0.3s;
            }}
            
            .main-content {{
                margin-left: 0;
            }}
            
            .stats-grid {{
                grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            }}
            
            .function-meta {{
                flex-direction: column;
                gap: 5px;
            }}
        }}
    </style>
</head>
<body>
    <div class="container">
        <nav class="sidebar">
            <h1>Boss3使用手册</h1>
            <input type="text" class="search-box" placeholder="搜索功能..." onkeyup="searchFunction(this.value)">
            <ul class="nav-menu">
                {nav_html}
            </ul>
        </nav>
        
        <main class="main-content">
            {main_content}
        </main>
    </div>
    
    <script>
        function searchFunction(query) {{
            const navItems = document.querySelectorAll('.nav-menu a');
            navItems.forEach(item => {{
                const text = item.textContent.toLowerCase();
                const parent = item.parentElement;
                if (text.includes(query.toLowerCase()) || query === '') {{
                    parent.style.display = 'block';
                }} else {{
                    parent.style.display = 'none';
                }}
            }});
        }}
        
        function scrollToSection(sectionId) {{
            document.getElementById(sectionId).scrollIntoView({{
                behavior: 'smooth'
            }});
        }}
    </script>
</body>
</html>"""
        
        return html_content

    def generate_navigation(self, menu_groups):
        """生成导航菜单"""
        nav_html = '<li><a href="#overview">系统概览</a></li>\n'
        nav_html += '<li><a href="#statistics">探索统计</a></li>\n'
        nav_html += '<li><a href="#functions">功能详情</a>\n<ul class="nav-submenu">\n'

        for menu_name in sorted(menu_groups.keys()):
            nav_html += f'<li><a href="#menu-{menu_name}">{menu_name} ({len(menu_groups[menu_name])})</a></li>\n'

        nav_html += '</ul></li>\n'
        return nav_html

    def generate_main_content(self, menu_groups):
        """生成主要内容"""
        total_functions = len(self.exploration_results)
        total_menus = len(menu_groups)
        total_screenshots = len(list(self.screenshots_dir.glob("enhanced_*.png")))

        content = f"""
        <div class="header">
            <h1>Boss3业务管理系统使用手册</h1>
            <p>上海联劝公益基金会 | 增强版 | 生成时间: {datetime.now().strftime('%Y年%m月%d日')}</p>
        </div>

        <section id="overview" class="section">
            <h2>系统概览</h2>

            <div class="stats-grid">
                <div class="stat-card">
                    <div class="stat-number">{total_menus}</div>
                    <div class="stat-label">主功能模块</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">{total_functions}</div>
                    <div class="stat-label">子功能数量</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">{total_screenshots}</div>
                    <div class="stat-label">功能截图</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">100%</div>
                    <div class="stat-label">模块覆盖率</div>
                </div>
            </div>

            <p>Boss3业务管理系统是上海联劝公益基金会的核心业务管理平台，本手册基于自动化探索技术生成，涵盖了系统的所有主要功能模块。</p>
        </section>

        <section id="statistics" class="section">
            <h2>探索统计</h2>
            <ul>
                <li>探索方法: 增强版自动化探索</li>
                <li>探索时间: {self.exploration_report.get('exploration_time', '未知')[:10]}</li>
                <li>成功率: {self.exploration_report.get('success_metrics', {}).get('main_menu_success_rate', 0) * 100:.1f}%</li>
                <li>平均每模块子功能: {self.exploration_report.get('success_metrics', {}).get('average_sub_functions_per_menu', 0):.1f}个</li>
            </ul>
        </section>

        <section id="functions" class="section">
            <h2>功能模块详情</h2>
            <p>以下是通过增强版自动化探索收集的所有功能模块详细信息：</p>
        """

        # 为每个主菜单生成内容
        for menu_name in sorted(menu_groups.keys()):
            functions = menu_groups[menu_name]
            content += f"""
            <h3 id="menu-{menu_name}">{menu_name}</h3>
            <p>该模块包含 {len(functions)} 个已探索的子功能：</p>
            """

            for func in functions:
                content += self.generate_function_card(func)

        content += "</section>"
        return content

    def generate_function_card(self, function_data):
        """生成功能卡片"""
        content_analysis = function_data.get('content_analysis', {})
        elements = content_analysis.get('elements', {})
        features = content_analysis.get('features', {})

        # 生成功能特性标签
        feature_tags = []
        if features.get('has_search'):
            feature_tags.append("搜索")
        if features.get('has_pagination'):
            feature_tags.append("分页")
        if features.get('has_add_button'):
            feature_tags.append("新增")
        if features.get('has_export_button'):
            feature_tags.append("导出")
        if features.get('has_edit_button'):
            feature_tags.append("编辑")
        if features.get('has_delete_button'):
            feature_tags.append("删除")
        if features.get('has_filter'):
            feature_tags.append("筛选")
        if features.get('has_tabs'):
            feature_tags.append("标签页")

        features_html = "".join([f'<span class="feature-tag">{tag}</span>' for tag in feature_tags])

        # 确保截图路径正确
        screenshot_path = function_data['screenshot']
        if not screenshot_path.startswith('screenshots/'):
            screenshot_path = f"screenshots/{screenshot_path}"

        return f"""
        <div class="function-card">
            <h4>{function_data['sub_menu']}</h4>
            <div class="function-meta">
                <span>📅 {function_data['timestamp'][:10]}</span>
                <span>🔗 <a href="{function_data['url']}" target="_blank">访问链接</a></span>
                <span>📊 {elements.get('tables_count', 0)} 个表格</span>
                <span>🔘 {elements.get('buttons_count', 0)} 个按钮</span>
                <span>📝 {elements.get('forms_count', 0)} 个表单</span>
                <span>⌨️ {elements.get('inputs_count', 0)} 个输入框</span>
            </div>
            <div class="function-features">
                {features_html}
            </div>
            <img src="{screenshot_path}" alt="{function_data['sub_menu']}截图" class="screenshot">
        </div>
        """

    def copy_screenshots(self):
        """复制截图到HTML目录"""
        import shutil

        html_screenshots_dir = self.html_dir / "screenshots"
        html_screenshots_dir.mkdir(exist_ok=True)

        copied_count = 0
        for screenshot in self.screenshots_dir.glob("*.png"):
            try:
                shutil.copy2(screenshot, html_screenshots_dir / screenshot.name)
                copied_count += 1
            except Exception as e:
                print(f"复制截图 {screenshot.name} 时出错: {e}")

        print(f"已复制 {copied_count} 张截图到HTML目录")

    def generate_enhanced_manual(self):
        """生成增强版手册"""
        try:
            # 生成HTML内容
            html_content = self.create_enhanced_html()

            # 保存HTML文件
            html_file = self.html_dir / "enhanced_index.html"
            with open(html_file, 'w', encoding='utf-8') as f:
                f.write(html_content)

            # 复制截图
            self.copy_screenshots()

            print(f"增强版HTML手册已生成: {html_file}")
            return html_file

        except Exception as e:
            print(f"生成增强版手册时出错: {e}")
            return None

def main():
    """主函数"""
    generator = EnhancedHTMLGenerator()
    success = generator.generate_enhanced_manual()

    if success:
        print("增强版HTML使用手册生成成功！")
    else:
        print("增强版HTML使用手册生成失败！")

if __name__ == "__main__":
    main()
