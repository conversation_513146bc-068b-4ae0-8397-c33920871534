#!/usr/bin/env python3
"""
Boss3系统使用手册生成器
基于收集的信息生成结构化的Markdown格式使用手册
"""

import json
from pathlib import Path
from datetime import datetime

class ManualGenerator:
    def __init__(self):
        self.data_dir = Path("data")
        self.analysis_dir = Path("analysis")
        self.screenshots_dir = Path("screenshots")
        self.output_dir = Path("manual")
        self.output_dir.mkdir(exist_ok=True)
        
        # 加载分析数据
        self.load_analysis_data()
    
    def load_analysis_data(self):
        """加载分析数据"""
        try:
            with open(self.analysis_dir / "function_catalog.json", 'r', encoding='utf-8') as f:
                self.function_catalog = json.load(f)
            
            with open(self.analysis_dir / "excel_analysis.json", 'r', encoding='utf-8') as f:
                self.excel_analysis = json.load(f)
                
            print("分析数据加载成功")
        except Exception as e:
            print(f"加载分析数据时出错: {e}")
            self.function_catalog = {}
            self.excel_analysis = {}
    
    def generate_main_manual(self):
        """生成主要使用手册"""
        print("生成主要使用手册...")
        
        manual_content = self.create_manual_header()
        manual_content += self.create_table_of_contents()
        manual_content += self.create_system_overview()
        manual_content += self.create_login_guide()
        manual_content += self.create_function_modules()
        manual_content += self.create_appendix()
        
        # 保存手册
        with open(self.output_dir / "Boss3系统使用手册.md", 'w', encoding='utf-8') as f:
            f.write(manual_content)
        
        print("主要使用手册生成完成")
    
    def create_manual_header(self):
        """创建手册头部"""
        return f"""# Boss3业务管理系统使用手册

**上海联劝公益基金会**

---

## 文档信息

- **系统名称**: Boss3业务管理系统
- **系统地址**: http://csboss.lianquan.org.cn
- **文档版本**: v1.0
- **生成日期**: {datetime.now().strftime('%Y年%m月%d日')}
- **适用对象**: 联劝基金会全体工作人员

---

## 文档说明

本手册旨在帮助联劝基金会工作人员快速掌握Boss3业务管理系统的使用方法，提高工作效率，确保操作规范性。

### 使用说明
- 本手册按功能模块组织，每个模块包含功能概述、操作步骤和注意事项
- 建议新用户先阅读"系统概览"和"登录指南"部分
- 可根据工作需要查阅相应的功能模块说明
- 遇到问题时可参考"常见问题"部分或联系系统管理员

---

"""
    
    def create_table_of_contents(self):
        """创建目录"""
        toc = """## 目录

1. [系统概览](#系统概览)
2. [登录指南](#登录指南)
3. [功能模块](#功能模块)
"""
        
        # 添加功能模块目录
        for i, (module_name, module_data) in enumerate(self.function_catalog.get('function_catalog', {}).items(), 4):
            toc += f"   {i}. [{module_name}](#{module_name})\n"
        
        toc += """4. [常见问题](#常见问题)
5. [附录](#附录)

---

"""
        return toc
    
    def create_system_overview(self):
        """创建系统概览"""
        total_menus = self.function_catalog.get('system_overview', {}).get('total_main_menus', 0)
        total_functions = self.function_catalog.get('system_overview', {}).get('total_sub_functions', 0)
        
        return f"""## 系统概览

Boss3业务管理系统是上海联劝公益基金会的核心业务管理平台，涵盖了基金会日常运营的各个方面。

### 系统特点
- **功能全面**: 包含{total_menus}个主要功能模块，{total_functions}个子功能
- **操作便捷**: 基于Web的用户界面，支持多种浏览器
- **权限管理**: 完善的用户权限控制，确保数据安全
- **实时更新**: 数据实时同步，支持多用户协同工作

### 主要功能模块

| 模块名称 | 主要功能 | 使用频率 |
|---------|---------|---------|
| 数据看板 | 数据概览和统计分析 | 高 |
| 收入管理 | 捐赠收入的认领和管理 | 高 |
| 支出管理 | 各类支出的申请和管理 | 高 |
| 项目管理 | 项目的全生命周期管理 | 中 |
| 财务统计 | 财务报表和统计分析 | 中 |
| 组织管理 | 用户和权限管理 | 低 |

![系统主界面](screenshots/03_main_interface.png)

---

"""
    
    def create_login_guide(self):
        """创建登录指南"""
        return """## 登录指南

### 访问系统

1. 打开浏览器，访问系统地址：http://csboss.lianquan.org.cn
2. 系统支持Chrome、Firefox、Safari等主流浏览器

![登录页面](screenshots/01_login_page.png)

### 登录步骤

1. **输入用户名**: 在用户名输入框中输入您的账号
2. **输入密码**: 在密码输入框中输入您的密码
3. **点击登录**: 点击"登录"按钮进入系统

![登录后界面](screenshots/02_after_login.png)

### 注意事项

- 请妥善保管您的账号密码，不要与他人共享
- 如果忘记密码，请联系系统管理员重置
- 建议定期更改密码，确保账户安全
- 使用完毕后请及时退出登录

---

"""
    
    def create_function_modules(self):
        """创建功能模块说明"""
        content = "## 功能模块\n\n"
        
        for module_name, module_data in self.function_catalog.get('function_catalog', {}).items():
            content += self.create_module_section(module_name, module_data)
        
        return content
    
    def create_module_section(self, module_name, module_data):
        """创建单个模块的说明"""
        section = f"### {module_name}\n\n"
        
        # 模块概述
        section += self.get_module_description(module_name)
        
        # 子功能列表
        sub_functions = module_data.get('sub_functions', [])
        if sub_functions:
            section += "#### 主要功能\n\n"
            for func in sub_functions:
                section += f"- **{func}**: {self.get_function_description(module_name, func)}\n"
            section += "\n"
        
        # Excel详细信息
        excel_details = module_data.get('excel_details', [])
        if excel_details:
            section += "#### 详细功能说明\n\n"
            for detail in excel_details[:5]:  # 只显示前5个
                section += f"**{detail.get('sub_function', '')}**\n"
                section += f"- 功能描述: {detail.get('description', '暂无描述')}\n"
                section += f"- 优先级: {detail.get('priority', '未设置')}\n"
                section += f"- 状态: {detail.get('status', '未知')}\n\n"
        
        # 操作指南
        section += self.create_operation_guide(module_name)
        
        # 截图
        screenshot_path = self.find_module_screenshot(module_name)
        if screenshot_path:
            section += f"![{module_name}界面]({screenshot_path})\n\n"
        
        section += "---\n\n"
        return section
    
    def get_module_description(self, module_name):
        """获取模块描述"""
        descriptions = {
            "数据看板": "提供系统整体数据概览，包括收入、支出、项目等关键指标的实时展示。",
            "收入管理": "管理各种渠道的捐赠收入，包括认领、审核、入账等流程。",
            "资金池管理": "管理基金会的资金池，包括资金分配、调整和清理等操作。",
            "预算决算": "处理项目和机构的预算制定和决算管理。",
            "物资管理": "管理基金会的物资库存，包括入库、出库、盘点等操作。",
            "支出管理": "处理各类支出申请，包括项目支出、行政支出、报销等。",
            "业务管理": "管理基金会的核心业务流程和相关数据。",
            "资助管理": "管理对外资助项目的申请、审批和执行。",
            "项目管理": "管理基金会执行的各类项目，包括立项、执行、结项等全流程。",
            "开票管理": "管理捐赠票据的开具、查询和核销等操作。",
            "合作方管理": "管理与基金会合作的各类机构和个人信息。",
            "合同管理": "管理基金会签署的各类合同文档。",
            "捐方管理": "管理捐赠人和捐赠机构的基本信息。",
            "财务统计": "提供各类财务报表和统计分析功能。",
            "用友管理": "与用友财务系统的数据对接和管理。",
            "可变配置": "系统的各类配置管理，包括表单、标签等。",
            "安全审计": "系统操作日志和安全审计功能。",
            "组织管理": "管理组织架构、用户角色和权限配置。"
        }
        
        description = descriptions.get(module_name, "该模块的详细功能说明待补充。")
        return f"{description}\n\n"
    
    def get_function_description(self, module_name, function_name):
        """获取功能描述"""
        # 这里可以根据需要添加更详细的功能描述
        return "具体功能说明请参考操作指南"
    
    def create_operation_guide(self, module_name):
        """创建操作指南"""
        return f"""#### 操作指南

1. **访问{module_name}**
   - 在左侧菜单栏找到"{module_name}"
   - 点击展开子菜单
   - 选择需要使用的具体功能

2. **基本操作**
   - 查看数据：进入功能页面后可查看相关数据列表
   - 搜索筛选：使用页面上方的搜索和筛选功能
   - 新增数据：点击"新增"或"添加"按钮
   - 编辑数据：点击数据行的"编辑"按钮
   - 删除数据：选择数据后点击"删除"按钮

3. **注意事项**
   - 请确保您有相应的操作权限
   - 重要操作前请仔细确认
   - 如有疑问请联系相关负责人

"""
    
    def find_module_screenshot(self, module_name):
        """查找模块截图"""
        # 查找相关截图文件
        screenshot_files = list(self.screenshots_dir.glob("*.png"))
        
        for file in screenshot_files:
            if module_name in file.name:
                return f"screenshots/{file.name}"
        
        return None
    
    def create_appendix(self):
        """创建附录"""
        return """## 常见问题

### Q: 忘记密码怎么办？
A: 请联系系统管理员重置密码。

### Q: 页面加载缓慢怎么办？
A: 请检查网络连接，或尝试刷新页面。如问题持续存在，请联系技术支持。

### Q: 没有某个功能的操作权限怎么办？
A: 请联系您的直属领导或系统管理员申请相应权限。

### Q: 数据保存失败怎么办？
A: 请检查必填字段是否完整，数据格式是否正确。如问题持续存在，请联系技术支持。

---

## 附录

### 系统技术信息
- **系统架构**: Web应用系统
- **支持浏览器**: Chrome 80+, Firefox 75+, Safari 13+
- **建议分辨率**: 1920x1080或更高

### 联系方式
- **技术支持**: 请联系IT部门
- **业务咨询**: 请联系相关业务负责人
- **系统管理员**: 王金晓

### 更新记录
- v1.0 (2025-06-30): 初始版本发布

---

*本手册由系统自动生成，如有疑问请联系相关负责人。*
"""
    
    def run_generation(self):
        """运行手册生成"""
        try:
            self.generate_main_manual()
            print(f"使用手册已生成到: {self.output_dir}/Boss3系统使用手册.md")
            return True
        except Exception as e:
            print(f"生成手册时出错: {e}")
            return False

def main():
    """主函数"""
    generator = ManualGenerator()
    success = generator.run_generation()
    
    if success:
        print("Markdown使用手册生成成功！")
    else:
        print("Markdown使用手册生成失败！")

if __name__ == "__main__":
    main()
