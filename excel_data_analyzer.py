#!/usr/bin/env python3
"""
Excel数据分析脚本
分析功能列表及反馈列表.xlsx中的业务逻辑，与实际系统功能对应
"""

import pandas as pd
import json
from pathlib import Path
from datetime import datetime

class ExcelDataAnalyzer:
    def __init__(self):
        self.excel_file = "功能列表及反馈列表.xlsx"
        self.data_dir = Path("data")
        self.output_dir = Path("analysis")
        self.output_dir.mkdir(exist_ok=True)
        
        # 系统功能结构（基于我们的探索结果）
        self.system_structure = {
            "数据看板": [],
            "收入管理": [
                "认领公告", "认领审核", "渠道收入", "账单明细", "未入账清单",
                "筹款产品", "慈善备案", "订单查询", "支出订单", "统计报表", "配置管理"
            ],
            "资金池管理": [
                "资金池", "收入调整", "隐藏资金池", "资金池清理"
            ],
            "预算决算": [
                "收入", "支出", "导入记录"
            ],
            "物资管理": [
                "库存管理", "仓管单据", "物品管理", "仓库管理"
            ],
            "支出管理": [
                "备用金", "外部请款", "项目报销", "行政报销", "支出调整",
                "支出退款", "捐赠退款", "商家退款", "银企直连", "票据管理",
                "票据催办", "支付汇总", "票据备份", "项目分摊", "报销票据管理"
            ],
            "业务管理": [],
            "资助管理": [],
            "项目管理": [],
            "开票管理": [
                "票据看板", "票据开具", "票据查询", "订单核销",
                "核销失败", "备份下载", "票据接口"
            ],
            "合作方管理": [],
            "合同管理": [],
            "捐方管理": [],
            "财务统计": [
                "月末关账", "月收入报表", "月收入结转", "业务收支汇总", "业务收支明细"
            ],
            "用友管理": [
                "会计科目", "辅助核算", "凭证确认", "凭证管理"
            ],
            "可变配置": [
                "动态表单", "动态标签", "合同模板"
            ],
            "安全审计": [
                "操作日志", "错误日志"
            ],
            "组织管理": [
                "组织架构", "角色配置", "资金权限", "更新记录"
            ]
        }
    
    def analyze_excel_data(self):
        """分析Excel数据"""
        print("开始分析Excel数据...")
        
        # 读取所有工作表
        excel_data = pd.read_excel(self.excel_file, sheet_name=None)
        
        analysis_results = {}
        
        for sheet_name, df in excel_data.items():
            print(f"\n分析工作表: {sheet_name}")
            sheet_analysis = self.analyze_sheet(df, sheet_name)
            analysis_results[sheet_name] = sheet_analysis
        
        return analysis_results
    
    def analyze_sheet(self, df, sheet_name):
        """分析单个工作表"""
        analysis = {
            "sheet_name": sheet_name,
            "total_rows": len(df),
            "columns": list(df.columns),
            "summary": {}
        }
        
        if sheet_name == "后台管理测试反馈计划":
            analysis["summary"] = self.analyze_boss3_functions(df)
        elif sheet_name == "伙伴系统测试反馈计划":
            analysis["summary"] = self.analyze_partner_functions(df)
        elif sheet_name == "联调反馈收集":
            analysis["summary"] = self.analyze_feedback_data(df)
        
        return analysis
    
    def analyze_boss3_functions(self, df):
        """分析Boss3系统功能"""
        print("  分析Boss3系统功能...")
        
        # 统计功能主菜单
        main_menus = df['功能主菜单'].value_counts().to_dict()
        
        # 统计子功能
        sub_functions = df['功能子菜单/子功能'].value_counts().to_dict()
        
        # 统计优先级
        priorities = df['优先级'].value_counts().to_dict()
        
        # 功能详细描述分析
        function_details = []
        for _, row in df.iterrows():
            if pd.notna(row['功能主菜单']) and pd.notna(row['功能子菜单/子功能']):
                function_details.append({
                    'main_menu': row['功能主菜单'],
                    'sub_function': row['功能子菜单/子功能'],
                    'description': row['功能详细描述'] if pd.notna(row['功能详细描述']) else '',
                    'priority': row['优先级'] if pd.notna(row['优先级']) else '',
                    'status': row['完成状态'] if pd.notna(row['完成状态']) else ''
                })
        
        return {
            'main_menus': main_menus,
            'sub_functions': sub_functions,
            'priorities': priorities,
            'function_details': function_details,
            'total_functions': len(function_details)
        }
    
    def analyze_partner_functions(self, df):
        """分析伙伴系统功能"""
        print("  分析伙伴系统功能...")
        
        # 统计功能主菜单
        main_menus = df['功能主菜单'].value_counts().to_dict()
        
        # 功能详细描述分析
        function_details = []
        for _, row in df.iterrows():
            if pd.notna(row['功能主菜单']):
                function_details.append({
                    'main_menu': row['功能主菜单'],
                    'sub_function': row['功能子菜单/子功能'] if pd.notna(row['功能子菜单/子功能']) else '',
                    'description': row['功能详细描述'] if pd.notna(row['功能详细描述']) else '',
                    'status': row['完成状态'] if pd.notna(row['完成状态']) else ''
                })
        
        return {
            'main_menus': main_menus,
            'function_details': function_details,
            'total_functions': len(function_details)
        }
    
    def analyze_feedback_data(self, df):
        """分析反馈数据"""
        print("  分析反馈数据...")
        
        # 统计系统分布
        systems = df['系统所属'].value_counts().to_dict()
        
        # 统计状态分布
        statuses = df['状态'].value_counts().to_dict()
        
        # 统计功能主菜单
        main_menus = df['功能主菜单'].value_counts().to_dict()
        
        # 反馈详情
        feedback_details = []
        for _, row in df.iterrows():
            if pd.notna(row['功能主菜单']):
                feedback_details.append({
                    'system': row['系统所属'] if pd.notna(row['系统所属']) else '',
                    'main_menu': row['功能主菜单'],
                    'sub_menu': row['功能子菜单'] if pd.notna(row['功能子菜单']) else '',
                    'feedback_item': row['反馈项'] if pd.notna(row['反馈项']) else '',
                    'description': row['反馈详细描述'] if pd.notna(row['反馈详细描述']) else '',
                    'status': row['状态'] if pd.notna(row['状态']) else '',
                    'date': row['录入日期'] if pd.notna(row['录入日期']) else ''
                })
        
        return {
            'systems': systems,
            'statuses': statuses,
            'main_menus': main_menus,
            'feedback_details': feedback_details,
            'total_feedback': len(feedback_details)
        }
    
    def map_excel_to_system(self, analysis_results):
        """将Excel数据映射到系统结构"""
        print("\n开始映射Excel数据到系统结构...")
        
        mapping_results = {
            'boss3_mapping': {},
            'coverage_analysis': {},
            'missing_functions': [],
            'extra_functions': []
        }
        
        # 获取Boss3功能数据
        boss3_data = analysis_results.get('后台管理测试反馈计划', {}).get('summary', {})
        boss3_functions = boss3_data.get('function_details', [])
        
        # 创建映射
        for function in boss3_functions:
            main_menu = function['main_menu']
            sub_function = function['sub_function']
            
            if main_menu not in mapping_results['boss3_mapping']:
                mapping_results['boss3_mapping'][main_menu] = []
            
            mapping_results['boss3_mapping'][main_menu].append({
                'sub_function': sub_function,
                'description': function['description'],
                'priority': function['priority'],
                'status': function['status']
            })
        
        # 分析覆盖率
        for system_menu, system_subs in self.system_structure.items():
            excel_subs = [item['sub_function'] for item in mapping_results['boss3_mapping'].get(system_menu, [])]
            
            coverage = {
                'system_functions': len(system_subs),
                'excel_functions': len(excel_subs),
                'matched_functions': len(set(system_subs) & set(excel_subs)),
                'coverage_rate': 0
            }
            
            if coverage['system_functions'] > 0:
                coverage['coverage_rate'] = coverage['matched_functions'] / coverage['system_functions']
            
            mapping_results['coverage_analysis'][system_menu] = coverage
        
        return mapping_results
    
    def generate_function_catalog(self, analysis_results, mapping_results):
        """生成功能目录"""
        print("\n生成功能目录...")
        
        catalog = {
            'generation_time': datetime.now().isoformat(),
            'system_overview': {
                'total_main_menus': len(self.system_structure),
                'total_sub_functions': sum(len(subs) for subs in self.system_structure.values())
            },
            'function_catalog': {}
        }
        
        for main_menu, sub_functions in self.system_structure.items():
            excel_data = mapping_results['boss3_mapping'].get(main_menu, [])
            
            catalog['function_catalog'][main_menu] = {
                'sub_functions': sub_functions,
                'excel_details': excel_data,
                'function_count': len(sub_functions),
                'has_excel_data': len(excel_data) > 0
            }
        
        return catalog
    
    def save_analysis_results(self, analysis_results, mapping_results, catalog):
        """保存分析结果"""
        print("\n保存分析结果...")
        
        # 保存Excel分析结果
        with open(self.output_dir / "excel_analysis.json", 'w', encoding='utf-8') as f:
            json.dump(analysis_results, f, ensure_ascii=False, indent=2, default=str)
        
        # 保存映射结果
        with open(self.output_dir / "mapping_results.json", 'w', encoding='utf-8') as f:
            json.dump(mapping_results, f, ensure_ascii=False, indent=2, default=str)
        
        # 保存功能目录
        with open(self.output_dir / "function_catalog.json", 'w', encoding='utf-8') as f:
            json.dump(catalog, f, ensure_ascii=False, indent=2, default=str)
        
        print(f"分析结果已保存到 {self.output_dir} 目录")
    
    def run_analysis(self):
        """运行完整分析"""
        try:
            # 分析Excel数据
            analysis_results = self.analyze_excel_data()
            
            # 映射到系统结构
            mapping_results = self.map_excel_to_system(analysis_results)
            
            # 生成功能目录
            catalog = self.generate_function_catalog(analysis_results, mapping_results)
            
            # 保存结果
            self.save_analysis_results(analysis_results, mapping_results, catalog)
            
            # 打印摘要
            self.print_summary(analysis_results, mapping_results)
            
            return True
            
        except Exception as e:
            print(f"分析过程中出现错误: {e}")
            return False
    
    def print_summary(self, analysis_results, mapping_results):
        """打印分析摘要"""
        print("\n=== 分析摘要 ===")
        
        for sheet_name, data in analysis_results.items():
            print(f"\n{sheet_name}:")
            print(f"  总行数: {data['total_rows']}")
            if 'summary' in data and 'total_functions' in data['summary']:
                print(f"  功能数量: {data['summary']['total_functions']}")
        
        print(f"\n系统结构覆盖率:")
        for menu, coverage in mapping_results['coverage_analysis'].items():
            rate = coverage['coverage_rate'] * 100
            print(f"  {menu}: {rate:.1f}% ({coverage['matched_functions']}/{coverage['system_functions']})")

def main():
    """主函数"""
    analyzer = ExcelDataAnalyzer()
    success = analyzer.run_analysis()
    
    if success:
        print("\nExcel数据分析完成！")
    else:
        print("\nExcel数据分析失败！")

if __name__ == "__main__":
    main()
